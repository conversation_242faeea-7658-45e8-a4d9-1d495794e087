"use client"

import React, { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { MessageSquare, Plus } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { useRouter } from "next/navigation"
import { toast } from "sonner"
import { createChannel } from "@/actions/chatActions"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"

interface Channel {
  id: string
  name: string
  description?: string
  role: string
  isCreator: boolean
}

export function ChatSection({ initialChannels }: { initialChannels: Channel[] }) {
  const router = useRouter()
  const [newChannelName, setNewChannelName] = useState("")
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [channels] = useState<Channel[]>(initialChannels)

  const handleCreateChannel = async (e: React.FormEvent) => {
    e.preventDefault()
    if (newChannelName.trim() === "") {
      toast.error("Please enter a channel name")
      return
    }

    toast.promise(
      createChannel(newChannelName),
      {
        loading: 'Creating channel...',
        success: (result) => {
          if (result.success) {
            setNewChannelName("")
            setIsDialogOpen(false)
            if (result.channel?.id) {
              router.push(`/hr/workspace/chats/${result.channel.id}`)
            }
            return `Channel "${newChannelName}" created successfully!`
          } else {
            throw new Error(result.error || "Failed to create channel")
          }
        },
        error: "Failed to create channel. Please try again."
      }
    )
  }

  return (
    <Card className="relative border-border/50">
      <div className="absolute top-0 right-0 w-full h-1 bg-gradient-to-l from-purple-500/20 via-primary/20 to-transparent" />
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-xl">Team Chat</CardTitle>
            <CardDescription>Active channels</CardDescription>
          </div>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm" variant="outline">
                <Plus className="w-4 h-4 mr-2" />
                Channel
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create a New Chat Channel</DialogTitle>
              </DialogHeader>
              <form onSubmit={handleCreateChannel} className="space-y-4">
                <Input
                  placeholder="Enter channel name"
                  value={newChannelName}
                  onChange={(e) => setNewChannelName(e.target.value)}
                />
                <Button type="submit" className="w-full">Create Channel</Button>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[400px] pr-4">
          <div className="space-y-2">
            {channels.length > 0 ? (
              channels.map((channel) => (
                <Button
                  key={channel.id}
                  variant="ghost"
                  className="w-full justify-start text-left group hover:bg-primary/5"
                  onClick={() => router.push(`/hr/workspace/chats/${channel.id}`)}
                >
                  <MessageSquare className="w-4 h-4 mr-2 text-primary" />
                  <span className="flex-1">{channel.name}</span>
                  {channel.isCreator && (
                    <Badge variant="outline" className="ml-2">Owner</Badge>
                  )}
                </Button>
              ))
            ) : (
              <p className="text-sm text-muted-foreground text-center py-4">
                No channels available. Create one to get started!
              </p>
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  )
}

// Also add a default export to ensure compatibility with old imports
export default ChatSection