'use client'

import React from 'react'
import { useRouter } from 'next/navigation'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  FolderOpen,
  Database,
  RefreshCw,
  Plus,
  ChevronDown,
  Code2,
  FileText,
  Brain,
  PanelRightOpen,
  PanelRightClose,
  Keyboard
} from "lucide-react"
import { toast } from "sonner"
import { WorkspaceSelector } from './WorkspaceSelector'
import { NotebookExporter } from './NotebookExporter'
import { Workspace, AddCellFunction, ImportFunction } from './types/workspace'
import { KeyboardShortcutsHelp } from './KeyboardShortcutsHelp'

interface ChartBuilderHeaderProps {
  // Workspace props
  currentWorkspace?: Workspace | null
  onWorkspaceChange: (workspace: Workspace) => void
  onSaveToWorkspace: () => void
  isSaving?: boolean

  // Tab props
  activeTab: string

  // Dataset props
  datasetCache: Record<string, any>
  isLoadingDatasets: boolean
  refreshDatasets: () => Promise<void>

  // Connection props
  isOnline: boolean
  isCheckingConnection: boolean
  checkConnectivity: () => void

  // Notebook props
  cells: any[]
  savedCharts: any[]
  onImport: ImportFunction
  onAddCell: AddCellFunction

  // Copilot props
  isCopilotCollapsed?: boolean
  onToggleCopilot?: () => void

  // Kernel props
  onResetKernel?: () => void
}

export function ChartBuilderHeader({
  currentWorkspace,
  onWorkspaceChange,
  onSaveToWorkspace,
  isSaving,
  activeTab,
  datasetCache,
  isLoadingDatasets,
  refreshDatasets,
  isOnline,
  isCheckingConnection,
  checkConnectivity,
  cells,
  savedCharts,
  onImport,
  onAddCell,
  isCopilotCollapsed,
  onToggleCopilot,
  onResetKernel
}: ChartBuilderHeaderProps) {
  const router = useRouter()

  return (
    <div className="flex items-center justify-between mb-2 px-2 py-2 bg-background/95 backdrop-blur-sm border-b border-border/40 sticky top-0 z-10 overflow-hidden">
      {/* Left Section */}
      <div className="flex items-center gap-1 flex-1 min-w-0 overflow-hidden">
        {/* Workspaces Button - More compact */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.push('/hr/chartbuilder/workspaces')}
          className="h-6 text-xs shrink-0 px-1.5"
          title="View all data workspaces"
        >
          <FolderOpen className="h-3 w-3" />
          <span className="hidden md:inline ml-1">Workspaces</span>
        </Button>

        {/* Title - More compact */}
        <h1 className="text-sm font-semibold truncate min-w-0 max-w-[120px] hidden sm:block">
          {currentWorkspace ? currentWorkspace.name : 'Data Analytics'}
        </h1>

        {/* Tabs - More compact */}
        <TabsList className="h-6 shrink-0">
          <TabsTrigger value="notebook" className="text-xs px-1.5 h-6">
            <span className="hidden md:inline">Notebook</span>
            <span className="md:hidden">NB</span>
          </TabsTrigger>
          <TabsTrigger value="dashboard" className="text-xs px-1.5 h-6">
            <span className="hidden md:inline">Dashboard</span>
            <span className="md:hidden">DB</span>
          </TabsTrigger>
          <TabsTrigger value="pipeline" className="text-xs px-1.5 h-6">
            <span className="hidden md:inline">Data Pipeline</span>
            <span className="md:hidden">DP</span>
          </TabsTrigger>
        </TabsList>

        {/* Dataset Info - Only show on larger screens */}
        {Object.keys(datasetCache).length > 0 && activeTab === "notebook" && (
          <div className="hidden xl:flex text-xs text-muted-foreground items-center gap-1 shrink-0">
            <Database className="h-3 w-3" />
            <span>{Object.keys(datasetCache).length}</span>
          </div>
        )}
      </div>

      {/* Center Section - Status & Actions - More compact */}
      <div className="hidden lg:flex items-center gap-1 mx-1 shrink-0">
        {/* Connection Status */}
        <div className="flex items-center gap-0.5">
          {isOnline ? (
            <div className="flex items-center gap-1 text-xs text-green-600 dark:text-green-400">
              <span className="h-1.5 w-1.5 rounded-full bg-green-500"></span>
              <span className="hidden xl:inline text-xs">Online</span>
            </div>
          ) : (
            <div className="flex items-center gap-1 text-xs text-yellow-600 dark:text-yellow-400">
              <span className="h-1.5 w-1.5 rounded-full bg-yellow-500 animate-pulse"></span>
              <span className="hidden xl:inline text-xs">Offline</span>
            </div>
          )}

          {/* Refresh Buttons */}
          <Button
            size="sm"
            variant="ghost"
            className="h-5 w-5 p-0"
            onClick={checkConnectivity}
            disabled={isCheckingConnection}
            title="Check server connection"
          >
            <RefreshCw className={`h-2.5 w-2.5 ${isCheckingConnection ? 'animate-spin' : ''}`} />
          </Button>

          <Button
            size="sm"
            variant="ghost"
            className="h-5 w-5 p-0"
            onClick={async () => {
              try {
                await refreshDatasets();
                toast.success('Datasets refreshed');
              } catch (error) {
                toast.error('Failed to refresh datasets');
              }
            }}
            disabled={isLoadingDatasets}
            title="Refresh datasets"
          >
            <Database className={`h-2.5 w-2.5 ${isLoadingDatasets ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </div>

      {/* Right Section - More compact */}
      <div className="flex items-center gap-0.5 shrink-0 overflow-hidden">
        {/* Data Copilot Toggle */}
        {onToggleCopilot && (
          <Button
            onClick={onToggleCopilot}
            variant="ghost"
            size="sm"
            className="h-7 px-2 text-xs hover:bg-muted transition-all duration-200"
            title={isCopilotCollapsed ? "Open Data Copilot" : "Close Data Copilot"}
          >
            <Brain className="h-3 w-3 mr-1" />
            <span className="hidden lg:inline">Copilot</span>
            {isCopilotCollapsed ? (
              <PanelRightOpen className="h-3 w-3 ml-1" />
            ) : (
              <PanelRightClose className="h-3 w-3 ml-1" />
            )}
          </Button>
        )}

        {/* Notebook Exporter - Hide on smaller screens */}
        <div className="hidden 2xl:block">
          <NotebookExporter
            cells={cells}
            savedCharts={savedCharts}
            selectedDatasets={Object.values(datasetCache)}
            onImport={onImport}
          />
        </div>

        {/* Workspace Selector */}
        <WorkspaceSelector
          currentWorkspace={currentWorkspace}
          onWorkspaceChange={onWorkspaceChange}
          onSaveToWorkspace={onSaveToWorkspace}
          isSaving={isSaving}
        />

        {/* Add Cell Button - Only for notebook tab, more compact */}
        {activeTab === "notebook" && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                size="sm"
                className="h-6 text-xs px-1.5"
                title="Add new cell"
              >
                <Plus className="h-3 w-3" />
                <span className="hidden lg:inline ml-1">Add</span>
                <ChevronDown className="h-3 w-3 ml-0.5" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onAddCell(undefined, 'code')}>
                <Code2 className="h-4 w-4 mr-2" />
                Code Cell
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onAddCell(undefined, 'markdown')}>
                <FileText className="h-4 w-4 mr-2" />
                Markdown Cell
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}

        {/* Reset Kernel Button - Only for notebook tab with Python cells */}
        {activeTab === "notebook" && cells.some(cell => cell.language === 'python') && onResetKernel && (
          <Button
            size="sm"
            variant="outline"
            className="h-6 text-xs px-1.5"
            title="Reset Python kernel (clears all variables)"
            onClick={onResetKernel}
          >
            <RefreshCw className="h-3 w-3" />
            <span className="hidden lg:inline ml-1">Reset Kernel</span>
          </Button>
        )}

        {/* Keyboard Shortcuts Help - Only for notebook tab */}
        {activeTab === "notebook" && (
          <KeyboardShortcutsHelp
            trigger={
              <Button
                size="sm"
                variant="outline"
                className="h-6 text-xs px-1.5"
                title="View keyboard shortcuts"
              >
                <Keyboard className="h-3 w-3" />
                <span className="hidden lg:inline ml-1">Shortcuts</span>
              </Button>
            }
          />
        )}

        {/* Mobile Menu for hidden items */}
        <div className="2xl:hidden">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                size="sm"
                variant="ghost"
                className="h-6 w-6 p-0"
                title="More options"
              >
                <ChevronDown className="h-3 w-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => {/* Handle notebook export */}}>
                Export Notebook
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => {/* Handle notebook import */}}>
                Import Notebook
              </DropdownMenuItem>
              {Object.keys(datasetCache).length > 0 && (
                <DropdownMenuItem disabled>
                  <Database className="h-4 w-4 mr-2" />
                  {Object.keys(datasetCache).length} dataset(s) loaded
                </DropdownMenuItem>
              )}
              {/* Add connection status and refresh options for mobile */}
              <DropdownMenuItem onClick={checkConnectivity} disabled={isCheckingConnection}>
                <RefreshCw className={`h-4 w-4 mr-2 ${isCheckingConnection ? 'animate-spin' : ''}`} />
                Check Connection
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={async () => {
                  try {
                    await refreshDatasets();
                    toast.success('Datasets refreshed');
                  } catch (error) {
                    toast.error('Failed to refresh datasets');
                  }
                }}
                disabled={isLoadingDatasets}
              >
                <Database className={`h-4 w-4 mr-2 ${isLoadingDatasets ? 'animate-spin' : ''}`} />
                Refresh Datasets
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </div>
  )
}
