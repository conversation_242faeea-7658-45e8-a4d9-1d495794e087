// Model mappings for easier reference
export const TOGETHER_MODELS = {
  'mistral-7b': 'mistralai/Mistral-7B-Instruct-v0.2',
  'mixtral-8x7b': 'mistralai/Mixtral-8x7B-Instruct-v0.1',
  'llama-2-70b': 'meta-llama/Llama-2-70b-chat-hf',
  'llama-2-13b': 'meta-llama/Llama-2-13b-chat-hf',
  'llama-2-7b': 'meta-llama/Llama-2-7b-chat-hf',
  'command-r': 'togethercomputer/CommandR-Plus',
  'command-r-plus': 'togethercomputer/CommandR-Plus',
};

export const COHERE_MODELS = {
  'command': 'command',
  'command-light': 'command-light',
  'command-r': 'command-r',
  'command-r-plus': 'command-r-plus',
  'command-nightly': 'command-nightly',
};

// Helper functions for model resolution
export const getModelProvider = (model: string): 'together' | 'cohere' => {
  if (model in TOGETHER_MODELS) {
    return 'together';
  }
  return 'cohere';
};

export const getModelId = (model: string, provider: 'together' | 'cohere'): string => {
  if (provider === 'together' && model in TOGETHER_MODELS) {
    return TOGETHER_MODELS[model as keyof typeof TOGETHER_MODELS];
  } else if (provider === 'cohere' && model in COHERE_MODELS) {
    return COHERE_MODELS[model as keyof typeof COHERE_MODELS];
  }
  
  // Default fallbacks
  return provider === 'together' ? TOGETHER_MODELS['command-r-plus'] : COHERE_MODELS['command-r-plus'];
};
