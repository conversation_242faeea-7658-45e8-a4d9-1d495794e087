"""
Jupyter-like kernel backend for code execution
Provides a clean interface similar to Jupyter notebooks
"""

import json
import sys
import io
import traceback
import base64
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from io import BytesIO
import warnings
import contextlib

class JupyterKernel:
    """
    A Jupyter-like kernel for executing Python code with proper output handling
    """
    
    def __init__(self):
        self.namespace = {}
        self.execution_count = 0
        self.setup_namespace()
    
    def setup_namespace(self):
        """Setup the execution namespace with common imports and utilities"""
        # Core libraries
        self.namespace.update({
            'pd': pd,
            'np': np,
            'plt': plt,
            'json': json,
            'sys': sys,
            'io': io,
            'warnings': warnings,
            # Add other common imports
            'os': __import__('os'),
            'datetime': __import__('datetime'),
            're': __import__('re'),
            'math': __import__('math'),
            'random': __import__('random'),
        })
        
        # Setup matplotlib for non-interactive backend
        plt.switch_backend('Agg')
        
    def capture_plots(self) -> List[str]:
        """Capture all matplotlib plots as base64 images"""
        plots = []
        
        # Get all figure numbers
        fig_nums = plt.get_fignums()
        
        for fig_num in fig_nums:
            try:
                fig = plt.figure(fig_num)
                
                # Save figure to buffer
                buffer = BytesIO()
                fig.savefig(buffer, format='png', dpi=100, bbox_inches='tight')
                buffer.seek(0)
                
                # Encode as base64
                image_data = buffer.getvalue()
                if len(image_data) > 0:
                    encoded_image = base64.b64encode(image_data).decode()
                    plots.append(f"data:image/png;base64,{encoded_image}")
                
                buffer.close()
            except Exception as e:
                print(f"Error capturing plot {fig_num}: {e}")
        
        # Close all figures
        plt.close('all')
        
        return plots
    
    def format_dataframe_html(self, df: pd.DataFrame, max_rows: int = 100) -> str:
        """Format DataFrame as HTML table"""
        if len(df) > max_rows:
            # Show first and last rows with truncation indicator
            top_rows = df.head(max_rows // 2)
            bottom_rows = df.tail(max_rows // 2)
            
            html_parts = []
            html_parts.append(top_rows.to_html(classes='dataframe', table_id='dataframe'))
            html_parts.append(f'<div class="truncation-indicator">... {len(df) - max_rows} more rows ...</div>')
            html_parts.append(bottom_rows.to_html(classes='dataframe', table_id='dataframe'))
            
            return ''.join(html_parts)
        else:
            return df.to_html(classes='dataframe', table_id='dataframe')
    
    def execute_code(self, code: str, datasets: Optional[List[Dict]] = None) -> Dict[str, Any]:
        """
        Execute Python code and return Jupyter-like results
        
        Returns:
        {
            'execution_count': int,
            'status': 'ok' | 'error',
            'stdout': str,
            'stderr': str,
            'result': Any,  # Last expression result
            'plots': List[str],  # Base64 encoded images
            'html_outputs': List[str],  # HTML outputs (tables, etc.)
            'error': Optional[Dict],  # Error information
            'variables': Dict[str, Any],  # Updated namespace variables
        }
        """
        
        self.execution_count += 1
        
        # Add datasets to namespace
        if datasets:
            for i, dataset in enumerate(datasets):
                if dataset and 'data' in dataset:
                    df_data = pd.DataFrame(dataset['data'])
                    dataset_name = dataset.get('name', f'Dataset {i+1}')
                    
                    # Create safe variable name
                    safe_name = dataset_name.lower().replace(' ', '_').replace('-', '_')
                    safe_name = ''.join(c for c in safe_name if c.isalnum() or c == '_')
                    if not safe_name or safe_name[0].isdigit():
                        safe_name = f'dataset_{safe_name}' if safe_name else f'dataset_{i+1}'
                    
                    # Store datasets with multiple names for flexibility
                    self.namespace[safe_name] = df_data
                    self.namespace[f'df{i+1}'] = df_data
                    
                    if i == 0:
                        self.namespace['df'] = df_data
        
        # Capture stdout and stderr
        stdout_capture = io.StringIO()
        stderr_capture = io.StringIO()
        
        result = None
        error_info = None
        html_outputs = []
        
        try:
            # Redirect stdout and stderr
            with contextlib.redirect_stdout(stdout_capture), \
                 contextlib.redirect_stderr(stderr_capture):
                
                # Split code into statements
                code_lines = code.strip().split('\n')
                
                # Execute all but the last statement
                if len(code_lines) > 1:
                    exec_code = '\n'.join(code_lines[:-1])
                    if exec_code.strip():
                        exec(exec_code, self.namespace)
                
                # Evaluate the last statement to capture result
                last_line = code_lines[-1].strip() if code_lines else ''
                if last_line:
                    try:
                        # Try to evaluate as expression first
                        result = eval(last_line, self.namespace)
                        
                        # Handle special result types
                        if isinstance(result, pd.DataFrame):
                            html_outputs.append(self.format_dataframe_html(result))
                        elif hasattr(result, '_repr_html_'):
                            html_outputs.append(result._repr_html_())
                            
                    except SyntaxError:
                        # If it's not an expression, execute as statement
                        exec(last_line, self.namespace)
                        result = None
        
        except Exception as e:
            error_info = {
                'ename': type(e).__name__,
                'evalue': str(e),
                'traceback': traceback.format_exc().split('\n')
            }
        
        # Capture plots
        plots = self.capture_plots()
        
        # Get stdout and stderr
        stdout_text = stdout_capture.getvalue()
        stderr_text = stderr_capture.getvalue()
        
        # Extract user-defined variables (exclude built-ins and modules)
        user_variables = {}
        for name, value in self.namespace.items():
            if (not name.startswith('_') and 
                not callable(value) and 
                not hasattr(value, '__module__') and
                name not in ['pd', 'np', 'plt', 'json', 'sys', 'io', 'warnings', 'os', 'datetime', 're', 'math', 'random']):
                try:
                    # Only include serializable variables
                    if isinstance(value, (int, float, str, bool, list, dict)):
                        user_variables[name] = value
                    elif isinstance(value, pd.DataFrame):
                        user_variables[name] = f"DataFrame({value.shape[0]} rows, {value.shape[1]} cols)"
                    elif isinstance(value, np.ndarray):
                        user_variables[name] = f"Array{value.shape}"
                    else:
                        user_variables[name] = str(type(value).__name__)
                except:
                    user_variables[name] = "Object"
        
        return {
            'execution_count': self.execution_count,
            'status': 'error' if error_info else 'ok',
            'stdout': stdout_text,
            'stderr': stderr_text,
            'result': result,
            'plots': plots,
            'html_outputs': html_outputs,
            'error': error_info,
            'variables': user_variables,
            'data': result.to_dict('records') if isinstance(result, pd.DataFrame) else None
        }
    
    def get_namespace_info(self) -> Dict[str, Any]:
        """Get information about current namespace variables"""
        variables = {}
        for name, value in self.namespace.items():
            if not name.startswith('_') and not callable(value):
                variables[name] = {
                    'type': type(value).__name__,
                    'value': str(value) if len(str(value)) < 100 else f"{str(value)[:100]}...",
                    'shape': getattr(value, 'shape', None)
                }
        return variables
    
    def reset_namespace(self):
        """Reset the execution namespace"""
        self.namespace.clear()
        self.execution_count = 0
        self.setup_namespace()

# Global kernel instance
_kernel_instance = None

def get_kernel() -> JupyterKernel:
    """Get or create the global kernel instance"""
    global _kernel_instance
    if _kernel_instance is None:
        _kernel_instance = JupyterKernel()
    return _kernel_instance

def reset_kernel():
    """Reset the global kernel instance"""
    global _kernel_instance
    if _kernel_instance:
        _kernel_instance.reset_namespace()
    else:
        _kernel_instance = JupyterKernel()
