'use client'

import { useState, useRef, useEffect, useCallback } from 'react';
import { SavedChart } from './types';
import { Grip, Maximize2, Trash2, GripVertical } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { cn } from '@/lib/utils';
import { EnhancedChartVisualizer } from '../ChartSectionsConf/EnhancedChartVisualizer';
import './styles/dashboard-chart-fix.css';

interface DashboardChartCardProps {
  chart: SavedChart;
  isEditMode: boolean;
  onUpdateChart: (chartId: string, updates: Partial<SavedChart>) => void;
  onRemoveChart: (chartId: string) => void;
  onToggleFullscreen: (chartId: string) => void;
}

export function DashboardChartCard({
  chart,
  isEditMode,
  onUpdateChart,
  onRemoveChart,
  onToggleFullscreen
}: DashboardChartCardProps) {
  const [isDragging, setIsDragging] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const isResizingRef = useRef<boolean>(false);

  // Enhanced function to trigger resize for charts
  const triggerResize = useCallback(() => {
    // Prevent infinite loops by checking if we're already resizing
    if (isResizingRef.current) return;

    isResizingRef.current = true;

    // Use setTimeout to break the call stack and prevent infinite loops
    setTimeout(() => {
      isResizingRef.current = false;
    }, 100);

    // Directly trigger resize on any ECharts instances in the card
    if (chartContainerRef.current) {
      // Find all canvas elements (ECharts uses canvas)
      const canvasElements = chartContainerRef.current.querySelectorAll('canvas');

      // Find any ECharts instances directly
      const echartsElements = chartContainerRef.current.querySelectorAll('.echarts-for-react, [_echarts_instance_]');

      // Try to access ECharts instances through data attributes
      if (echartsElements.length > 0) {
        echartsElements.forEach(element => {
          // Try to get the ECharts instance
          const instance = (element as any).__echarts_instance__ ||
                          (element as any)._echarts_instance_ ||
                          (window as any).echarts?.getInstanceByDom?.(element);

          if (instance && typeof instance.resize === 'function') {
            // If we found an instance, resize it directly
            instance.resize({
              width: 'auto',
              height: 'auto'
            });
          }
        });
      }

      // Remove canvas event dispatching to prevent infinite loops

      // Force redraw by temporarily adjusting container size
      const container = chartContainerRef.current;
      const originalWidth = container.style.width;
      const originalHeight = container.style.height;

      // Force a reflow
      container.style.width = '100.1%';
      container.style.height = '100.1%';

      // Force browser to process the size change
      void container.offsetWidth;

      // Restore original size
      container.style.width = originalWidth;
      container.style.height = originalHeight;
    }
    
  }, []);

  // Resize handle component
  const ResizeHandle = ({ direction }: { direction: 'e' | 's' | 'se' }) => {
    const handleMouseDown = (e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();

      const startX = e.clientX;
      const startY = e.clientY;
      const startWidth = chart.width || 4;
      const startHeight = chart.height || 3;

      const handleMouseMove = (moveEvent: MouseEvent) => {
        moveEvent.preventDefault();

        // Set a flag to indicate resizing is in progress
        if (typeof window !== 'undefined') {
          window.isResizingDashboardItem = true;
        }

        const deltaX = moveEvent.clientX - startX;
        const deltaY = moveEvent.clientY - startY;

        // Calculate new width and height based on direction
        let newWidth = startWidth;
        let newHeight = startHeight;

        if (direction === 'e' || direction === 'se') {
          // Limit width to a maximum of 12 grid columns to prevent breaking dashboard border
          newWidth = Math.min(12, Math.max(2, startWidth + Math.round(deltaX / 100)));
        }

        if (direction === 's' || direction === 'se') {
          // Ensure reasonable height limits
          newHeight = Math.max(2, startHeight + Math.round(deltaY / 100));
        }

        // Update chart dimensions
        onUpdateChart(chart.id, { width: newWidth, height: newHeight });
      };

      const handleMouseUp = () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);

        // Clear the resizing flag
        if (typeof window !== 'undefined') {
          window.isResizingDashboardItem = false;
        }

        // Enhanced resize handling with more frequent checks
        // Immediate resize
        triggerResize();

        // Series of delayed resizes with increasing intervals to ensure charts properly redraw
        // as the browser completes layout calculations
        const resizeIntervals = [10, 50, 100, 200, 300, 500, 1000];
        resizeIntervals.forEach(delay => {
          setTimeout(triggerResize, delay);
        });

        // Also notify any parent components that might need to know about the resize
        if (typeof window !== 'undefined' && window.dispatchEvent) {
          window.dispatchEvent(new CustomEvent('dashboard-item-resized', {
            detail: { itemId: chart.id, width: chart.width, height: chart.height }
          }));
        }
      };

      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    };

    return (
      <div
        className={`absolute ${direction === 'e' ? 'right-0 top-0 bottom-0 w-2 cursor-ew-resize' :
                              direction === 's' ? 'bottom-0 left-0 right-0 h-2 cursor-ns-resize' :
                              'bottom-0 right-0 w-4 h-4 cursor-nwse-resize'}`}
        style={{
          backgroundColor: 'rgba(0, 0, 0, 0.05)',
          zIndex: 10,
          borderRadius: direction === 'se' ? '0 0 4px 0' : '0'
        }}
        onMouseDown={handleMouseDown}
      />
    );
  };

  // Enhanced resize handling with improved observers and event handling
  useEffect(() => {
    // Initial resize with multiple attempts to ensure the component is fully rendered
    setTimeout(triggerResize, 10);
    setTimeout(triggerResize, 50);
    setTimeout(triggerResize, 200);

    // Track resize timeouts for cleanup
    const timeouts: NodeJS.Timeout[] = [];

    // Improved resize handler with throttling
    let resizeTimeout: NodeJS.Timeout;
    let lastResizeTime = 0;
    const THROTTLE_DELAY = 100; // ms

    const handleResize = () => {
      const now = Date.now();

      // Clear any pending timeout
      clearTimeout(resizeTimeout);

      // If we recently handled a resize, throttle
      if (now - lastResizeTime < THROTTLE_DELAY) {
        resizeTimeout = setTimeout(() => {
          lastResizeTime = Date.now();
          // Direct chart resize without triggering window events
          if (chartContainerRef.current) {
            const echartsElements = chartContainerRef.current.querySelectorAll('.echarts-for-react, [_echarts_instance_]');
            echartsElements.forEach(element => {
              const instance = (element as any).__echarts_instance__ ||
                              (element as any)._echarts_instance_ ||
                              (window as any).echarts?.getInstanceByDom?.(element);
              if (instance && typeof instance.resize === 'function') {
                instance.resize({ width: 'auto', height: 'auto' });
              }
            });
          }
        }, THROTTLE_DELAY);
      } else {
        // Otherwise handle immediately
        lastResizeTime = now;
        // Direct chart resize without triggering window events
        if (chartContainerRef.current) {
          const echartsElements = chartContainerRef.current.querySelectorAll('.echarts-for-react, [_echarts_instance_]');
          echartsElements.forEach(element => {
            const instance = (element as any).__echarts_instance__ ||
                            (element as any)._echarts_instance_ ||
                            (window as any).echarts?.getInstanceByDom?.(element);
            if (instance && typeof instance.resize === 'function') {
              instance.resize({ width: 'auto', height: 'auto' });
            }
          });
        }
      }
    };

    window.addEventListener('resize', handleResize);

    // Listen for parent container resizes
    window.addEventListener('dashboard-layout-changed', handleResize);

    // Create an enhanced ResizeObserver with better error handling
    let resizeObserver: ResizeObserver | null = null;

    try {
      resizeObserver = new ResizeObserver((entries) => {
        // Check if entries contain our elements
        const isOurElement = entries.some(entry =>
          entry.target === cardRef.current ||
          entry.target === chartContainerRef.current ||
          cardRef.current?.contains(entry.target) ||
          chartContainerRef.current?.contains(entry.target)
        );

        if (isOurElement) {
          handleResize();
        }
      });

      // Observe the card, chart container, and parent elements for more reliable resize detection
      if (cardRef.current) {
        resizeObserver.observe(cardRef.current);

        // Also observe parent elements up to 3 levels
        let parent = cardRef.current.parentElement;
        let level = 0;
        while (parent && level < 3) {
          resizeObserver.observe(parent);
          parent = parent.parentElement;
          level++;
        }
      }

      if (chartContainerRef.current) {
        resizeObserver.observe(chartContainerRef.current);

        // Also observe any canvas elements inside (ECharts uses canvas)
        const canvasElements = chartContainerRef.current.querySelectorAll('canvas');
        canvasElements.forEach(canvas => {
          if (resizeObserver) {
            resizeObserver.observe(canvas);
          }
        });
      }
    } catch (error) {
      console.error('ResizeObserver error:', error);

      // Fallback: poll for size changes if ResizeObserver fails
      const pollInterval = setInterval(() => {
        if (cardRef.current && chartContainerRef.current) {
          const currentWidth = cardRef.current.clientWidth;
          const currentHeight = cardRef.current.clientHeight;

          // Store these values on the element itself using dataset
          const prevWidth = parseInt(cardRef.current.dataset.prevWidth || '0', 10);
          const prevHeight = parseInt(cardRef.current.dataset.prevHeight || '0', 10);

          if (currentWidth !== prevWidth || currentHeight !== prevHeight) {
            // Size changed, trigger resize
            triggerResize();

            // Update stored dimensions
            cardRef.current.dataset.prevWidth = String(currentWidth);
            cardRef.current.dataset.prevHeight = String(currentHeight);
          }
        }
      }, 200);

      // Add to timeouts for cleanup
      timeouts.push(pollInterval as unknown as NodeJS.Timeout);
    }

    // Handle visibility changes (tab switching, etc.)
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        // When tab becomes visible again, trigger multiple resize events
        // to ensure charts render properly
        triggerResize();
        setTimeout(triggerResize, 100);
        setTimeout(triggerResize, 500);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Handle theme changes which might affect chart rendering
    const handleThemeChange = () => {
      triggerResize();
    };

    // Listen for theme changes if using a theme toggle
    document.addEventListener('themeChange', handleThemeChange);

    // MutationObserver to detect DOM changes that might affect chart size
    const mutationObserver = new MutationObserver((mutations) => {
      // Only trigger for relevant mutations (class changes that might affect layout)
      const shouldTrigger = mutations.some(mutation =>
        mutation.type === 'attributes' &&
        (mutation.attributeName === 'class' || mutation.attributeName === 'style')
      );

      if (shouldTrigger) {
        handleResize();
      }
    });

    // Observe the card and its parent for class/style changes
    if (cardRef.current) {
      mutationObserver.observe(cardRef.current, {
        attributes: true,
        attributeFilter: ['class', 'style']
      });

      if (cardRef.current.parentElement) {
        mutationObserver.observe(cardRef.current.parentElement, {
          attributes: true,
          attributeFilter: ['class', 'style']
        });
      }
    }

    return () => {
      // Clean up all event listeners and observers
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('dashboard-layout-changed', handleResize);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      document.removeEventListener('themeChange', handleThemeChange);

      if (resizeObserver) {
        resizeObserver.disconnect();
      }

      mutationObserver.disconnect();

      clearTimeout(resizeTimeout);

      // Clear all timeouts
      timeouts.forEach(timeout => clearTimeout(timeout));
    };
  }, []);

  return (
    <div
      ref={cardRef}
      className={cn(
        "relative rounded-lg bg-card text-card-foreground dashboard-card-group",
        isDragging ? "z-50 cursor-grabbing" : isEditMode ? "cursor-grab" : "",
        isEditMode ? "border shadow-sm hover:border-primary" : "border-0 shadow-none",
        "dashboard-item-container", // Add this class for grid layout integration
        !isEditMode && "no-border" // Add no-border class in view mode for cleaner appearance
      )}
      data-item-type="chart"
      data-item-id={chart.id}
      data-is-dragging={isDragging}
      data-is-resizing={false}
      style={{
        width: '100%',
        height: '100%',
        display: 'grid',
        gridTemplateRows: (chart.description || chart.config.description)
          ? '24px 1fr 20px'
          : '24px 1fr',
        overflow: 'hidden',
        transform: 'translate3d(0,0,0)', // Force GPU acceleration
        willChange: isEditMode ? 'transform, box-shadow' : 'auto'
      }}
    >
      {/* Draggable Handle - Positioned at the top */}
      {isEditMode && (
        <div className="draggable-handle absolute top-0 left-0 w-full h-6 flex items-center justify-center cursor-grab rounded-t-md z-10 bg-muted/50">
          <GripVertical className="h-4 w-4 text-primary" />
        </div>
      )}

      {/* Card Header */}
      <div className={`flex items-center justify-between px-1 py-0.5 ${isEditMode ? 'border-b border-border' : ''} bg-card`}>
        <div className="flex items-center gap-1">
          <h3 className="text-xs font-medium truncate text-foreground">{chart.title || chart.config.title || 'Untitled Chart'}</h3>
          <span className="text-[10px] text-muted-foreground bg-muted/20 px-1 py-0.5 rounded ml-1">
            {chart.chartType || chart.config.type || 'bar'}
          </span>
        </div>
        <div className="flex items-center gap-1">
          {isEditMode && (
            <Button
              variant="ghost"
              size="sm"
              className="h-5 w-5 p-0 hover:bg-blue-100 hover:text-blue-600 transition-colors non-draggable"
              onClick={() => onToggleFullscreen(chart.id)}
            >
              <Maximize2 className="h-3 w-3" />
            </Button>
          )}
        </div>
      </div>

      {/* Hover-based Remove Button */}
      {isEditMode && (
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="card-remove-button h-6 w-6 p-0 hover:bg-red-100 hover:text-red-600 transition-all non-draggable"
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Delete Chart</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to delete this chart? This action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={() => onRemoveChart(chart.id)}
                className="bg-red-600 hover:bg-red-700"
              >
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}

      {/* Chart Area - Simplified structure for better resizing */}
      <div
        ref={chartContainerRef}
        className="chart-container dark:bg-black bg-white"
        style={{
          width: '100%',
          height: '100%',
          overflow: 'hidden',
          display: 'flex',
          flexDirection: 'column',
          marginTop: isEditMode ? '20px' : '0', // Add margin when in edit mode to account for the drag handle
          minHeight: '100px', // Ensure minimum height for small charts
          position: 'relative' // Ensure proper positioning context
        }}
      >
        <EnhancedChartVisualizer
          data={chart.data}
          initialChartType={chart.chartType || chart.config.type || 'bar'}
          chartConfig={{
            ...chart.config,
            type: chart.chartType || chart.config.type || 'bar',
            color: chart.config.color,
            customLabel: chart.config.customLabel,
            showGrid: true,
            // Adjust legend and labels based on container size
            showLegend: chart.height > 2, // Only show legend if chart is tall enough
            showLabels: false, // Disable labels for cleaner look in small cards
            ...(chart.config.aggregation ? { aggregation: chart.config.aggregation } : {}),
            ...(chart.config.groupBy ? { groupBy: chart.config.groupBy } : {}),
            ...(chart.config.timeScale ? { timeScale: chart.config.timeScale } : {}),
            ...(chart.config.enableZoom !== undefined ? { enableZoom: chart.config.enableZoom } : {}),
            ...(chart.config.multiSeries !== undefined ? { multiSeries: chart.config.multiSeries } : {})
          }}
          showConfig={false}
          fullHeight={true}
          isDashboardChart={true}
        />
      </div>

      {/* Chart Footer - Only shown if there's a description */}
      {(chart.description || chart.config.description) && (
        <div className={`${isEditMode ? 'border-t dark:border-gray-700' : ''} dark:bg-black bg-white text-[10px] text-muted-foreground overflow-hidden`}>
          <p className="truncate">{chart.description || chart.config.description}</p>
        </div>
      )}

      {/* Resize Handles */}
      {isEditMode && (
        <>
          <ResizeHandle direction="e" />
          <ResizeHandle direction="s" />
          <ResizeHandle direction="se" />
        </>
      )}
    </div>
  );
}
