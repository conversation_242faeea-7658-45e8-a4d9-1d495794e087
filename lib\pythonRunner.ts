import fetch from 'cross-fetch';

export interface OutputTypeInfo {
  type: 'dataframe' | 'plot' | 'image' | 'video' | 'streamlit' | 'text' | 'json' | 'error';
  format?: string;
  shape?: [number, number];
  size?: number;
  variables?: string[];
}

export interface ExecutionResult {
  data?: any[];
  output?: string;
  plots?: string[];
  error?: string;
  errorDetails?: any;
  executionTime?: number;
  outputType?: OutputTypeInfo;
  variables?: Record<string, any>;
  variableTypes?: Record<string, string>;
}

export class PythonRunner {
  private static instance: PythonRunner;
  private baseUrl: string;
  private authToken: string | null;
  private tokenExpiry: number | null;
  private isOfflineMode: boolean = false;
  private variableContext: Record<string, any> = {}; // Persistent variable context

  private constructor() {
    // Use cloud backend URL in production, localhost in development
    this.baseUrl = process.env.NODE_ENV === 'production'
      ? 'https://flopbackend.onrender.com'
      : 'http://127.0.0.1:8000';
    this.authToken = null;
    this.tokenExpiry = null;
  }

  static getInstance(): PythonRunner {
    if (!PythonRunner.instance) {
      PythonRunner.instance = new PythonRunner();
    }
    return PythonRunner.instance;
  }

  setAuthToken(token: string) {
    this.authToken = token;
    this.tokenExpiry = Date.now() + 3600000; // 1 hour expiry
    console.log('PythonRunner token set:', token.substring(0, 20) + '...');
  }

  setOfflineMode(enabled: boolean) {
    this.isOfflineMode = enabled;
  }

  private isTokenValid(): boolean {
    return !!(this.authToken && this.tokenExpiry && Date.now() < this.tokenExpiry);
  }

  private formatPythonError(error: string): string {
    // Format Python error messages to be more readable
    const lines = error.split('\n');

    // Handle specific error types
    if (error.includes('UnboundLocalError') && error.includes('result_val')) {
      return 'Python execution error: Variable scoping issue detected. This has been fixed in the latest version.';
    }

    if (error.includes('Streamlit') && error.includes('port')) {
      return 'Streamlit app deployment issue: Cloud environments require special handling for Streamlit apps.';
    }

    if (error.includes('cannot access local variable')) {
      return 'Python variable scoping error: This issue has been resolved in the backend.';
    }

    if (lines.length > 1) {
      return lines[lines.length - 1]; // Return the last line which usually contains the actual error
    }
    return error;
  }

  private detectOutputType(result: any): OutputTypeInfo {
    // Detect the type of output based on the result structure
    if (result.error) {
      return { type: 'error' };
    }

    if (result.plots && result.plots.length > 0) {
      const plot = result.plots[0];
      if (plot.includes('data:video/') || plot.includes('.mp4') || plot.includes('.avi')) {
        return { type: 'video', format: 'video' };
      } else if (plot.includes('streamlit') || plot.includes('localhost:8501') || plot.includes('data:text/html')) {
        return { type: 'streamlit', format: 'iframe' };
      } else {
        return { type: 'plot', format: 'image' };
      }
    }

    // Check if this is a Streamlit app result (new cloud-compatible format)
    if (result.outputType?.type === 'streamlit' ||
      (result.embed_url && result.embed_url.includes('data:text/html')) ||
      (result.deployment_options && result.deployment_options.streamlit_cloud)) {
      return { type: 'streamlit', format: 'iframe' };
    }

    if (result.data && Array.isArray(result.data)) {
      const shape: [number, number] = [result.data.length, Object.keys(result.data[0] || {}).length];
      return {
        type: 'dataframe',
        format: 'table',
        shape,
        size: result.data.length
      };
    }

    if (result.output) {
      try {
        JSON.parse(result.output);
        return { type: 'json', format: 'json' };
      } catch {
        return { type: 'text', format: 'text' };
      }
    }

    return { type: 'text', format: 'text' };
  }

  // Get current variable context
  getVariableContext(): Record<string, any> {
    return { ...this.variableContext };
  }

  // Clear variable context
  clearVariableContext(): void {
    this.variableContext = {};
  }

  // Set variable in context
  setVariable(name: string, value: any): void {
    this.variableContext[name] = value;
  }

  async execute(code: string, datasetId: string, datasetIds?: string[], variableContext?: Record<string, any>): Promise<ExecutionResult> {
    try {
      if (this.isOfflineMode) {
        return this.executeOffline(code, datasetId);
      }

      if (!this.isTokenValid()) {
        throw new Error('Auth token is invalid or expired');
      }

      // Add common imports and utilities if not present in the code
      const enhancedCode = this.enhanceCode(code);

      // Merge provided variable context with persistent context
      const mergedContext = { ...this.variableContext, ...variableContext };

      const response = await fetch(`${this.baseUrl}/api/execute`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.authToken!}`,
        },
        body: JSON.stringify({
          code: enhancedCode,  // Changed from 'query' to 'code' to match backend
          language: 'python',
          datasetId,
          datasetIds: datasetIds || [datasetId],
          variableContext: mergedContext, // Pass variable context to backend
        }),
      });

      if (response.status === 401) {
        throw new Error('Authentication failed');
      }

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Python execution failed:', {
          status: response.status,
          data: errorData,
          datasetId
        });
        throw new Error(this.formatPythonError(errorData.detail || `HTTP error! status: ${response.status}`));
      }

      const result = await response.json();

      // Handle plots if present (including URLs)
      if (result.plots?.length > 0) {
        result.plots = result.plots.map((plot: string) => {
          // Check if it's already a data URL or HTTP URL
          if (plot.startsWith('data:') || plot.startsWith('http://') || plot.startsWith('https://')) {
            return plot;
          }
          // Otherwise treat as base64
          return `data:image/png;base64,${plot}`;
        });
        console.log(`Processed ${result.plots.length} plots/URLs from Python execution`);
      }

      // Format DataFrame results
      if (result.data && Array.isArray(result.data)) {
        // Keep only the first 1000 rows for performance
        result.data = result.data.slice(0, 1000);
      }

      // Update persistent variable context if provided
      if (result.variables) {
        this.variableContext = { ...this.variableContext, ...result.variables };
      }

      // Detect output type
      const outputType = this.detectOutputType(result);
      result.outputType = outputType;

      console.log('Python execution succeeded with result shape:',
        result.data ? result.data.length : 'no data', 'Output type:', outputType.type);

      return result as ExecutionResult;

    } catch (error) {
      if (!this.isOfflineMode) {
        // If online execution fails, try offline mode
        this.setOfflineMode(true);
        return this.executeOffline(code, datasetId);
      }
      throw error;
    }
  }

  private enhanceCode(code: string): string {
    // Add common imports if not present
    const imports = [
      'import pandas as pd',
      'import numpy as np',
      'import matplotlib.pyplot as plt',
      'import seaborn as sns'
    ];

    // Add helpful plot setup code if not present
    const plotSetup = [
      '# Set plot style for better visualization',
      'plt.style.use("default")',
      'sns.set_theme(style="whitegrid")',
      'plt.rcParams.update({"figure.figsize": (10, 6)})',
    ];

    // Check if code already has imports
    const hasImports = imports.some(imp => code.includes(imp));
    // Check if code already has plot setup
    const hasPlotSetup = code.includes('plt.style.use') || code.includes('sns.set_theme');

    let enhancedCode = code;

    // Add imports if needed
    if (!hasImports) {
      enhancedCode = `${imports.join('\n')}\n\n${enhancedCode}`;
    }

    // Add plot setup if needed and if code seems to be creating plots
    if (!hasPlotSetup && (code.includes('plt.') || code.includes('sns.'))) {
      enhancedCode = `${plotSetup.join('\n')}\n\n${enhancedCode}`;
    }

    // Add a reminder about get_plot() if the code creates plots but doesn't call get_plot()
    if ((code.includes('plt.') || code.includes('sns.')) && !code.includes('get_plot()')) {
      enhancedCode += '\n\n# Uncomment the next line to return the plot as an image\n# result = get_plot()'
    }

    return enhancedCode;
  }

  private async executeOffline(code: string, datasetId: string) {
    const { getMockDataset } = await import('@/utils/mockData');
    const dataset = getMockDataset(datasetId);

    if (!dataset) {
      throw new Error('Dataset not found');
    }

    // Add common imports and utilities if not present
    const enhancedCode = this.enhanceCode(code);

    // Simulate Python execution with mock data
    let result: any = {
      data: dataset.data,
      output: `Running in offline mode\nDataset shape: (${dataset.data.length}, ${Object.keys(dataset.data[0]).length})\n`,
      plots: []
    };

    // Handle basic DataFrame operations
    if (code.includes('df.head()')) {
      result.data = dataset.data.slice(0, 5);
    } else if (code.includes('df.describe()')) {
      result.data = this.generateDescriptiveStats(dataset.data);
    }

    // Handle plot generation
    if (code.includes('get_plot()')) {
      result.plots = [this.generateMockPlot(code, dataset.data)];
    }

    return result;
  }

  private generateDescriptiveStats(data: any[]) {
    // Generate basic statistical summary
    const numericColumns = Object.keys(data[0]).filter(key =>
      typeof data[0][key] === 'number'
    );

    return numericColumns.map(col => {
      const values = data.map(row => row[col]).filter(v => !isNaN(v));
      return {
        column: col,
        count: values.length,
        mean: values.reduce((a, b) => a + b, 0) / values.length,
        std: Math.sqrt(values.reduce((a, b) => a + Math.pow(b - (values.reduce((c, d) => c + d, 0) / values.length), 2), 0) / values.length),
        min: Math.min(...values),
        max: Math.max(...values)
      };
    });
  }

  private generateMockPlot(code: string, data: any[]): string {
    // Base64 encoded simple plot placeholder
    // You can enhance this to generate different types of mock plots based on the code
    return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...'; // Add your base64 mock plot image
  }
}

