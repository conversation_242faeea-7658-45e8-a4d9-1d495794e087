import { useEffect, useRef } from 'react';
import { toast } from 'sonner';

export interface KeyboardShortcutHandlers {
  onRun: () => void;
  onDelete: () => void;
  onAddCellAbove: () => void;
  onAddCellBelow: () => void;
  onMoveUp?: () => void;
  onMoveDown?: () => void;
  onConvertToMarkdown?: () => void;
  onConvertToCode?: () => void;
  onFocusEditor?: () => void;
  onToggleLanguage?: () => void;
}

export interface KeyboardShortcutOptions {
  cellId: string;
  isHovered: boolean;
  isRunning: boolean;
  cellType: 'code' | 'markdown';
  language: string;
  editorRef: React.RefObject<any>;
  disabled?: boolean;
}

export const KEYBOARD_SHORTCUTS = {
  RUN_CELL: 'Ctrl+Enter',
  RUN_AND_ADD: 'Shift+Enter',
  ADD_CELL_ABOVE: 'Ctrl+Shift+B',
  ADD_CELL_BELOW: 'Ctrl+B',
  DELETE_CELL: 'Ctrl+Shift+D',
  MOVE_UP: 'Ctrl+Shift+↑',
  MOVE_DOWN: 'Ctrl+Shift+↓',
  CONVERT_TO_MARKDOWN: 'Ctrl+M',
  CONVERT_TO_CODE: 'Ctrl+Y',
  FOCUS_EDITOR: 'Enter',
  TOGGLE_LANGUAGE: 'Ctrl+Shift+L',
  ESCAPE: 'Escape'
} as const;

export function useKeyboardShortcuts(
  handlers: KeyboardShortcutHandlers,
  options: KeyboardShortcutOptions
) {
  const { cellId, isHovered, isRunning, cellType, language, editorRef, disabled = false } = options;
  const lastShortcutTime = useRef<number>(0);

  useEffect(() => {
    if (disabled) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      // Prevent rapid-fire shortcuts
      const now = Date.now();
      if (now - lastShortcutTime.current < 100) return;

      // Check if we're in an input field (but allow shortcuts in Monaco editor)
      const target = event.target as HTMLElement;
      const isInInput = target.tagName === 'INPUT' || target.tagName === 'TEXTAREA';
      const isInMonaco = target.closest('.monaco-editor') !== null;
      const isInCell = target.closest(`[data-cell-id="${cellId}"]`) !== null;

      // Only handle shortcuts if:
      // 1. We're in the Monaco editor for this cell, OR
      // 2. Cell is hovered/focused, OR
      // 3. It's a global shortcut (like Ctrl+Enter in Monaco)
      const shouldHandleShortcut = isInMonaco || isInCell || isHovered;

      // For Ctrl+Enter, prioritize the cell that contains the focused editor
      if (ctrlKey && !shiftKey && !altKey && key === 'Enter') {
        if (isInMonaco) {
          // This is the cell with the focused editor - handle it
          event.preventDefault();
          event.stopPropagation();
          if (!isRunning && cellType === 'code') {
            lastShortcutTime.current = now;
            handlers.onRun();
            toast.success('Running cell...', { duration: 1000 });
          }
          return;
        } else if (!shouldHandleShortcut) {
          // Not in this cell's editor, don't handle
          return;
        }
      }

      if (!shouldHandleShortcut) return;

      // Don't interfere with normal input fields (except Monaco)
      if (isInInput && !isInMonaco) return;

      const { ctrlKey, shiftKey, altKey, key, code } = event;



      // Shift+Enter: Run cell and add new cell below
      if (!ctrlKey && shiftKey && !altKey && key === 'Enter') {
        event.preventDefault();
        event.stopPropagation();
        if (!isRunning && cellType === 'code') {
          lastShortcutTime.current = now;
          handlers.onRun();
          // Add cell below after a short delay
          setTimeout(() => {
            handlers.onAddCellBelow();
          }, 100);
          toast.success('Running cell and adding new cell...', { duration: 1000 });
        }
        return;
      }

      // Only handle other shortcuts when cell is hovered or focused
      if (!isHovered && !isInCell) return;

      // Ctrl+Shift+D: Delete cell
      if (ctrlKey && shiftKey && !altKey && key === 'D') {
        event.preventDefault();
        event.stopPropagation();
        lastShortcutTime.current = now;
        handlers.onDelete();
        toast.success('Cell deleted', { duration: 1000 });
        return;
      }

      // Ctrl+B: Add cell below
      if (ctrlKey && !shiftKey && !altKey && key === 'b') {
        event.preventDefault();
        event.stopPropagation();
        lastShortcutTime.current = now;
        handlers.onAddCellBelow();
        toast.success('Added cell below', { duration: 1000 });
        return;
      }

      // Ctrl+Shift+B: Add cell above
      if (ctrlKey && shiftKey && !altKey && key === 'B') {
        event.preventDefault();
        event.stopPropagation();
        lastShortcutTime.current = now;
        handlers.onAddCellAbove();
        toast.success('Added cell above', { duration: 1000 });
        return;
      }

      // Ctrl+Shift+↑: Move cell up
      if (ctrlKey && shiftKey && !altKey && key === 'ArrowUp' && handlers.onMoveUp) {
        event.preventDefault();
        event.stopPropagation();
        lastShortcutTime.current = now;
        handlers.onMoveUp();
        toast.success('Moved cell up', { duration: 1000 });
        return;
      }

      // Ctrl+Shift+↓: Move cell down
      if (ctrlKey && shiftKey && !altKey && key === 'ArrowDown' && handlers.onMoveDown) {
        event.preventDefault();
        event.stopPropagation();
        lastShortcutTime.current = now;
        handlers.onMoveDown();
        toast.success('Moved cell down', { duration: 1000 });
        return;
      }

      // Ctrl+M: Convert to markdown
      if (ctrlKey && !shiftKey && !altKey && key === 'm' && cellType === 'code' && handlers.onConvertToMarkdown) {
        event.preventDefault();
        event.stopPropagation();
        lastShortcutTime.current = now;
        handlers.onConvertToMarkdown();
        toast.success('Converted to markdown', { duration: 1000 });
        return;
      }

      // Ctrl+Y: Convert to code
      if (ctrlKey && !shiftKey && !altKey && key === 'y' && cellType === 'markdown' && handlers.onConvertToCode) {
        event.preventDefault();
        event.stopPropagation();
        lastShortcutTime.current = now;
        handlers.onConvertToCode();
        toast.success('Converted to code', { duration: 1000 });
        return;
      }

      // Enter: Focus editor (when not already in editor)
      if (!ctrlKey && !shiftKey && !altKey && key === 'Enter' && !isInMonaco && handlers.onFocusEditor) {
        event.preventDefault();
        event.stopPropagation();
        lastShortcutTime.current = now;
        handlers.onFocusEditor();
        return;
      }

      // Ctrl+Shift+L: Toggle language (for code cells)
      if (ctrlKey && shiftKey && !altKey && key === 'L' && cellType === 'code' && handlers.onToggleLanguage) {
        event.preventDefault();
        event.stopPropagation();
        lastShortcutTime.current = now;
        handlers.onToggleLanguage();
        return;
      }

      // Escape: Blur editor/unfocus
      if (!ctrlKey && !shiftKey && !altKey && key === 'Escape' && isInMonaco) {
        event.preventDefault();
        event.stopPropagation();
        if (editorRef.current) {
          editorRef.current.getModel()?.setValue(editorRef.current.getValue());
          (document.activeElement as HTMLElement)?.blur();
        }
        return;
      }
    };

    // Add event listener to document for global shortcuts
    document.addEventListener('keydown', handleKeyDown, true);

    return () => {
      document.removeEventListener('keydown', handleKeyDown, true);
    };
  }, [
    handlers,
    cellId,
    isHovered,
    isRunning,
    cellType,
    language,
    editorRef,
    disabled
  ]);

  // Return shortcut information for display
  return {
    shortcuts: KEYBOARD_SHORTCUTS,
    getShortcutHelp: () => {
      const shortcuts = [
        { key: KEYBOARD_SHORTCUTS.RUN_CELL, description: 'Run cell' },
        { key: KEYBOARD_SHORTCUTS.RUN_AND_ADD, description: 'Run cell and add new cell' },
        { key: KEYBOARD_SHORTCUTS.ADD_CELL_BELOW, description: 'Add cell below' },
        { key: KEYBOARD_SHORTCUTS.ADD_CELL_ABOVE, description: 'Add cell above' },
        { key: KEYBOARD_SHORTCUTS.DELETE_CELL, description: 'Delete cell' },
        { key: KEYBOARD_SHORTCUTS.MOVE_UP, description: 'Move cell up' },
        { key: KEYBOARD_SHORTCUTS.MOVE_DOWN, description: 'Move cell down' },
        { key: KEYBOARD_SHORTCUTS.CONVERT_TO_MARKDOWN, description: 'Convert to markdown' },
        { key: KEYBOARD_SHORTCUTS.CONVERT_TO_CODE, description: 'Convert to code' },
        { key: KEYBOARD_SHORTCUTS.FOCUS_EDITOR, description: 'Focus editor' },
        { key: KEYBOARD_SHORTCUTS.TOGGLE_LANGUAGE, description: 'Toggle language' },
        { key: KEYBOARD_SHORTCUTS.ESCAPE, description: 'Unfocus editor' },
      ];
      return shortcuts;
    }
  };
}
