import { Dataset } from '@/types/index';
import { Saved<PERSON><PERSON>, TableItem, PythonPlotItem } from '../DashboardSection/types';

// Output type information
export interface OutputTypeInfo {
  type: 'dataframe' | 'plot' | 'image' | 'video' | 'streamlit' | 'text' | 'json' | 'error';
  format?: string;
  shape?: [number, number];
  size?: number;
  variables?: string[];
}

// Cell data structure
export interface CellData {
  id: string;
  content: string;
  language: string;
  cellType?: 'code' | 'markdown';
  result?: {
    data: any[];
    output?: string;
    plots?: string[];
    variables?: Record<string, any>; // Variables created by this cell
    variableTypes?: Record<string, string>; // Types of variables
    outputType?: OutputTypeInfo; // Information about the output type
  };
  error?: string;
  errorDetails?: {
    message: string;
    code?: string;
    stack?: string;
    serverTrace?: string;
  };
  executionTime?: number;
  isSuccess?: boolean;
  isLoading?: boolean;
  showGraphicWalker?: boolean;
  notes?: string;
  selectedDatasetIds?: string[]; // Selected dataset IDs for each cell
}

// Query result interface
export interface QueryResult {
  [key: string]: string | number | boolean | null;
}

// Variable info interface for Jupyter-like functionality
export interface VariableInfo {
  name: string;
  type: 'dataframe' | 'variable' | 'plot' | 'result' | 'function';
  definedInCell: string;
}

// Cell execution hook return type (simplified for Jupyter kernel)
export interface UseCellExecutionReturn {
  handleRunCell: (cellId: string, code: string, showGraphicWalker?: boolean) => Promise<void>;
  isAlasqlInitialized: boolean;
}

// Server status interface
export interface ServerStatus {
  status: 'healthy' | 'error' | 'warning';
  message: string;
}

// Execution time tracking
export interface ExecutionTimeTracking {
  startTime?: Date;
  endTime?: Date;
}

// Dataset handling hook return type
export interface UseDatasetHandlingReturn {
  datasets: Dataset[];
  datasetCache: Record<string, Dataset>;
  isLoadingDatasets: boolean;
  handleSelectDatasets: (cellId: string, datasetIds: string[]) => Promise<{ selectedList: Dataset[], datasetIds: string[] }>;
  refreshDatasets: () => Promise<void>;
}

// Cell execution hook return type
export interface UseCellExecutionReturn {
  handleRunCell: (cellId: string, code: string, showGraphicWalker?: boolean) => Promise<void>;
  isAlasqlInitialized: boolean;
}

// Chart saving hook return type
export interface UseChartSavingReturn {
  handleSaveChart: (chartData: any, chartConfig: any, chartType: 'line' | 'bar' | 'pie' | 'area', chartId?: string) => void;
  handleSaveTable: (tableData: any[], columns: string[], tableId?: string) => void;
  handleSavePlot: (plotUrl: string, plotTitle: string, plotId?: string) => void;
  handleSaveCalculatorResult: (formula: string, result: any, title?: string, description?: string, resultId?: string) => void;
  handleRemoveChart: (chartId: string) => void;
  handleUpdateChart: (chartId: string, updatedProps: Partial<SavedChart>) => void;
  handleReorderCharts: (reorderedCharts: any[]) => void;
}

// Dashboard interaction hook return type
export interface UseDashboardInteractionReturn {
  handleNotebookImport: (importedNotebook: any) => void;
  handleImportChartConfig: (cellId: string, config: any) => void;
}

// Default content options
export type DefaultContentLanguage = 'sql' | 'python' | 'javascript' | 'markdown';
