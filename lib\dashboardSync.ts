'use client'

import { useDashboardStore } from './dashboardStore'
import { Saved<PERSON>hart, TableItem, PythonPlotItem, CalculatorResultItem, TextItem } from '@/components/ChartBuilder/DashboardSection/types'

// Utility functions to sync dashboard items with the database
export class DashboardSync {
  
  static async syncItemToDatabase(
    item: SavedChart | TableItem | PythonPlotItem | CalculatorResultItem | TextItem,
    action: 'create' | 'update' | 'delete'
  ): Promise<boolean> {
    const { workspaceId, dashboardId } = useDashboardStore.getState().getWorkspaceContext()
    
    if (!workspaceId || !dashboardId) {
      console.warn('No workspace context available for database sync')
      return false
    }

    try {
      switch (action) {
        case 'create':
          return await this.createItem(workspaceId, dashboardId, item)
        case 'update':
          return await this.updateItem(workspaceId, dashboardId, item)
        case 'delete':
          return await this.deleteItem(workspaceId, dashboardId, item.id)
        default:
          return false
      }
    } catch (error) {
      console.error('Error syncing item to database:', error)
      return false
    }
  }

  private static async createItem(
    workspaceId: string,
    dashboardId: string,
    item: SavedChart | TableItem | PythonPlotItem | CalculatorResultItem | TextItem
  ): Promise<boolean> {
    const response = await fetch(`/api/workspaces/${workspaceId}/dashboards/${dashboardId}/items`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        type: item.type,
        // @ts-ignore
        title: item.title,
        // @ts-ignore
        description: item.description,
        data: this.prepareItemData(item),
        config: this.prepareItemConfig(item),
        content: item.type === 'text' ? (item as TextItem).content : null,
        gridColumn: item.gridColumn,
        gridRow: item.gridRow,
        width: item.width,
        height: item.height
      }),
    })

    return response.ok
  }

  private static async updateItem(
    workspaceId: string,
    dashboardId: string,
    item: SavedChart | TableItem | PythonPlotItem | CalculatorResultItem | TextItem
  ): Promise<boolean> {
    const response = await fetch(`/api/workspaces/${workspaceId}/dashboards/${dashboardId}/items/${item.id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        type: item.type,
        // @ts-ignore
        title: item.title,
        // @ts-ignore
        description: item.description,
        data: this.prepareItemData(item),
        config: this.prepareItemConfig(item),
        content: item.type === 'text' ? (item as TextItem).content : null,
        gridColumn: item.gridColumn,
        gridRow: item.gridRow,
        width: item.width,
        height: item.height
      }),
    })

    return response.ok
  }

  private static async deleteItem(
    workspaceId: string,
    dashboardId: string,
    itemId: string
  ): Promise<boolean> {
    const response = await fetch(`/api/workspaces/${workspaceId}/dashboards/${dashboardId}/items/${itemId}`, {
      method: 'DELETE',
    })

    return response.ok
  }

  private static prepareItemData(item: SavedChart | TableItem | PythonPlotItem | CalculatorResultItem | TextItem): any {
    switch (item.type) {
      case 'chart':
        return (item as SavedChart).data
      case 'table':
        return (item as TableItem).data
      case 'pythonplot':
        return { plotUrl: (item as PythonPlotItem).plotUrl }
      case 'calculator':
        const calcItem = item as CalculatorResultItem
        return {
          result: calcItem.result,
          formula: calcItem.formula,
          resultType: calcItem.resultType,
          formattedResult: calcItem.formattedResult,
          icon: calcItem.icon
        }
      case 'text':
        const textItem = item as TextItem
        return {
          textAlign: textItem.textAlign,
          textStyle: textItem.textStyle,
          isBold: textItem.isBold,
          isItalic: textItem.isItalic,
          isUnderline: textItem.isUnderline,
          color: textItem.color,
          backgroundColor: textItem.backgroundColor
        }
      default:
        return {}
    }
  }

  private static prepareItemConfig(item: SavedChart | TableItem | PythonPlotItem | CalculatorResultItem | TextItem): any {
    switch (item.type) {
      case 'chart':
        return (item as SavedChart).config
      case 'table':
        return { columns: (item as TableItem).columns }
      case 'pythonplot':
        return {}
      case 'calculator':
        const calcItem = item as CalculatorResultItem
        return {
          formula: calcItem.formula,
          icon: calcItem.icon
        }
      case 'text':
        const textItem = item as TextItem
        return {
          textAlign: textItem.textAlign,
          textStyle: textItem.textStyle,
          placeholder: textItem.placeholder
        }
      default:
        return {}
    }
  }

  // Batch sync all items to database (used during workspace save)
  static async syncAllItemsToDatabase(): Promise<boolean> {
    const { workspaceId, dashboardId, charts, tables, plots, calculatorResults, textItems } = useDashboardStore.getState()
    
    if (!workspaceId || !dashboardId) {
      console.warn('No workspace context available for batch sync')
      return false
    }

    try {
      // Clear existing items first
      const existingItemsResponse = await fetch(`/api/workspaces/${workspaceId}/dashboards/${dashboardId}/items`)
      if (existingItemsResponse.ok) {
        const existingItemsData = await existingItemsResponse.json()
        if (existingItemsData.success && existingItemsData.items) {
          // Delete existing items
          const deletePromises = existingItemsData.items.map((item: any) =>
            fetch(`/api/workspaces/${workspaceId}/dashboards/${dashboardId}/items/${item.id}`, {
              method: 'DELETE'
            })
          )
          await Promise.all(deletePromises)
        }
      }

      // Create all current items
      const allItems = [...charts, ...tables, ...plots, ...calculatorResults, ...textItems]
      const createPromises = allItems.map(item => this.createItem(workspaceId, dashboardId, item))
      const results = await Promise.all(createPromises)
      
      return results.every(result => result)
    } catch (error) {
      console.error('Error in batch sync:', error)
      return false
    }
  }
}

// Enhanced store actions that sync with database
export const createDashboardStoreWithSync = () => {
  const store = useDashboardStore.getState()
  
  return {
    ...store,
    
    // Override add functions to sync with database
    addChart: async (chart: SavedChart) => {
      store.addChart(chart)
      await DashboardSync.syncItemToDatabase(chart, 'create')
    },
    
    addTable: async (table: TableItem) => {
      store.addTable(table)
      await DashboardSync.syncItemToDatabase(table, 'create')
    },
    
    addPlot: async (plot: PythonPlotItem) => {
      store.addPlot(plot)
      await DashboardSync.syncItemToDatabase(plot, 'create')
    },
    
    addCalculatorResult: async (result: CalculatorResultItem) => {
      store.addCalculatorResult(result)
      await DashboardSync.syncItemToDatabase(result, 'create')
    },
    
    // Override update functions to sync with database
    updateChart: async (chartId: string, updatedChart: Partial<SavedChart>) => {
      store.updateChart(chartId, updatedChart)
      const chart = store.charts.find(c => c.id === chartId)
      if (chart) {
        await DashboardSync.syncItemToDatabase(chart, 'update')
      }
    },
    
    updateTable: async (tableId: string, updatedTable: Partial<TableItem>) => {
      store.updateTable(tableId, updatedTable)
      const table = store.tables.find(t => t.id === tableId)
      if (table) {
        await DashboardSync.syncItemToDatabase(table, 'update')
      }
    },
    
    updatePlot: async (plotId: string, updatedPlot: Partial<PythonPlotItem>) => {
      store.updatePlot(plotId, updatedPlot)
      const plot = store.plots.find(p => p.id === plotId)
      if (plot) {
        await DashboardSync.syncItemToDatabase(plot, 'update')
      }
    },
    
    updateCalculatorResult: async (resultId: string, updatedResult: Partial<CalculatorResultItem>) => {
      store.updateCalculatorResult(resultId, updatedResult)
      const result = store.calculatorResults.find(r => r.id === resultId)
      if (result) {
        await DashboardSync.syncItemToDatabase(result, 'update')
      }
    },

    // Text item sync functions
    addTextItem: async (textItem: TextItem) => {
      store.addTextItem(textItem)
      await DashboardSync.syncItemToDatabase(textItem, 'create')
    },

    updateTextItem: async (textId: string, updatedText: Partial<TextItem>) => {
      store.updateTextItem(textId, updatedText)
      const textItem = store.textItems.find(t => t.id === textId)
      if (textItem) {
        await DashboardSync.syncItemToDatabase(textItem, 'update')
      }
    },

    removeTextItem: async (textId: string) => {
      const textItem = store.textItems.find(t => t.id === textId)
      store.removeTextItem(textId)
      if (textItem) {
        await DashboardSync.syncItemToDatabase(textItem, 'delete')
      }
    },

    // Override remove functions to sync with database
    removeChart: async (chartId: string) => {
      const chart = store.charts.find(c => c.id === chartId)
      store.removeChart(chartId)
      if (chart) {
        await DashboardSync.syncItemToDatabase(chart, 'delete')
      }
    },
    
    removeTable: async (tableId: string) => {
      const table = store.tables.find(t => t.id === tableId)
      store.removeTable(tableId)
      if (table) {
        await DashboardSync.syncItemToDatabase(table, 'delete')
      }
    },
    
    removePlot: async (plotId: string) => {
      const plot = store.plots.find(p => p.id === plotId)
      store.removePlot(plotId)
      if (plot) {
        await DashboardSync.syncItemToDatabase(plot, 'delete')
      }
    },
    
    removeCalculatorResult: async (resultId: string) => {
      const result = store.calculatorResults.find(r => r.id === resultId)
      store.removeCalculatorResult(resultId)
      if (result) {
        await DashboardSync.syncItemToDatabase(result, 'delete')
      }
    }
  }
}
