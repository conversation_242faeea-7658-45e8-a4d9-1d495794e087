"use client"

import React, { useState, useEffect } from "react";
import { defaultProps } from "@blocknote/core";
import { createReactBlockSpec } from "@blocknote/react";
import { Cell } from "../Cell";
import { Database } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";
import { nanoid } from "nanoid";
import { Dataset } from "@/types/index";
// import { initDatabase, loadData, executeQuery, closeDatabase } from '@/lib/sqlUtils';
import { setupTable, executeQuery as alasqlExecuteQuery } from '@/lib/alasql-utils';

// Add the GraphicWalker comment detection function
function hasGraphicWalkerComment(code: string): boolean {
  return code.includes("--#graphicwalker") ||
         code.includes("-- #graphicwalker") ||
         code.includes("--loopchart") ||
         code.includes("-- loopchart");
}

// Mock datasets for demonstration - adjusted to match required structure
const mockDatasets: Dataset[] = [
  {
    id: "ds1",
    name: "HR Data",
    data: [
      { id: 1, name: "<PERSON>", department: "HR", salary: 45000, age: 32 },
      { id: 2, name: "Sarah", department: "HR", salary: 52000, age: 38 },
      { id: 3, name: "Mike", department: "HR", salary: 38000, age: 27 },
      { id: 4, name: "Adel", department: "IT", salary: 65000, age: 34 },
      { id: 5, name: "David", department: "Marketing", salary: 72000, age: 41 },
      { id: 6, name: "mal", department: "Slaes", salary: 38000, age: 27 },
      { id: 7, name: "Lee", department: "UX", salary: 65000, age: 34 },
      { id: 8, name: "David", department: "3d", salary: 72000, age: 41 },
    ],
    columns: [
      { name: "id", type: "number" },
      { name: "name", type: "string" },
      { name: "department", type: "string" },
      { name: "salary", type: "number" },
      { name: "age", type: "number" },
      { name: "department", type: "string" },
      { name: "salary", type: "number" },
      { name: "age", type: "number" }
    ],
    headers: ["ID", "Name", "Department", "Salary", "Age"],
    fileType: "csv",
    createdAt: new Date()
  },
  {
    id: "ds2",
    name: "Payroll Summary",
    data: [
      { month: "January", employees: 150, total_salary: 750000, avg_salary: 5000 },
      { month: "February", employees: 152, total_salary: 760000, avg_salary: 5000 },
      { month: "March", employees: 155, total_salary: 775000, avg_salary: 5000 },
      { month: "April", employees: 158, total_salary: 790000, avg_salary: 5000 },
      { month: "May", employees: 160, total_salary: 800000, avg_salary: 5000 },
    ],
    columns: [
      { name: "month", type: "string" },
      { name: "employees", type: "number" },
      { name: "total_salary", type: "number" },
      { name: "avg_salary", type: "number" }
    ],
    headers: ["Month", "Employees", "Total Salary", "Average Salary"],
    fileType: "csv",
    createdAt: new Date()
  }
];

// Add at the top of the file, after imports
const VALID_LANGUAGES = ["sql", "python", "javascript"] as const;
type ValidLanguage = typeof VALID_LANGUAGES[number];

const VALID_CHART_TYPES = ["line", "bar", "pie", "area"] as const;
type ValidChartType = typeof VALID_CHART_TYPES[number];

const VALID_VIEW_MODES = ["table", "chart", "output", "plots", "graphicwalker"] as const;
type ValidViewMode = typeof VALID_VIEW_MODES[number];

// Data Analysis Block using Cell component
export const DataAnalysisBlock = createReactBlockSpec(
  {
    type: "dataAnalysis",
    propSchema: {
      // Include default styling props
      textAlignment: defaultProps.textAlignment,
      textColor: defaultProps.textColor,
      notes: {
        default: "[]",
      },
      analysisType: {
        default: "code",
        values: ["code", "table", "chart"],
      },
      // Store the Cell state
      savedQuery: {
        default: "",
      },
      language: {
        default: "sql",
        values: ["sql", "python", "javascript"] as const,
      },
      cellId: {
        default: "",
      },
      datasetIds: {
        default: "[]",
      },
      // Store execution results
      resultData: {
        default: "[]",
      },
      resultOutput: {
        default: "",
      },
      resultPlots: {
        default: "[]",
      },
      hasError: {
        default: false,
      },
      errorMessage: {
        default: "",
      },
      // Add GraphicWalker flag
      showGraphicWalker: {
        default: false,
      },
      // Add chart configuration
      chartType: {
        default: "bar",
      },
      chartConfig: {
        default: "{}",
      },
      // Add viewMode to stored props
      viewMode: {
        default: "table",
      },
    },
    content: "none",
  },
  {
    render: (props) => {
      // State for cell management
      const [isRunning, setIsRunning] = useState(false);
      const [cellId] = useState(() => props.block.props.cellId || nanoid());
      const [language, setLanguage] = useState<ValidLanguage>(
        (VALID_LANGUAGES.includes(props.block.props.language as ValidLanguage) 
          ? props.block.props.language 
          : "sql") as ValidLanguage
      );
      const [chartType, setChartType] = useState<ValidChartType>(
        (VALID_CHART_TYPES.includes(props.block.props.chartType as ValidChartType)
          ? props.block.props.chartType
          : "bar") as ValidChartType
      );

      // State for dataset management
      const [availableDatasets, setAvailableDatasets] = useState<Dataset[]>([]);
      const [selectedDatasets, setSelectedDatasets] = useState<Dataset[]>([]);
      const [isLoadingDatasets, setIsLoadingDatasets] = useState(false);

      // State for results
      const [result, setResult] = useState<any>(null);
      const [isSuccess, setIsSuccess] = useState<boolean>(false);
      const [showGraphicWalker, setShowGraphicWalker] = useState<boolean>(props.block.props.showGraphicWalker || false);

      // Add state for view mode
      const [viewMode, setViewMode] = useState<ValidViewMode>(
        (VALID_VIEW_MODES.includes(props.block.props.viewMode as ValidViewMode)
          ? props.block.props.viewMode
          : "table") as ValidViewMode
      );

      // Save the cellId to the block props on first render
      useEffect(() => {
        if (!props.block.props.cellId) {
          props.editor.updateBlock(props.block, {
            props: {
              ...props.block.props,
              cellId
            },
          });
        }
      }, [cellId, props.block.props.cellId, props.editor, props.block]);

      // Load datasets from API on component mount
      useEffect(() => {
        const fetchDatasets = async () => {
          setIsLoadingDatasets(true);
          try {
            // Try to fetch from API first
            const response = await fetch('/api/datasets');
            if (response.ok) {
              const data = await response.json();
              if (data && !Array.isArray(data) && Array.isArray(data.datasets)) {
                setAvailableDatasets(data.datasets);
              } else if (Array.isArray(data)) {
                setAvailableDatasets(data);
              } else {
                // Fall back to mock data if API response is not in expected format
                setAvailableDatasets(mockDatasets);
              }
            } else {
              // Fall back to mock data if API call fails
              setAvailableDatasets(mockDatasets);
            }
          } catch (error) {
            console.error('Failed to fetch datasets:', error);
            // Use mock data as fallback
            setAvailableDatasets(mockDatasets);
          } finally {
            setIsLoadingDatasets(false);
          }
        };

        fetchDatasets();
      }, []);

      // Load saved results from block props
      useEffect(() => {
        try {
          if (props.block.props.resultData && props.block.props.resultData !== '[]') {
            const parsedData = JSON.parse(props.block.props.resultData);
            const resultObj = {
              data: parsedData,
              output: props.block.props.resultOutput || '',
              plots: props.block.props.resultPlots ? JSON.parse(props.block.props.resultPlots) : [],
              error: props.block.props.hasError ? props.block.props.errorMessage : undefined
            };
            setResult(resultObj);
            setIsSuccess(!props.block.props.hasError);
          }
        } catch (error) {
          console.error('Failed to parse saved results:', error);
        }
      }, [props.block.props]);

      // Update selected datasets based on datasetIds
      useEffect(() => {
        try {
          // Parse the datasetIds from props
          let datasetIds: string[] = [];
          if (props.block.props.datasetIds) {
            try {
              datasetIds = JSON.parse(props.block.props.datasetIds);
            } catch (e) {
              console.error('Failed to parse datasetIds:', e);
              datasetIds = [];
            }
          }

          if (availableDatasets.length > 0) {
            // Find all datasets that match the IDs
            const datasets = datasetIds
              .map(id => availableDatasets.find(d => d.id === id))
              .filter((d): d is Dataset => d !== undefined);

            // Only update if we found matching datasets
            if (datasets.length > 0) {
              setSelectedDatasets(datasets);
            }
          }
        } catch (error) {
          console.error('Error updating selected datasets:', error);
        }
      }, [props.block.props.datasetIds, availableDatasets]);

      // Run query function
      const handleRunCell = async (id: string, code: string, shouldShowGraphicWalker: boolean = false): Promise<void> => {
        try {
          // Validate input
          if (!code || typeof code !== 'string') {
            console.error('Invalid code input:', code);
            return;
          }

          setIsRunning(true);
          setIsSuccess(false);

          // Check for command in the SQL code
          if (language === 'sql') {
            shouldShowGraphicWalker = shouldShowGraphicWalker ||
                                     hasGraphicWalkerComment(code);
          }

          // Update GraphicWalker flag state
          setShowGraphicWalker(shouldShowGraphicWalker);

          try {
            // For Python, allow execution without dataset selection
            if (!selectedDatasets || selectedDatasets.length === 0 && language !== 'python') {
              toast.error('Please select at least one dataset first');
              return;
            }

            let endpoint;
            let payload;
            let response;

            // Determine which API endpoint to use based on language
            switch (language) {
              case 'python':
                endpoint = '/api/execute';
                payload = {
                  code,
                  datasetId: selectedDatasets.length > 0 ? selectedDatasets[0].id : '', // Keep for backward compatibility
                  datasetIds: selectedDatasets.map(ds => ds.id), // Send all selected dataset IDs
                  datasets: selectedDatasets.map(ds => ({ // Send actual dataset data
                    id: ds.id,
                    name: ds.name,
                    data: ds.data
                  })),
                  language: 'python'
                };
                console.log('Sending Python payload with multiple datasets:', payload);
                break;
              case 'javascript':
                endpoint = '/api/execute-js';
                payload = {
                  code,
                  datasetId: selectedDatasets[0]?.id,
                  data: selectedDatasets[0]?.data,
                  datasets: selectedDatasets.reduce((acc, dataset, index) => {
                    acc[`dataset${index + 1}`] = dataset.data;
                    return acc;
                  }, {} as Record<string, any>)
                };
                break;
              case 'sql':
                endpoint = '/api/query';
                payload = {
                  query: code.trim(),
                  datasetId: selectedDatasets[0]?.id,
                  datasets: selectedDatasets.reduce((acc, dataset, index) => {
                    acc[`dataset${index + 1}`] = dataset.data;
                    return acc;
                  }, {} as Record<string, any>)
                };
                break;
              default:
                throw new Error(`Unsupported language: ${language}`);
            }

            try {
              // First try to use the real API
              response = await fetch(endpoint, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload)
              });

              if (!response.ok) {
                throw new Error('API response not OK');
              }

              const rawResult = await response.json();

              // Process the result
              const processedResult = {
                data: Array.isArray(rawResult) ? rawResult :
                      rawResult.data ? rawResult.data :
                      language === 'python' && rawResult.output ? [] : // For Python, if we have output but no data, return empty array
                      [rawResult],
                output: rawResult.output || '',
                plots: rawResult.plots || []
              };

              // For Python, ensure output is displayed in the output tab
              if (language === 'python' && processedResult.output) {
                setViewMode('output');
                props.editor.updateBlock(props.block, {
                  props: {
                    ...props.block.props,
                    viewMode: 'output'
                  }
                });
              }

              setResult(processedResult);
              setIsSuccess(true);

              // Save results to block props, including GraphicWalker flag
              props.editor.updateBlock(props.block, {
                props: {
                  ...props.block.props,
                  savedQuery: typeof code === 'string' ? code : '',
                  language,
                  resultData: JSON.stringify(processedResult.data),
                  resultOutput: processedResult.output,
                  resultPlots: JSON.stringify(processedResult.plots),
                  hasError: false,
                  errorMessage: '',
                  showGraphicWalker: shouldShowGraphicWalker
                },
              });

              toast.success(`${language} executed successfully`);

            } catch (error) {
              console.log('API error, trying client-side execution:', error);

              // Try client-side execution with sql.js before falling back to mock data
              if (language === 'sql') {
                try {
                  // Setup tables for all selected datasets
                  selectedDatasets.forEach((dataset, index) => {
                    const tableName = index === 0 ? 'dataset' : `dataset${index + 1}`;
                    setupTable(tableName, dataset.data);
                  });

                  // Execute the query
                  const result = alasqlExecuteQuery(code);

                  const clientResult = {
                    data: result,
                    output: `Query executed successfully (client-side). Returned ${result.length} rows.`
                  };

                  setResult(clientResult);
                  setIsSuccess(true);

                  // Save results to block props
                  props.editor.updateBlock(props.block, {
                    props: {
                      ...props.block.props,
                      savedQuery: typeof code === 'string' ? code : '',
                      language,
                      resultData: JSON.stringify(clientResult.data),
                      resultOutput: clientResult.output,
                      resultPlots: JSON.stringify([]),
                      hasError: false,
                      errorMessage: '',
                      showGraphicWalker: shouldShowGraphicWalker
                    },
                  });

                  toast.success('SQL executed client-side successfully');
                  setIsRunning(false);
                  return;
                } catch (sqlError: any) {
                  console.error('Client-side SQL execution failed:', sqlError);
                  // Continue to mock data fallback
                }
              }

              // Fall back to mock data if API fails and client-side execution fails
              let mockResult;

              // Use the first dataset for mock data
              const primaryDataset = selectedDatasets[0];

              if (language === 'sql') {
                mockResult = {
                  data: primaryDataset.data.slice(0, 3),
                  output: `Executed SQL query successfully. Found ${primaryDataset.data.length} records.`
                };
              } else if (language === 'python') {
                mockResult = {
                  data: primaryDataset.data,
                  // Fix for the linter error by checking if columns exists
                  output: `Executed Python code successfully.\nDataset shape: (${primaryDataset.data.length}, ${primaryDataset?.columns?.length || Object.keys(primaryDataset.data[0] || {}).length})`
                };
              } else {
                mockResult = {
                  data: primaryDataset.data.map(item => ({...item, computed: Math.random() * 100})),
                  output: "JavaScript execution completed."
                };
              }

              setResult(mockResult);
              setIsSuccess(true);

              // Save results to block props
              props.editor.updateBlock(props.block, {
                props: {
                  ...props.block.props,
                  savedQuery: typeof code === 'string' ? code : '',
                  language,
                  resultData: JSON.stringify(mockResult.data),
                  resultOutput: mockResult.output,
                  resultPlots: JSON.stringify([]),
                  hasError: false,
                  errorMessage: '',
                  showGraphicWalker: shouldShowGraphicWalker
                },
              });

              toast.success(`${language} executed with mock data`);
            }

          } catch (error) {
            console.error('Execution error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Execution failed';

            setResult({
              data: [],
              output: '',
              error: errorMessage
            });

            setIsSuccess(false);

            // Save error to block props
            props.editor.updateBlock(props.block, {
              props: {
                ...props.block.props,
                savedQuery: typeof code === 'string' ? code : '',
                language,
                hasError: true,
                errorMessage: errorMessage,
                showGraphicWalker: shouldShowGraphicWalker
              },
            });

            toast.error(errorMessage);
          } finally {
            setIsRunning(false);
          }
        } catch (error) {
          console.error('Error in handleRunCell:', error);
        }
      };

      // Handle multiple dataset selection
      const handleSelectDatasets = async (datasetIds: string[]): Promise<void> => {
        try {
          // Keep track of existing selections to avoid duplicates
          const existingIds = new Set(selectedDatasets.map(ds => ds.id));
          const newDatasetsList: Dataset[] = [...selectedDatasets];

          // Process each dataset ID, but only add new ones
          for (const datasetId of datasetIds) {
            if (!existingIds.has(datasetId)) {
              // First try to fetch dataset details from API
              try {
                const response = await fetch(`/api/datasets?datasetId=${datasetId}`);
                if (response.ok) {
                  const data = await response.json();
                  if (data.success && data.datasetInfo) {
                    const dataset: Dataset = {
                      id: datasetId,
                      name: data.datasetInfo.name,
                      data: data.datasetInfo.data,
                      columns: data.datasetInfo.columns,
                      headers: data.datasetInfo.headers,
                      fileType: data.datasetInfo.fileType,
                      createdAt: new Date(data.datasetInfo.createdAt)
                    };
                    newDatasetsList.push(dataset);
                    existingIds.add(datasetId);
                  }
                }
              } catch (error) {
                // Fall back to mock data if API fails
                console.log('API error, using mock data instead:', error);
                const dataset = availableDatasets.find(d => d.id === datasetId);
                if (dataset && !existingIds.has(dataset.id)) {
                  newDatasetsList.push(dataset);
                  existingIds.add(dataset.id);
                }
              }
            }
          }

          // If we found any datasets, update the state
          if (newDatasetsList.length > 0) {
            setSelectedDatasets(newDatasetsList);

            // Update datasetIds in block props
            props.editor.updateBlock(props.block, {
              props: {
                ...props.block.props,
                datasetIds: JSON.stringify(datasetIds)
              },
            });

            const datasetNames = newDatasetsList.map(d => d.name).join(', ');
            toast.success(`Datasets selected: ${datasetNames}`);
          } else {
            throw new Error('No datasets found');
          }
        } catch (error) {
          console.error('Failed to select datasets:', error);
          toast.error('Failed to select datasets');
        }
      };

      // Handle delete cell (do nothing but prevent errors)
      const handleDeleteCell = (id: string): void => {
        // In this context, we don't actually delete the cell
        toast.info("Cell deletion is disabled in the note context");
      };

      // Handle add cell (do nothing but prevent errors)
      const handleAddCell = (id: string): void => {
        // In this context, we don't add new cells
        toast.info("Adding cells is disabled in the note context");
      };

      // Handle language change
      const handleLanguageChange = (newLanguage: string): void => {
        // Validate and convert the language type
        const validLanguage = (["sql", "python", "javascript"].includes(newLanguage) 
          ? newLanguage 
          : "sql") as "sql" | "python" | "javascript";

        // Update local state with validated language
        setLanguage(validLanguage);

        // Use setTimeout to avoid race conditions
        setTimeout(() => {
          try {
            props.editor.updateBlock(props.block, {
              props: {
                ...props.block.props,
                language: validLanguage
              },
            });
          } catch (error) {
            console.error('Failed to update language in BlockNote:', error);
          }
        }, 0);
      };

      // Add handler for chart type changes
      const handleChartTypeChange = (type: string) => {
        // Validate and convert the chart type
        const validChartType = (VALID_CHART_TYPES.includes(type as ValidChartType)
          ? type
          : "bar") as ValidChartType;

        // First update local state
        setChartType(validChartType);

        // Use setTimeout to avoid race conditions
        setTimeout(() => {
          try {
            props.editor.updateBlock(props.block, {
              props: {
                ...props.block.props,
                chartType: type
              },
            });
          } catch (error) {
            console.error('Failed to update chartType in BlockNote:', error);
          }
        }, 0);
      };

      // Add handler for view mode changes
      const handleViewModeChange = (mode: string) => {
        // Validate and convert the view mode
        const validViewMode = (VALID_VIEW_MODES.includes(mode as ValidViewMode)
          ? mode
          : "table") as ValidViewMode;

        // First update local state
        setViewMode(validViewMode);

        // Use a small delay before updating the block props to avoid race conditions
        setTimeout(() => {
          try {
            props.editor.updateBlock(props.block, {
              props: {
                ...props.block.props,
                viewMode: mode
              },
            });
          } catch (error) {
            console.error('Failed to update viewMode in BlockNote:', error);
          }
        }, 0);
      };

      return (
        <div className="relative w-full">
          <Cell
                id={cellId}
                content={props.block.props.savedQuery || (language === 'python' ? 
                  "# Write your Python code here\nimport pandas as pd\nimport numpy as np\n\n# Your code here" : 
                  "SELECT * FROM dataset LIMIT 5")}
                result={{
                  data: result?.data || [],
                  output: result?.output || '',
                  plots: result?.plots || [],
                  error: result?.error
                }}
                onRun={handleRunCell}
                onDelete={handleDeleteCell}
                onAddCell={handleAddCell}
                onSelectDatasets={async (ids) => {
                  if (ids.length > 0) {
                    try {
                      await handleSelectDatasets(ids);
                      return Promise.resolve();
                    } catch (error) {
                      console.error('Failed to select datasets:', error);
                      toast.error('Failed to select datasets');
                      return Promise.reject(error);
                    }
                  }
                  return Promise.resolve();
                }}
                language={language}
                onLanguageChange={handleLanguageChange}
                selectedDatasets={selectedDatasets}
                availableDatasets={availableDatasets || []}
                isSuccess={isSuccess}
                showGraphicWalker={showGraphicWalker}
                chartType={chartType as 'line' | 'bar' | 'pie' | 'area'}
                onChartTypeChange={handleChartTypeChange}
                // @ts-ignore
                viewMode={viewMode}
                onViewModeChange={handleViewModeChange}
                onContentChange={(value) => {
                  if (typeof value === 'string') {
                    setTimeout(() => {
                      try {
                        props.editor.updateBlock(props.block, {
                          props: {
                            ...props.block.props,
                            savedQuery: value
                          }
                        });
                      } catch (error) {
                        console.error('Failed to update content in BlockNote:', error);
                      }
                    }, 0);
                  }
                }}
                notes={props.block.props.notes || "[]"}
                onUpdateNotes={(id, notesContent) => {
                  if (props.block.props.notes !== notesContent) {
                    try {
                      props.editor.updateBlock(props.block, {
                        props: {
                          ...props.block.props,
                          notes: notesContent
                        }
                      });
                    } catch (error) {
                      console.error('Failed to update notes in BlockNote:', error);
                      toast.error("Failed to save notes");
                    }
                  }
                }}
              />
        </div>
      );
    },
  }
);