'use client'

import { create } from 'zustand'
import { Saved<PERSON>hart, TableItem, PythonPlotItem, CalculatorResultItem, TextItem } from '@/components/ChartBuilder/DashboardSection/types'

// Define the store state type
interface DashboardStore {
  // State
  charts: SavedChart[]
  tables: TableItem[]
  plots: PythonPlotItem[]
  calculatorResults: CalculatorResultItem[]
  textItems: TextItem[]

  // Workspace context for database sync
  workspaceId: string | null
  dashboardId: string | null

  // Actions
  addChart: (chart: SavedChart) => void
  updateChart: (chartId: string, updatedChart: Partial<SavedChart>) => void
  removeChart: (chartId: string) => void

  addTable: (table: TableItem) => void
  updateTable: (tableId: string, updatedTable: Partial<TableItem>) => void
  removeTable: (tableId: string) => void

  addPlot: (plot: PythonPlotItem) => void
  updatePlot: (plotId: string, updatedPlot: Partial<PythonPlotItem>) => void
  removePlot: (plotId: string) => void

  addCalculatorResult: (calculatorResult: CalculatorResultItem) => void
  updateCalculatorResult: (resultId: string, updatedResult: Partial<CalculatorResultItem>) => void
  removeCalculatorResult: (resultId: string) => void

  addTextItem: (textItem: TextItem) => void
  updateTextItem: (textId: string, updatedText: Partial<TextItem>) => void
  removeTextItem: (textId: string) => void

  clearAll: () => void
  loadFromDatabase: (items: any[]) => void
  setWorkspaceContext: (workspaceId: string | null, dashboardId: string | null) => void
  getWorkspaceContext: () => { workspaceId: string | null; dashboardId: string | null }
}

// Create the store without persistence (will be handled by workspace API)
export const useDashboardStore = create<DashboardStore>()((set, get) => ({
  // Initial state
  charts: [],
  tables: [],
  plots: [],
  calculatorResults: [],
  textItems: [],
  workspaceId: null,
  dashboardId: null,

      // Chart actions
      addChart: (chart) => set((state) => {
        // Check if chart with this ID already exists
        const existingIndex = state.charts.findIndex(c => c.id === chart.id)

        if (existingIndex >= 0) {
          // Update existing chart but preserve position and size
          const updatedCharts = [...state.charts]
          updatedCharts[existingIndex] = {
            ...chart,
            gridColumn: state.charts[existingIndex].gridColumn,
            gridRow: state.charts[existingIndex].gridRow,
            width: state.charts[existingIndex].width,
            height: state.charts[existingIndex].height
          }
          return { charts: updatedCharts }
        } else {
          // Add new chart
          return { charts: [...state.charts, chart] }
        }
      }),

      updateChart: (chartId, updatedChart) => set((state) => ({
        charts: state.charts.map(chart =>
          chart.id === chartId
            ? {
                ...chart,
                ...updatedChart,
                // If config was updated, merge it
                config: updatedChart.config
                  ? { ...chart.config, ...updatedChart.config }
                  : chart.config,
                // Ensure type is always 'chart'
                type: 'chart'
              }
            : chart
        )
      })),

      removeChart: (chartId) => set((state) => ({
        charts: state.charts.filter(chart => chart.id !== chartId)
      })),

      // Table actions
      addTable: (table) => set((state) => {
        // Check if table with this ID already exists
        const existingIndex = state.tables.findIndex(t => t.id === table.id)

        if (existingIndex >= 0) {
          // Update existing table but preserve position and size
          const updatedTables = [...state.tables]
          updatedTables[existingIndex] = {
            ...table,
            gridColumn: state.tables[existingIndex].gridColumn,
            gridRow: state.tables[existingIndex].gridRow,
            width: state.tables[existingIndex].width,
            height: state.tables[existingIndex].height
          }
          return { tables: updatedTables }
        } else {
          // Add new table
          return { tables: [...state.tables, table] }
        }
      }),

      updateTable: (tableId, updatedTable) => set((state) => ({
        tables: state.tables.map(table =>
          table.id === tableId
            ? { ...table, ...updatedTable, type: 'table' }
            : table
        )
      })),

      removeTable: (tableId) => set((state) => ({
        tables: state.tables.filter(table => table.id !== tableId)
      })),

      // Plot actions
      addPlot: (plot) => set((state) => {
        // Check if plot with this ID already exists
        const existingIndex = state.plots.findIndex(p => p.id === plot.id)

        if (existingIndex >= 0) {
          // Update existing plot but preserve position and size
          const updatedPlots = [...state.plots]
          updatedPlots[existingIndex] = {
            ...plot,
            gridColumn: state.plots[existingIndex].gridColumn,
            gridRow: state.plots[existingIndex].gridRow,
            width: state.plots[existingIndex].width,
            height: state.plots[existingIndex].height
          }
          return { plots: updatedPlots }
        } else {
          // Add new plot
          return { plots: [...state.plots, plot] }
        }
      }),

      updatePlot: (plotId, updatedPlot) => set((state) => ({
        plots: state.plots.map(plot =>
          plot.id === plotId
            ? { ...plot, ...updatedPlot, type: 'pythonplot' }
            : plot
        )
      })),

      removePlot: (plotId) => set((state) => ({
        plots: state.plots.filter(plot => plot.id !== plotId)
      })),

      // Calculator result actions
      addCalculatorResult: (calculatorResult) => set((state) => {
        // Check if calculator result with this ID already exists
        const existingIndex = state.calculatorResults.findIndex(r => r.id === calculatorResult.id)

        if (existingIndex >= 0) {
          // Update existing result but preserve position and size
          const updatedResults = [...state.calculatorResults]
          updatedResults[existingIndex] = {
            ...calculatorResult,
            gridColumn: state.calculatorResults[existingIndex].gridColumn,
            gridRow: state.calculatorResults[existingIndex].gridRow,
            width: state.calculatorResults[existingIndex].width,
            height: state.calculatorResults[existingIndex].height
          }
          return { calculatorResults: updatedResults }
        } else {
          // Add new calculator result
          return { calculatorResults: [...state.calculatorResults, calculatorResult] }
        }
      }),

      updateCalculatorResult: (resultId, updatedResult) => set((state) => ({
        calculatorResults: state.calculatorResults.map(result =>
          result.id === resultId
            ? { ...result, ...updatedResult, type: 'calculator' }
            : result
        )
      })),

      removeCalculatorResult: (resultId) => set((state) => ({
        calculatorResults: state.calculatorResults.filter(result => result.id !== resultId)
      })),

      // Text item actions
      addTextItem: (textItem) => set((state) => ({
        textItems: [...state.textItems, { ...textItem, type: 'text' }]
      })),

      updateTextItem: (textId, updatedText) => set((state) => ({
        textItems: state.textItems.map(text =>
          text.id === textId
            ? { ...text, ...updatedText, type: 'text' }
            : text
        )
      })),

      removeTextItem: (textId) => set((state) => ({
        textItems: state.textItems.filter(text => text.id !== textId)
      })),

      // Clear all items
      clearAll: () => set({ charts: [], tables: [], plots: [], calculatorResults: [], textItems: [] }),

      // Load items from database with enhanced position preservation
      loadFromDatabase: (items) => {
        const charts: SavedChart[] = []
        const tables: TableItem[] = []
        const plots: PythonPlotItem[] = []
        const calculatorResults: CalculatorResultItem[] = []
        const textItems: TextItem[] = []

        items.forEach((item: any) => {
          // Enhanced position preservation - ensure exact grid coordinates are maintained
          const gridColumn = typeof item.gridColumn === 'number' ? Math.max(0, item.gridColumn) : 0;
          const gridRow = typeof item.gridRow === 'number' ? Math.max(0, item.gridRow) : 0;

          switch (item.type) {
            case 'chart':
              charts.push({
                id: item.id,
                type: 'chart',
                title: item.title || 'Chart',
                description: item.description || '',
                data: item.data || [],
                config: item.config || {},
                gridColumn,
                gridRow,
                width: typeof item.width === 'number' ? Math.max(1, item.width) : 6,
                height: typeof item.height === 'number' ? Math.max(1, item.height) : 4,
                chartType: item.config?.chartType || 'bar',
                createdAt: new Date(item.createdAt || Date.now())
              })
              break
            case 'table':
              tables.push({
                id: item.id,
                type: 'table',
                title: item.title || 'Table',
                description: item.description || '',
                data: item.data || [],
                columns: item.config?.columns || [],
                gridColumn,
                gridRow,
                width: typeof item.width === 'number' ? Math.max(1, item.width) : 6,
                height: typeof item.height === 'number' ? Math.max(1, item.height) : 4,
                createdAt: new Date(item.createdAt || Date.now())
              })
              break
            case 'pythonplot':
              plots.push({
                id: item.id,
                type: 'pythonplot',
                title: item.title || 'Plot',
                description: item.description || '',
                plotUrl: item.data?.plotUrl || '',
                gridColumn,
                gridRow,
                width: typeof item.width === 'number' ? Math.max(1, item.width) : 6,
                height: typeof item.height === 'number' ? Math.max(1, item.height) : 4,
                createdAt: new Date(item.createdAt || Date.now())
              })
              break
            case 'calculator':
              calculatorResults.push({
                id: item.id,
                type: 'calculator',
                title: item.title || 'Result',
                description: item.description || '',
                formula: item.data?.formula || item.config?.formula || '',
                result: item.data?.result || '',
                timestamp: item.timestamp || Date.now(),
                gridColumn,
                gridRow,
                width: typeof item.width === 'number' ? Math.max(1, item.width) : 3,
                height: typeof item.height === 'number' ? Math.max(1, item.height) : 2,
                createdAt: new Date(item.createdAt || Date.now()),
                cellId: item.sourceCellId || undefined,
                resultType: item.data?.resultType || 'text',
                formattedResult: item.data?.formattedResult || undefined,
                icon: item.data?.icon || item.config?.icon || undefined
              })
              break
            case 'text':
              textItems.push({
                id: item.id,
                type: 'text',
                content: item.content || '',
                placeholder: item.placeholder || 'Enter your text here...',
                gridColumn,
                gridRow,
                width: typeof item.width === 'number' ? Math.max(1, item.width) : 4,
                height: typeof item.height === 'number' ? Math.max(1, item.height) : 3,
                textAlign: (item.data?.textAlign || item.config?.textAlign || 'left') as 'left' | 'center' | 'right',
                textStyle: item.data?.textStyle || item.config?.textStyle || 'normal',
                isBold: item.data?.isBold || item.config?.isBold || false,
                isItalic: item.data?.isItalic || item.config?.isItalic || false,
                isUnderline: item.data?.isUnderline || item.config?.isUnderline || false,
                color: item.data?.color || item.config?.color,
                backgroundColor: item.data?.backgroundColor || item.config?.backgroundColor,
                createdAt: new Date(item.createdAt || Date.now())
              })
              break
          }
        })

        set({ charts, tables, plots, calculatorResults, textItems })
      },

      // Workspace context management
      setWorkspaceContext: (workspaceId, dashboardId) => set({ workspaceId, dashboardId }),

      getWorkspaceContext: () => {
        const state = get()
        return { workspaceId: state.workspaceId, dashboardId: state.dashboardId }
      }
    }))

// Legacy compatibility functions to match the old cellResultsStore API
export function addChartResult(chart: SavedChart): void {
  useDashboardStore.getState().addChart(chart)
}

export function addTableResult(table: TableItem): void {
  useDashboardStore.getState().addTable(table)
}

export function addPlotResult(plot: PythonPlotItem): void {
  useDashboardStore.getState().addPlot(plot)
}

export function addCalculatorResult(calculator: CalculatorResultItem): void {
  useDashboardStore.getState().addCalculatorResult(calculator)
}

export function getCharts(): SavedChart[] {
  return useDashboardStore.getState().charts
}

export function getTables(): TableItem[] {
  return useDashboardStore.getState().tables
}

export function getPlots(): PythonPlotItem[] {
  return useDashboardStore.getState().plots
}

export function getCalculatorResults(): CalculatorResultItem[] {
  return useDashboardStore.getState().calculatorResults
}

export function addTextResult(textItem: TextItem): void {
  useDashboardStore.getState().addTextItem(textItem)
}

export function getTextItems(): TextItem[] {
  return useDashboardStore.getState().textItems
}

export function removeResult(id: string, type: 'chart' | 'table' | 'plot' | 'calculator'): void {
  switch (type) {
    case 'chart':
      useDashboardStore.getState().removeChart(id)
      break
    case 'table':
      useDashboardStore.getState().removeTable(id)
      break
    case 'plot':
      useDashboardStore.getState().removePlot(id)
      break
    case 'calculator':
      useDashboardStore.getState().removeCalculatorResult(id)
      break
  }
}

export function clearResults(): void {
  useDashboardStore.getState().clearAll()
}

export function getAllResults() {
  const state = useDashboardStore.getState()
  return {
    charts: state.charts,
    tables: state.tables,
    plots: state.plots,
    calculatorResults: state.calculatorResults
  }
}
