'use client'

import { useState, useRef, useEffect, useMemo } from 'react'
import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Play, Trash2, Plus, Database, ChevronDown, Loader2, Code2, Server, BarChart3, Maximize2, MoveUp, MoveDown, Bold, Italic, List, ListOrdered, Code, Link, Image as ImageIcon, Table as TableIcon, Square, FileText } from "lucide-react"
import Editor from '@monaco-editor/react'
import { useTheme } from 'next-themes'
import { toast } from 'sonner'
import { Table } from "@/components/ui/table"
import { Dataset } from '@/types/index'
import { QueryResult } from './QueryResult'
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select"
import { cn } from "@/lib/utils"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { formatDistanceToNow } from 'date-fns'
import _ from 'lodash'
import { Checkbox } from "@/components/ui/checkbox"
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import { Separator } from "@/components/ui/separator"
import { MarkdownCell } from './MarkdownCell'
import { AIAssistant } from './AIAssistant'

interface ServerStatus {
  status: 'healthy' | 'error' | 'warning'
  message: string
}


interface CellProps {
  id: string
  content: string
  result?: {
    data: any[];
    output?: string;
    plots?: string[];
    error?: string;
    errorDetails?: {
      message: string;
      code?: string;
      stack?: string;
      serverTrace?: string;
    };
    executionTime?: number;
  };
  onRun: (id: string, code: string, shouldShowGraphicWalker?: boolean) => Promise<void>
  onDelete: (id: string) => void
  onAddCell: (id: string, cellType?: 'code' | 'markdown') => void
  onSelectDatasets: (ids: string[]) => Promise<{ selectedList: Dataset[], datasetIds: string[] } | void>
  language: string
  onLanguageChange: (language: string) => void
  isSuccess?: boolean
  selectedDatasets: Dataset[]
  availableDatasets: Dataset[]
  showGraphicWalker?: boolean
  onSaveChart?: (data: any[], config: any, chartType: 'line' | 'bar' | 'pie' | 'area', chartId?: string) => void
  onSaveTable?: (data: any[], columns: string[], tableId?: string) => void
  onSavePlot?: (plotUrl: string, plotTitle: string, plotId?: string) => void
  lastRun?: Date;
  lastUpdate?: Date;
  chartType?: 'line' | 'bar' | 'pie' | 'area'
  onChartTypeChange?: (type: 'line' | 'bar' | 'pie' | 'area') => void
  onContentChange?: (value: string) => void
  viewMode?: 'table' | 'chart' | 'output' | 'graphicwalker'
  onViewModeChange?: (mode: 'table' | 'chart' | 'output' | 'graphicwalker') => void
  notes?: string;
  onUpdateNotes?: (id: string, notes: string) => void;
  cellType?: 'code' | 'markdown';
  onConvertCellType?: (id: string, targetType: 'code' | 'markdown') => void;
  index?: number;
  dragHandleProps?: any;
  onMoveUp?: (id: string) => void;
  onMoveDown?: (id: string) => void;
}

// Add this function to check if the SQL query has the --#graphicwalker comment
function hasGraphicWalkerComment(code: string): boolean {
  return code.includes("--#graphicwalker") || code.includes("-- #graphicwalker");
}

// Add this function to check if the SQL query has the loopchart command
function hasLoopchartCommand(code: string): boolean {
  return code.includes("--loopchart") || code.includes("-- loopchart");
}



export function Cell({
  id,
  content,
  result,
  onRun,
  onDelete,
  onAddCell,
  onSelectDatasets,
  language,
  onLanguageChange,
  isSuccess,
  selectedDatasets,
  availableDatasets,
  showGraphicWalker,
  onSaveChart,
  onSaveTable,
  onSavePlot,
  chartType,
  onChartTypeChange,
  onContentChange,
  viewMode,
  onViewModeChange,
  notes = '[]',
  onUpdateNotes,
  index,
  dragHandleProps,
  onMoveUp,
  onMoveDown,
  cellType = 'code',
  onConvertCellType
}: CellProps) {
  const { theme } = useTheme()
  const [isHovered, setIsHovered] = useState(false)
  const [isRunning, setIsRunning] = useState(false)
  const [isLoadingDatasets, setIsLoadingDatasets] = useState(false)
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const [editorHeight, setEditorHeight] = useState('50px')
  const [serverStatus, setServerStatus] = useState<ServerStatus>({
    status: 'warning',
    message: 'Checking server status...'
  })
  const dropdownRef = useRef<HTMLDivElement>(null)
  const editorRef = useRef<any>(null)
  const [executionTime, setExecutionTime] = useState<{
    startTime?: Date;
    endTime?: Date;
  }>({})
  const updateTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const [isEditingMarkdown, setIsEditingMarkdown] = useState(false)
  const [windowWidth, setWindowWidth] = useState<number>(typeof window !== 'undefined' ? window.innerWidth : 0)

  // Check if data is available for chart view
  const hasChartableData = result?.data && result.data.length > 0 && Object.keys(result.data[0] || {}).some(
    key => typeof result.data[0][key] === 'number'
  );
  
  // Listen for window resize events to trigger editor resize
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
      
      // Force editor to update its layout
      if (editorRef.current) {
        editorRef.current.layout();
      }
    };

    // Handle sidebar toggle events specifically
    const handleSidebarToggle = () => {
      const updateEditorLayout = () => {
        if (editorRef.current) {
          editorRef.current.layout();
        }
      };

      // Initial layout update
      requestAnimationFrame(updateEditorLayout);

      // Update layout again after the parent's transition is likely complete
      setTimeout(() => {
        requestAnimationFrame(updateEditorLayout);
      }, 350); // Parent transition is 300ms, add a small buffer
    };

    window.addEventListener('resize', handleResize);
    window.addEventListener('sidebar-toggle', handleSidebarToggle);
    
    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('sidebar-toggle', handleSidebarToggle);
    };
  }, []);

  // Check server status periodically
  useEffect(() => {
    const checkServerStatus = async () => {
      try {
        const response = await fetch('http://127.0.0.1:8000/health')
        if (response.ok) {
          setServerStatus({
            status: 'healthy',
            message: 'Python server is running'
          })
        } else {
          setServerStatus({
            status: 'warning',
            message: 'Server responded with an error'
          })
        }
      } catch (error) {
        setServerStatus({
          status: 'error',
          message: 'Python server is not running'
        })
      }
    }

    checkServerStatus()
    const interval = setInterval(checkServerStatus, 30000) // Check every 30 seconds
    return () => clearInterval(interval)
  }, [])

  // Auto-adjust editor height based on content
  const handleEditorDidMount = (editor: any) => {
    editorRef.current = editor

    // Initial height adjustment
    const updateHeight = () => {
      const lineCount = editor.getModel().getLineCount()
      const newHeight = Math.min(Math.max(60, lineCount * 18), 250) + 'px'
      setEditorHeight(newHeight)
    }

    // Update height on content change
    editor.onDidChangeModelContent(() => {
      updateHeight()
    })

    updateHeight()
    editor.focus()
  }

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // Add keyboard shortcuts for moving cells
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Only handle keyboard shortcuts when this cell is focused or hovered
      if (!isHovered && !editorRef.current?.hasTextFocus()) return;

      // Alt+ArrowUp to move cell up
      if (event.altKey && event.key === 'ArrowUp' && onMoveUp) {
        event.preventDefault();
        onMoveUp(id);
      }

      // Alt+ArrowDown to move cell down
      if (event.altKey && event.key === 'ArrowDown' && onMoveDown) {
        event.preventDefault();
        onMoveDown(id);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [id, isHovered, onMoveUp, onMoveDown]);

  // Remove the datasets fetch since we now get them from props
  const handleDropdownToggle = () => {
    setIsDropdownOpen(!isDropdownOpen)
  }

  // Add a new state for managing multiple selection
  const [selectedDatasetIds, setSelectedDatasetIds] = useState<string[]>(() => {
    // Initialize with the IDs of the datasets passed to this cell
    return selectedDatasets.map(dataset => dataset.id);
  });

  // Use all available datasets from the database
  const filteredAvailableDatasets = useMemo(() => {
    console.log('Cell: Available datasets:', {
      total: availableDatasets.length,
      datasets: availableDatasets.map(ds => ({ id: ds.id, name: ds.name }))
    });

    return availableDatasets;
  }, [availableDatasets]);

  // Update this function to handle multiple dataset selection
  const handleDatasetSelect = async (datasetId: string, isSelected: boolean) => {
    try {
      let newSelection: string[];

      if (isSelected) {
        // Add to selection
        newSelection = [...selectedDatasetIds, datasetId];
      } else {
        // Remove from selection
        newSelection = selectedDatasetIds.filter(id => id !== datasetId);
      }

      setSelectedDatasetIds(newSelection);
      await onSelectDatasets(newSelection);

      const selectedNames = newSelection.map(id =>
        filteredAvailableDatasets.find(d => d.id === id)?.name || 'Unknown'
      ).join(', ');

      toast.success(`Selected datasets: ${selectedNames}`);
      setIsDropdownOpen(false);
    } catch (error) {
      toast.error('Failed to update dataset selection');
      console.error('Dataset selection error:', error);
    }
  };

  const handleRun = async () => {
    if (!editorRef.current) {
      toast.error('Editor not initialized');
      return;
    }

    const code = editorRef.current.getValue();
    if (!code.trim()) {
      toast.error('Please enter some code before running');
      return;
    }

    // For Python, allow execution without dataset selection (users can load their own data)
    if (selectedDatasetIds.length === 0 && language !== 'python') {
      toast.error('Please select at least one dataset first');
      return;
    }

    // Validate code based on language
    if (language === 'python') {
      // Check if the code contains SQL-like comments or SQL syntax
      if (code.includes('--') || /^SELECT|^INSERT|^UPDATE|^DELETE/i.test(code.trim())) {
        toast.error('This appears to be SQL code but you have Python selected. Please change the language or update your code.');
        return;
      }

      // Check if the code is the default SQL template
      if (code.includes('-- Basic SQL query for a single dataset')) {
        toast.error('You are using the SQL template with Python selected. Please change to SQL or update your code.');
        return;
      }

      // For matplotlib plots, ensure the code includes get_plot() if it uses plt
      if (code.includes('plt.') && !code.includes('get_plot()')) {
        // If the code uses matplotlib but doesn't call get_plot(), show a warning
        toast.warning('Your code uses matplotlib but doesn\'t call get_plot(). Add result = get_plot() to display the plot.');
      }
    }

    setIsRunning(true);
    const startTime = new Date();
    setExecutionTime({ startTime, endTime: undefined });

    // Check for GraphicWalker comment or loopchart command
    const shouldShowGraphicWalker = language === 'sql' &&
      (hasGraphicWalkerComment(code) || hasLoopchartCommand(code));

    try {
      // For Python, ensure we have a valid code that won't cause syntax errors
      let codeToRun = code;
      if (language === 'python' && code.trim().startsWith('--')) {
        // If somehow we still have SQL comments in Python mode, convert them to Python comments
        codeToRun = code.replace(/^--/gm, '#');
      }

      // For Python plots, add debug info and ensure plots are captured
      if (language === 'python') {
        if (code.includes('plt.')) {
          // Add debug print statements to help diagnose plot issues
          codeToRun += '\n\n# Debug info for plots\nif "result" in locals() and hasattr(plt, "get_fignums"):\n    print(f"Number of figures: {len(plt.get_fignums())}")\n    if plt.get_fignums():\n        print("Figures exist but may not be captured if result != get_plot()")\n        # If there are figures but result is not from get_plot(), capture the plot anyway\n        if not isinstance(result, str) or not result.startswith("data:image"):\n            print("Auto-capturing plot since result is not a plot image")\n            result = get_plot()\n';
        }

        // Add a simple working example if the code is just the default
        if (code.trim() === 'import matplotlib.pyplot as plt\nimport numpy as np\n\n\nx = np.linspace(0, 10, 100)\ny = np.sin(x)\n\n\nplt.figure(figsize=(10, 6))\nplt.plot(x, y)\nplt.title(\'Sine Wave\')\nplt.xlabel(\'X\')\nplt.ylabel(\'Y\')\nplt.grid(True)\n\n\nresult = get_plot()') {
          // This is the exact example code - make sure it works by adding tight_layout
          codeToRun = 'import matplotlib.pyplot as plt\nimport numpy as np\n\n# Create data\nx = np.linspace(0, 10, 100)\ny = np.sin(x)\n\n# Create plot\nplt.figure(figsize=(10, 6))\nplt.plot(x, y)\nplt.title(\'Sine Wave\')\nplt.xlabel(\'X\')\nplt.ylabel(\'Y\')\nplt.grid(True)\nplt.tight_layout()  # Add tight_layout for better formatting\n\n# Get the plot\nresult = get_plot()\n\nprint("Plot generated successfully!")\n';
        }
      }

      // Create a safer promise that handles potential errors
      const safePromise = async () => {
        try {
          // Add a timeout to prevent hanging
          const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Execution timed out')), 30000); // 30 second timeout
          });

          // Race the execution against the timeout
          await Promise.race([
            onRun(id, codeToRun, shouldShowGraphicWalker),
            timeoutPromise
          ]);

          // Since onRun returns void, we need to get the result from props
          // after the execution is complete
          return result || { data: [], plots: [] };
        } catch (error) {
          console.error('Execution error in safePromise:', error);
          // Rethrow with a cleaner message
          if (error instanceof Error) {
            if (error.message.includes('Unexpected token')) {
              throw new Error('Server returned an invalid response. The Python server might be down or experiencing issues.');
            }
            throw error;
          }
          throw new Error('Unknown execution error');
        }
      };

      toast.promise(safePromise(), {
        loading: 'Executing code...',
        success: (result) => {
          // Check if plots were generated and switch to output tab if so
          if (result && result.plots && Array.isArray(result.plots) && result.plots.length > 0) {
            // Switch to output tab if we have plots (now unified with output)
            if (onViewModeChange) {
              onViewModeChange('output');
            }
            return 'Code executed successfully. Plot generated!';
          }

          // Check if we're running Python code with plt but no plots were returned
          if (language === 'python' && code.includes('plt.') &&
              (!result || !result.plots || !Array.isArray(result.plots) || result.plots.length === 0)) {
            return 'Code executed, but no plots were captured. Did you use result = get_plot()?';
          }
          return 'Code executed successfully';
        },
        error: (err) => {
          console.error('Toast error handler:', err);
          return `Execution failed: ${err.message}`;
        }
      });

      try {
        await safePromise();
        const endTime = new Date();
        setExecutionTime({ startTime, endTime });
      } catch (error) {
        // Error already handled by toast.promise
        const endTime = new Date();
        setExecutionTime({ startTime, endTime });
      }
    } catch (error) {
      console.error('Execution error:', error);
      const endTime = new Date();
      setExecutionTime({ startTime, endTime });
    } finally {
      setIsRunning(false);
    }
  };

  const getDefaultContent = (lang: string) => {
    switch (lang) {
      case 'python':
        return `# Selected datasets from UI are available as virtual CSV files
# Use pd.read_csv() with standardized names: dataset1.csv, dataset2.csv, etc.
# Available libraries: pandas as pd, numpy as np, matplotlib.pyplot as plt, seaborn as sns

# Check what datasets are available
show_datasets()  # This will show you the exact filenames to use

# Example 1: Load selected datasets using standardized names
df1 = pd.read_csv('dataset1.csv')  # First selected dataset
# df2 = pd.read_csv('dataset2.csv')  # Second selected dataset (if available)

print(f"df1 shape: {df1.shape}")
print(f"df1 columns: {df1.columns.tolist()}")

# Display first dataset
result = df1.head()

# UNCOMMENT ONE OF THESE EXAMPLES:

# Example 2: Compare multiple datasets
"""
# First check available datasets
show_datasets()

# Load multiple datasets
df1 = pd.read_csv('dataset1.csv')
df2 = pd.read_csv('dataset2.csv')

print(f"df1 has {len(df1)} rows and {len(df1.columns)} columns")
print(f"df2 has {len(df2)} rows and {len(df2.columns)} columns")

# Find common columns
common_cols = set(df1.columns) & set(df2.columns)
print(f"Common columns: {list(common_cols)}")

result = pd.concat([df1.head(), df2.head()], keys=['df1', 'df2'])
"""

# Example 3: Merge datasets
"""
df1 = pd.read_csv('dataset1.csv')
df2 = pd.read_csv('dataset2.csv')

# Merge on common column (adjust column name as needed)
if 'id' in df1.columns and 'id' in df2.columns:
    merged = pd.merge(df1, df2, on='id', how='inner')
    print(f"Merged dataset shape: {merged.shape}")
    result = merged.head()
"""

# Example 4: Create comparison plots
"""
df1 = pd.read_csv('dataset1.csv')
df2 = pd.read_csv('dataset2.csv')

# Compare a numeric column across datasets
column_to_compare = 'salary'  # Change this to your column name

if column_to_compare in df1.columns and column_to_compare in df2.columns:
    plt.figure(figsize=(12, 6))
    
    plt.subplot(1, 2, 1)
    plt.hist(df1[column_to_compare].dropna(), bins=20, alpha=0.7)
    plt.title(f'{column_to_compare} - Dataset 1')
    plt.xlabel(column_to_compare)
    plt.ylabel('Frequency')
    
    plt.subplot(1, 2, 2)
    plt.hist(df2[column_to_compare].dropna(), bins=20, alpha=0.7, color='orange')
    plt.title(f'{column_to_compare} - Dataset 2')
    plt.xlabel(column_to_compare)
    plt.ylabel('Frequency')
    
    plt.tight_layout()
    result = get_plot()
"""
`;
      case 'javascript':
        return `// Dataset is available as "df" with helper methods
// Examples:

// Get first 5 rows
result = df.head()

// Basic statistics
// result = df.describe()

// Filter data
// result = df.filter(row => row.age > 25)

// Select columns
// result = df.select(['name', 'age'])

// Count rows
// result = df.count()

// Group by
// result = df.groupBy('category')`;
      default:
        return `-- Basic SQL query for a single dataset
SELECT * FROM dataset1 LIMIT 5;

-- Join multiple datasets
-- SELECT
--   a.employee_id,
--   a.name,
--   b.department,
--   b.salary
-- FROM dataset1 a
-- JOIN dataset2 b ON a.employee_id = b.employee_id;

-- Union data from different datasets
-- SELECT month, sales FROM dataset1
-- UNION ALL
-- SELECT month, sales FROM dataset2;

-- Complex analysis across datasets
-- SELECT
--   d1.department,
--   COUNT(*) AS employee_count,
--   AVG(d2.salary) AS avg_salary,
--   SUM(d3.sales) AS total_sales
-- FROM dataset1 d1
-- JOIN dataset2 d2 ON d1.employee_id = d2.employee_id
-- JOIN dataset3 d3 ON d1.employee_id = d3.employee_id
-- GROUP BY d1.department
-- ORDER BY total_sales DESC;

-- Add --#graphicwalker comment to open the interactive visual explorer
-- --#graphicwalker
-- SELECT * FROM dataset1;`;
    }
  }

  // In the Cell component, update the editor onChange handler to use debouncing
  const debouncedContentChange = useRef(
    _.debounce((value: string) => {
      if (onContentChange) {
        onContentChange(value);
      }
    }, 200)  // 200ms debounce
  ).current;

  // Add a useEffect for cleanup
  useEffect(() => {
    return () => {
      // Clean up the timeout when component unmounts
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
    };
  }, []);

  // Handle notes update
  const handleUpdateNotes = (updatedNotes: string) => {
    if (onUpdateNotes) {
      onUpdateNotes(id, updatedNotes);
    }
  };

  // Update the chart button click handler to avoid redundant state changes
  const handleChartButtonClick = () => {
    // Instead of finding DOM elements, directly update the view mode
    if (onViewModeChange) {
      // If we're already in chart view, don't trigger another update
      if (viewMode !== 'chart') {
        onViewModeChange('chart');
      }
    }
  };

  return (
    <div
      className="relative group my-2"
      data-cell-id={id}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Add Cell Button - Now positioned at the bottom */}
      <div className={cn(
        "absolute -bottom-2 left-1/2 transform -translate-x-1/2 z-20",
        isHovered ? "opacity-100" : "opacity-0"
      )}>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className="h-5 rounded-full p-0 bg-background hover:bg-accent px-1"
            >
              <Plus className="h-3 w-3 mr-0.5" />
              <span className="text-[9px]">Add</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="center">
            <DropdownMenuItem onClick={() => onAddCell(id, 'code')}>
              <Code2 className="h-3 w-3 mr-2" />
              <span className="text-xs">Code Cell</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onAddCell(id, 'markdown')}>
              <FileText className="h-3 w-3 mr-2" />
              <span className="text-xs">Markdown Cell</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <Card className={cn(
        "relative w-full overflow-hidden transition-all duration-300 py-0",
        cellType === 'markdown'
          ? "bg-transparent border-none shadow-none"
          : cn(
              "border",
              isHovered
                ? (result?.error ? "border-red-500" : isSuccess ? "border-green-500" : "border-primary/50")
                : "border-border",
              "shadow-sm"
            )
      )}
      style={{
        width: '100%',
        maxWidth: '100%',
      }}>
        {/* Cell Number Indicator - Always visible with drag handle functionality */}
        <div
          className={cn(
            "absolute left-0 top-0 z-20 flex items-center justify-center h-6 w-6 rounded-br-md text-xs font-mono font-semibold",
            cellType === 'markdown' ? "text-muted-foreground/50" : ""
          )}
          {...(dragHandleProps ? {
            ...dragHandleProps,
            title: '', // Remove the title attribute
            'aria-label': 'Drag to reorder cell' // Keep accessibility but without visible tooltip
          } : {})}
        >
          {typeof index === 'number' ? `[${index + 1}]` : '•'}
        </div>

        {/* Cell Actions - Fixed visibility */}
        <div className={cn(
          "absolute right-1 top-1 z-30 flex items-center gap-0.5",
          cellType === 'markdown'
            ? "bg-background/30 backdrop-blur-sm rounded-md px-0.5 py-0.5"
            : "bg-background/50 backdrop-blur-sm rounded-md px-0.5 py-0.5",
          "transition-opacity duration-200",
          isHovered ? "opacity-100" : "opacity-0 pointer-events-none"
        )}>
          {/* Server Status - Compact version */}
          {cellType !== 'markdown' && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger>
                  <div className="flex items-center gap-1 px-1">
                    <div className={cn(
                      "w-1.5 h-1.5 rounded-full", // Smaller indicator
                      serverStatus.status === 'healthy' ? "bg-green-500" :
                      serverStatus.status === 'warning' ? "bg-yellow-500" :
                      "bg-red-500"
                    )} />
                    <Server className="h-3 w-3 text-muted-foreground" />
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p className="text-xs">
                    {serverStatus.message}
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          {/* Dataset Selector - Compact version */}
          {cellType !== 'markdown' && (
            <div className="relative" ref={dropdownRef}>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 px-1.5 hover:bg-accent flex items-center gap-1"
                onClick={handleDropdownToggle}
              >
                <Database className="h-3 w-3" />
                <span className="text-xs truncate max-w-[80px]">
                  {selectedDatasets.length === 1
                    ? selectedDatasets[0].name
                    : selectedDatasets.length > 1
                      ? `${selectedDatasets.length} datasets`
                      : 'Select Datasets'}
                </span>
                <ChevronDown className="h-3 w-3" />
              </Button>

              {isDropdownOpen && (
                <div className="absolute top-full right-0 mt-1 w-[200px] bg-background rounded-md border shadow-lg z-[100]">
                  <div className="max-h-[200px] overflow-y-auto">
                    {filteredAvailableDatasets.length === 0 ? (
                      <div className="px-2 py-1.5 text-xs text-muted-foreground">
                        {serverStatus.status === 'healthy'
                          ? 'No datasets found in database. Upload some datasets to get started.'
                          : 'No datasets available'
                        }
                      </div>
                    ) : (
                      <div className="py-1">
                        {filteredAvailableDatasets.map(dataset => (
                          <div
                            key={dataset.id}
                            className="flex items-center px-2 py-1 hover:bg-accent hover:text-accent-foreground"
                          >
                            <Checkbox
                              id={`dataset-${dataset.id}`}
                              checked={selectedDatasetIds.includes(dataset.id)}
                              onCheckedChange={(checked) =>
                                handleDatasetSelect(dataset.id, checked === true)
                              }
                              className="mr-2 h-3 w-3"
                            />
                            <label
                              htmlFor={`dataset-${dataset.id}`}
                              className="text-xs flex-1 cursor-pointer"
                            >
                              {dataset.name}
                            </label>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Language Selector - Compact version */}
          {cellType !== 'markdown' && (
            <Select
              value={language}
              onValueChange={(newLang) => {
                try {
                  // When language changes, update the editor content with the default for that language
                  if (editorRef.current && newLang !== language) {
                    const currentContent = editorRef.current.getValue();
                    // Only replace content if it's empty or matches the default for the previous language
                    const defaultForOldLang = getDefaultContent(language);
                    if (!currentContent.trim() || currentContent === defaultForOldLang) {
                      editorRef.current.setValue(getDefaultContent(newLang));
                    }

                    // Clear any previous results when changing languages
                    // This prevents JSON parsing errors when switching languages
                    if (onViewModeChange) {
                      onViewModeChange('table'); // Reset to table view
                    }
                  }

                  // Notify parent about language change
                  onLanguageChange(newLang);

                  // Show a toast to confirm language change
                  toast.success(`Switched to ${newLang.charAt(0).toUpperCase() + newLang.slice(1)} language`);
                } catch (error) {
                  console.error('Error changing language:', error);
                  toast.error('Failed to change language');
                }
              }}
            >
              <SelectTrigger className="h-6 w-[90px] text-xs px-1.5"> {/* Smaller select */}
                <Code2 className="h-3 w-3 mr-1" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="sql" className="text-xs">SQL</SelectItem>
                <SelectItem value="python" className="text-xs">Python</SelectItem>
                <SelectItem value="javascript" className="text-xs">JavaScript</SelectItem>
              </SelectContent>
            </Select>
          )}

          {/* Chart Button - Only visible when chartable data is available */}
          {result && result.data && result.data.length > 0 && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 hover:bg-primary hover:text-primary-foreground"
                    onClick={handleChartButtonClick}
                  >
                    <BarChart3 className="h-3 w-3" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p className="text-xs">View Chart</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          {/* Move Up Button */}
          {onMoveUp && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 hover:bg-accent rounded-full"
                    onClick={() => onMoveUp(id)}
                  >
                    <MoveUp className="h-3 w-3 text-muted-foreground" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p className="text-xs">Move cell up</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          {/* Move Down Button */}
          {onMoveDown && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 hover:bg-accent rounded-full"
                    onClick={() => onMoveDown(id)}
                  >
                    <MoveDown className="h-3 w-3 text-muted-foreground" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p className="text-xs">Move cell down</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          {/* Run Button - Fixed visibility and interaction */}
          {cellType !== 'markdown' && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleRun}
              disabled={isRunning}
              className={cn(
                "h-5 w-5 p-0",
                "hover:bg-accent active:scale-95",
                "transition-transform duration-100"
              )}
            >
              {isRunning ? (
                <Loader2 className="h-3 w-3 animate-spin" />
              ) : (
                <Play className="h-3 w-3" />
              )}
            </Button>
          )}

          {/* Convert Cell Type Button */}
          {onConvertCellType && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 hover:bg-accent rounded-full"
                    onClick={() =>
                      onConvertCellType(id, cellType === 'code' ? 'markdown' : 'code')
                    }
                  >
                    {cellType === 'code'
                      ? <FileText className="h-3 w-3 text-muted-foreground" />
                      : <Code2 className="h-3 w-3 text-muted-foreground" />
                    }
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p className="text-xs">
                    Convert to {cellType === 'code' ? 'markdown' : 'code'} cell
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          {/* Add Edit Markdown button for markdown cells */}
          {cellType === 'markdown' && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 hover:bg-accent rounded-full"
                    onClick={() => setIsEditingMarkdown(!isEditingMarkdown)}
                  >
                    <Bold className="h-3 w-3 text-muted-foreground" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p className="text-xs">
                    {isEditingMarkdown ? "Exit Edit Mode" : "Edit Markdown"}
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          {/* AI Assistant Button - only for code cells */}
          {cellType === 'code' && onContentChange && (
            <div className="ml-2">
              <AIAssistant
                selectedDatasets={selectedDatasets}
                language={language as 'sql' | 'python' | 'javascript' | 'markdown'}
                onCodeGenerated={(code) => {
                  onContentChange(code);
                  toast.success('AI-generated code inserted!');
                }}
                editorRef={editorRef}
              />
            </div>
          )}

          {/* Delete Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onDelete(id)}
            className="h-6 w-6 p-0 hover:bg-destructive hover:text-destructive-foreground" // Square button
          >
            <Trash2 className="h-3 w-3" />
          </Button>
        </div>

        {/* Dataset Info - Even more compact */}
        {(selectedDatasets.length > 0 || cellType === 'code') && (
          <div className="px-1 pt-0 pb-0 text-[9px] text-muted-foreground flex items-center justify-between gap-1">
            <div className="flex items-center gap-1">
              <Database className="h-3 w-3" />
              <span>
               {selectedDatasets.length > 0 ? (
                 selectedDatasets.length === 1
                   ? language === 'sql'
                     ? `Using dataset: ${selectedDatasets[0].name} → SQL table: dataset1`
                     : `Using dataset: ${selectedDatasets[0].name} → pd.read_csv('dataset1.csv')`
                   : language === 'sql'
                     ? `Using ${selectedDatasets.length} datasets → SQL tables: ${selectedDatasets.map((_, idx) => `dataset${idx + 1}`).join(', ')}`
                     : `Using ${selectedDatasets.length} datasets → ${selectedDatasets.map((_, idx) => `pd.read_csv('dataset${idx + 1}.csv')`).join(', ')}`
               ) : language === 'python' ? (
                 'No UI datasets selected - use pd.read_csv() to load your own data'
               ) : language === 'sql' ? (
                 'No datasets selected - select datasets to create SQL tables'
               ) : (
                 'No datasets selected'
               )}
                <span className="text-muted-foreground ml-2">
                  {new Date().toLocaleDateString('en-US', {
                    month: 'short',
                    day: 'numeric',
                    year: 'numeric'
                  })}
                </span>
              </span>
            </div>

            {/* Execution time information - more compact */}
            {executionTime.endTime && (
              <span className="text-[9px] text-muted-foreground">
                {formatDistanceToNow(executionTime.endTime, { addSuffix: true })}
                {executionTime.startTime && executionTime.endTime && (
                  <span className="ml-1">
                    ({((executionTime.endTime.getTime() - executionTime.startTime.getTime()) / 1000).toFixed(1)}s)
                  </span>
                )}
              </span>
            )}
          </div>
        )}

        <div className="transition-colors duration-200">
          {/* Editor Area - Adjusted padding for cell number only */}
          <div className="p-0 pl-6"> {/* Add left padding to accommodate cell number */}
            {cellType === 'markdown' ? (
              <MarkdownCell
                content={content}
                onContentChange={onContentChange}
                isEditing={isEditingMarkdown}
                setIsEditing={setIsEditingMarkdown}
              />
            ) : (
              <Editor
                height={editorHeight}
                defaultLanguage={language}
                defaultValue={content || getDefaultContent(language)}
                theme={theme === 'dark' ? 'vs-dark' : 'light'}
                options={{
                  minimap: { enabled: false },
                  lineNumbers: 'on',
                  folding: false,
                  scrollBeyondLastLine: false,
                  wordWrap: 'on',
                  contextmenu: false,
                  fontSize: 11,
                  lineHeight: 15,
                  padding: { top: 1, bottom: 1 },
                }}
                onMount={handleEditorDidMount}
                onChange={(value) => {
                  if (typeof value === 'string') {
                    debouncedContentChange(value);
                  }
                }}
              />
            )}
          </div>

          {/* Results - More compact */}
          <div className={cn("mt-0 relative", result ? "border-t border-t-border/50" : "")} data-cell-id={id}>
            {result && cellType === 'code' && (
              <QueryResult
                data={result.data}
                output={result.output}
                plots={result.plots}
                // @ts-ignore
                result={result.result}
                error={result.error}
                errorDetails={result.errorDetails}
                executionTime={result.executionTime}
                isSuccess={isSuccess && !result.error}
                showGraphicWalker={showGraphicWalker}
                onSaveChart={onSaveChart}
                onSaveTable={onSaveTable}
                onSavePlot={onSavePlot}
                chartType={chartType}
                onChartTypeChange={onChartTypeChange}
                viewMode={viewMode}
                onViewModeChange={onViewModeChange}
                cellId={id}
                language={language} // Pass language to QueryResult
                // Pass cell control props
                onMoveUp={onMoveUp}
                onMoveDown={onMoveDown}
                notes={notes}
                onUpdateNotes={onUpdateNotes}
              />
            )}

          </div>
        </div>
      </Card>
    </div>
  );
}  