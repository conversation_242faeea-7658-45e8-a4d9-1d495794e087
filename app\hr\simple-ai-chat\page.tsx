"use client"

import React from 'react';
import { useSimpleAIChat } from '@/hooks/useSimpleAIChat';
import SimpleAIChatInterface from '@/components/SimpleAIChat/SimpleAIChatInterface';
import { Card, CardContent } from '@/components/ui/card';
import { Loader2, AlertCircle } from 'lucide-react';

const SimpleAIChatPage: React.FC = () => {
  const {
    datasets,
    selectedDatasets,
    handleDatasetSelectionChange,
  } = useSimpleAIChat();

  return (
    <div className="h-screen">
      <SimpleAIChatInterface
        datasets={datasets}
        selectedDatasets={selectedDatasets}
        onDatasetSelectionChange={handleDatasetSelectionChange}
      />
    </div>
  );
};

export default SimpleAIChatPage;
