Improving the quality of your RAG (Retrieval-Augmented Generation) pipeline usually boils down to three core areas:

1. **How you split and embed your source documents**
2. **How you index and retrieve them at query time**
3. **How you post-process or re-rank before you send into your LLM**

Below are a set of practical tweaks and techniques—complete with LangChain-JS snippets—to help you dig deeper into your data and avoid “I don’t see that value” failures.

---

## 1. Better chunking & embedding

### a) Use an overlap when splitting

If you split your text into non-overlapping chunks, you may lose context that lives on chunk boundaries.

```js
import { RecursiveCharacterTextSplitter } from "langchain/text_splitter";
const splitter = new RecursiveCharacterTextSplitter({
  chunkSize: 1000,
  chunkOverlap: 200,
});
const docs = await splitter.splitDocuments(rawDocs);
```

### b) Tune your embedding model

– Try switching to a higher-quality embedding model (for instance, `openai/text-embedding-3-small` if you can) or experimenting with <PERSON><PERSON>’s embedder (`cohere.minimal`).
– If your data is domain-specific, consider fine-tuning an embedding model on a small set of your own examples.

```js
import { CohereEmbeddings } from "langchain/embeddings/cohere";
const embedder = new CohereEmbeddings({ model: "embed-english-light-v2" });
```

---

## 2. Smarter vector store + retrieval configuration

### a) Increase `k` and/or implement MMR re-ranking

By default most retrievers pull back the top 4 or 5 docs. Bump that up to 10–20, then use Maximal Marginal Relevance to reduce redundancy while keeping diversity:

```js
import { VectorDBQAChain } from "langchain/chains";
import { MMRRetriever } from "langchain/retrievers/mmr";
const baseRetriever = vectorStore.asRetriever({ k: 20 });
const retriever = new MMRRetriever(baseRetriever, { fetchK: 40, lambda: 0.7 });
const qa = VectorDBQAChain.fromLLM(llm, retriever);
```

### b) Hybrid semantic + keyword search

Combine a quick keyword filter (to nail down exact matches) with a semantic ranking step:

```js
// 1) filter by keyword metadata
const filteredDocs = await vectorStore.filterByMetadata({ category: "financial" });
// 2) semantically re-rank that subset
const semRanked = filteredDocs.similaritySearch(query, 10);
```

---

## 3. Post-retrieval checks & fallbacks

### a) In-context document titles & “I don’t see it” prompts

Have your chain include a short diagnostic:

```js
const qaWithCheck = new RetrievalQAChain({
  llm,
  retriever,
  returnSourceDocuments: true,
  prompt: `Use only the following documents. If the answer is not present, say “Not found in source.”\n\n{context}\n\nQuestion: {question}`,
});
```

### b) Fallback to a broader search

If `retriever.getRelevantDocuments()` returns < N documents, run a pure keyword search over the raw text (e.g. via ElasticSearch or a simple regex index), and append those hits as a backup.

---

## 4. Putting it all together: a LangChain-JS pattern

```js
import { OpenAI } from "langchain/llms/openai";
import { CohereEmbeddings } from "langchain/embeddings/cohere";
import { FaissStore } from "langchain/vectorstores/faiss";
import { RecursiveCharacterTextSplitter } from "langchain/text_splitter";
import { MMRRetriever } from "langchain/retrievers/mmr";
import { VectorDBQAChain } from "langchain/chains";

// 1. Split + embed
const splitter = new RecursiveCharacterTextSplitter({ chunkSize: 1000, chunkOverlap: 200 });
const docs = await splitter.splitDocuments(rawDocs);
const embedder = new CohereEmbeddings({ model: "embed-english-light-v2" });
const vectorStore = await FaissStore.fromDocuments(docs, embedder);

// 2. Build MMR retriever
const baseRetriever = vectorStore.asRetriever({ k: 20 });
const retriever = new MMRRetriever(baseRetriever, { fetchK: 40, lambda: 0.5 });

// 3. Build QA chain with “not found” guardrail
const llm = new OpenAI({ modelName: "gpt-4o-mini" });
const qaChain = VectorDBQAChain.fromLLM(llm, retriever, {
  returnSourceDocuments: true,
  promptTemplate: `Use ONLY the following passages. If the answer is NOT in them, reply “Not found.”\n\n{context}\n\nQuestion: {question}`,
});

// 4. Query
const res = await qaChain.call({ question: "What is the ROI for project X?" });
console.log(res.text);
```

---

### ✔️ Checklist to debug further

* **Inspect your chunks**: print out the chunk containing the missing value and run `embedder.embedQuery()` on it manually.
* **Log similarity scores**: test `retriever.getRelevantDocuments()` and inspect the scores to see if your query is too far semantically.
* **Experiment with different split parameters** (smaller vs. larger chunks; more overlap).
* **Try an alternative retriever**: e.g. `Contriever` or `HNSWLibRetriever` if using FAISS/Pinecone.

By iterating on chunk size/overlap, embedding model, retriever parameters (k, fetchK, lambda) and adding a keyword-based fallback, you’ll dramatically boost coverage—so that when your chain says “I don’t see it,” you know it really isn’t there (rather than being a miss of your retrieval layer).
