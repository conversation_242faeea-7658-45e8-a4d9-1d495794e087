import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Search, Plus, Loader2, GitBranch, Eye, Save, Undo2, Redo2 } from "lucide-react";

interface TableControlsProps {
  globalFilter: string;
  setGlobalFilter: (value: string) => void;
  handleAddRow: () => void;
  handleAddColumn: () => void;
  setShowVersionHistory: (show: boolean) => void;
  versions: any[];
  editHistory: any[];
  setShowChangesPreview: (show: boolean) => void;
  handleSaveChanges: () => void;
  rowOperation: {
    type: string;
    loading: boolean;
  };
  onUndo: () => void;
  onRedo: () => void;
  canUndo: boolean;
  canRedo: boolean;
}

export function TableControls({
  globalFilter,
  setGlobalFilter,
  handleAddRow,
  handleAddColumn,
  setShowVersionHistory,
  versions,
  editHistory,
  setShowChangesPreview,
  handleSaveChanges,
  rowOperation,
  onUndo,
  onRedo,
  canUndo,
  canRedo,
}: TableControlsProps) {
  return (
    <div className="flex items-center justify-between gap-4 p-2 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b sticky top-0 z-20">
      <div className="flex items-center gap-4">
        <div className="relative">
          <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search in table..."
            value={globalFilter ?? ""}
            onChange={(event) => setGlobalFilter(event.target.value)}
            className="pl-9 w-[250px] h-9 text-xs"
          />
        </div>
        
        <div className="flex items-center gap-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="h-9"
                  onClick={handleAddRow}
                  disabled={rowOperation.loading && rowOperation.type === 'add'}
                >
                  {rowOperation.loading && rowOperation.type === 'add' ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Adding...
                    </>
                  ) : (
                    <>
                      <Plus className="h-4 w-4 mr-2" />
                      Add Row
                    </>
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent>Add new row to table</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="h-9"
                  onClick={handleAddColumn}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Column
                </Button>
              </TooltipTrigger>
              <TooltipContent>Add new column to table</TooltipContent>
            </Tooltip>
          </TooltipProvider>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowVersionHistory(true)}
            className="h-9"
          >
            <GitBranch className="h-4 w-4 mr-2" />
            History
            {versions.length > 0 && (
              <Badge variant="secondary" className="ml-1 h-5">
                {versions.length}
              </Badge>
            )}
          </Button>
        </div>

        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="icon"
            onClick={onUndo}
            disabled={!canUndo}
            className="h-8 w-8"
          >
            <Undo2 className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={onRedo}
            disabled={!canRedo}
            className="h-8 w-8"
          >
            <Redo2 className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div className="flex items-center gap-3">
        {editHistory.length > 0 && (
          <>
            <Badge variant="secondary" className="h-7 px-3">
              {editHistory.length} pending changes
            </Badge>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowChangesPreview(true)}
              className="h-9"
            >
              <Eye className="h-4 w-4 mr-2" />
              View Changes
            </Button>
            <Button
              variant="default"
              size="sm"
              onClick={handleSaveChanges}
              className="h-9"
            >
              <Save className="h-4 w-4 mr-2" />
              Save Changes
            </Button>
          </>
        )}
      </div>
    </div>
  );
}

export default TableControls;