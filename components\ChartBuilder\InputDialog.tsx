'use client'

import { useState } from 'react'
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { Terminal, Send } from "lucide-react"

interface InputDialogProps {
  isOpen: boolean
  prompt: string
  onSubmit: (input: string) => void
  onCancel: () => void
}

export function InputDialog({ isOpen, prompt, onSubmit, onCancel }: InputDialogProps) {
  const [inputValue, setInputValue] = useState('')

  const handleSubmit = () => {
    onSubmit(inputValue)
    setInputValue('')
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      handleSubmit()
    } else if (e.key === 'Escape') {
      e.preventDefault()
      onCancel()
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={() => onCancel()}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Terminal className="h-4 w-4" />
            Python Input Required
          </DialogTitle>
          <DialogDescription>
            The Python code is waiting for your input.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Prompt display */}
          <div className="bg-muted p-3 rounded-md">
            <div className="text-sm font-mono">
              {prompt || "Enter input:"}
            </div>
          </div>
          
          {/* Input field */}
          <div className="flex gap-2">
            <Input
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Type your input here..."
              className="flex-1 font-mono"
              autoFocus
            />
            <Button onClick={handleSubmit} size="sm">
              <Send className="h-4 w-4" />
            </Button>
          </div>
          
          {/* Help text */}
          <div className="text-xs text-muted-foreground">
            Press Enter to submit, Escape to cancel
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
