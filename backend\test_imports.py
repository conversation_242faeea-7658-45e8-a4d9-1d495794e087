#!/usr/bin/env python3
"""
Test script to verify all imports work correctly
"""

print("Testing imports...")

try:
    print("✓ Testing basic imports...")
    import fastapi
    import uvicorn
    import pandas as pd
    import numpy as np
    print("✓ Basic imports successful")
    
    print("✓ Testing jupyter_kernel import...")
    from jupyter_kernel import get_kernel, reset_kernel
    print("✓ Jupyter kernel import successful")
    
    print("✓ Testing main.py import...")
    from main import app
    print("✓ Main app import successful")
    
    print("✓ Testing kernel creation...")
    kernel = get_kernel()
    print("✓ Kernel creation successful")
    
    print("✓ Testing simple execution...")
    result = kernel.execute_code("print('Hello World!')", [])
    print(f"✓ Execution result: {result.get('stdout', 'No output')}")
    
    print("\n🎉 All tests passed! Server should start correctly.")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
