'use client'

import { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, <PERSON><PERSON>Title, DialogTrigger } from "@/components/ui/dialog";
import { Keyboard, HelpCircle } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { KEYBOARD_SHORTCUTS } from './hooks/useKeyboardShortcuts';

interface KeyboardShortcutsHelpProps {
  trigger?: React.ReactNode;
}

export function KeyboardShortcutsHelp({ trigger }: KeyboardShortcutsHelpProps) {
  const [open, setOpen] = useState(false);

  const shortcutCategories = [
    {
      title: "Cell Execution",
      shortcuts: [
        { key: KEYBOARD_SHORTCUTS.RUN_CELL, description: "Run current cell", icon: "▶️" },
        { key: KEYBOARD_SHORTCUTS.RUN_AND_ADD, description: "Run cell and add new cell below", icon: "▶️➕" },
      ]
    },
    {
      title: "Cell Management",
      shortcuts: [
        { key: KEYBOARD_SHORTCUTS.ADD_CELL_BELOW, description: "Add cell below", icon: "➕⬇️" },
        { key: KEYBOARD_SHORTCUTS.ADD_CELL_ABOVE, description: "Add cell above", icon: "➕⬆️" },
        { key: KEYBOARD_SHORTCUTS.DELETE_CELL, description: "Delete current cell", icon: "🗑️" },
        { key: KEYBOARD_SHORTCUTS.MOVE_UP, description: "Move cell up", icon: "⬆️" },
        { key: KEYBOARD_SHORTCUTS.MOVE_DOWN, description: "Move cell down", icon: "⬇️" },
      ]
    },
    {
      title: "Cell Type & Language",
      shortcuts: [
        { key: KEYBOARD_SHORTCUTS.CONVERT_TO_MARKDOWN, description: "Convert to markdown cell", icon: "📝" },
        { key: KEYBOARD_SHORTCUTS.CONVERT_TO_CODE, description: "Convert to code cell", icon: "💻" },
        { key: KEYBOARD_SHORTCUTS.TOGGLE_LANGUAGE, description: "Toggle programming language", icon: "🔄" },
      ]
    },
    {
      title: "Editor Navigation",
      shortcuts: [
        { key: KEYBOARD_SHORTCUTS.FOCUS_EDITOR, description: "Focus editor (when cell is selected)", icon: "🎯" },
        { key: KEYBOARD_SHORTCUTS.ESCAPE, description: "Unfocus editor", icon: "⎋" },
      ]
    }
  ];

  const formatShortcut = (shortcut: string) => {
    return shortcut.split('+').map((part, index, array) => (
      <span key={index} className="inline-flex items-center">
        <Badge variant="outline" className="text-xs px-1 py-0 font-mono">
          {part}
        </Badge>
        {index < array.length - 1 && <span className="mx-1 text-muted-foreground">+</span>}
      </span>
    ));
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
            <Keyboard className="h-3 w-3" />
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Keyboard className="h-5 w-5" />
            Keyboard Shortcuts
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          <div className="text-sm text-muted-foreground">
            Use these keyboard shortcuts to work more efficiently with cells. Most shortcuts work when a cell is hovered or focused.
          </div>

          {shortcutCategories.map((category, categoryIndex) => (
            <div key={categoryIndex} className="space-y-3">
              <h3 className="font-semibold text-sm flex items-center gap-2">
                {category.title}
              </h3>
              
              <div className="space-y-2">
                {category.shortcuts.map((shortcut, index) => (
                  <div key={index} className="flex items-center justify-between py-2 px-3 rounded-md bg-muted/30 hover:bg-muted/50 transition-colors">
                    <div className="flex items-center gap-3">
                      <span className="text-lg">{shortcut.icon}</span>
                      <span className="text-sm">{shortcut.description}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      {formatShortcut(shortcut.key)}
                    </div>
                  </div>
                ))}
              </div>
              
              {categoryIndex < shortcutCategories.length - 1 && <Separator />}
            </div>
          ))}

          <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-950/30 rounded-lg border border-blue-200 dark:border-blue-800">
            <div className="flex items-start gap-2">
              <HelpCircle className="h-4 w-4 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
              <div className="text-sm">
                <div className="font-medium text-blue-900 dark:text-blue-100 mb-1">Tips:</div>
                <ul className="text-blue-800 dark:text-blue-200 space-y-1 text-xs">
                  <li>• Hover over a cell to activate most shortcuts</li>
                  <li>• <Badge variant="outline" className="text-xs px-1 py-0 font-mono">Ctrl+Enter</Badge> works from anywhere in the editor</li>
                  <li>• <Badge variant="outline" className="text-xs px-1 py-0 font-mono">Shift+Enter</Badge> runs and creates a new cell automatically</li>
                  <li>• Use <Badge variant="outline" className="text-xs px-1 py-0 font-mono">Escape</Badge> to exit editor focus</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
