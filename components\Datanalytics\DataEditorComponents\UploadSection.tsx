import { useState, useCallback, useRef } from 'react';
import { useDropzone } from 'react-dropzone';
import { FileUp, Database, X } from 'lucide-react';
import { motion } from 'framer-motion';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
// Removed Sheet import as we're using the sidebar directly
import * as XLSX from 'xlsx';
import Papa from 'papaparse';
import { toast } from 'sonner';
import { DatasetList } from './DatasetList';
import FileUpload from './FileImport';
// Remove Dialog import and modal logic

interface UploadSectionProps {
  onDataLoaded: (data: any[], headers: string[], fileName: string) => void;
  savedDatasets: any[];
  storageInfo: {
    used: number;
    total: number;
    percentage: number;
  };
  isLoadingDatasets: boolean;
  onDatasetSelect: (dataset: any) => void;
  onDeleteDataset: (datasetId: string) => void;
  onShowVersionHistory: (dataset: any) => void;
  allVersions: Record<string, any[]>;
  renderDatasetInfo: () => React.ReactNode;
}

export const UploadSection: React.FC<UploadSectionProps> = ({
  onDataLoaded,
  savedDatasets,
  storageInfo,
  isLoadingDatasets,
  onDatasetSelect,
  onDeleteDataset,
  onShowVersionHistory,
  allVersions,
  renderDatasetInfo
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [processingStage, setProcessingStage] = useState<string>('');
  const [showUploadCard, setShowUploadCard] = useState(false);
  const uploadRef = useRef<HTMLDivElement>(null);

  const validateAndProcessHeaders = (headers: any[]): string[] => {
    // Convert all headers to strings and clean them
    const processedHeaders = headers.map((header, index) => {
      const processed = header ? String(header).trim().replace(/[\u0000-\u001F]/g, '') : `Column_${index + 1}`;
      return processed || `Column_${index + 1}`;
    });

    // Check for duplicates and make unique if necessary
    const uniqueHeaders = processedHeaders.reduce((acc: string[], header: string, index: number) => {
      let uniqueHeader = header;
      let counter = 1;
      while (acc.includes(uniqueHeader)) {
        uniqueHeader = `${header}_${counter}`;
        counter++;
      }
      acc.push(uniqueHeader);
      return acc;
    }, []);

    return uniqueHeaders;
  };

  const processExcelData = (data: any[][], headers: string[]) => {
    return data.map(row => {
      const rowData: Record<string, any> = {};
      headers.forEach((header, index) => {
        let value = row[index];
        // Convert empty or undefined values to null
        rowData[header] = (value === undefined || value === '') ? null : String(value);
      });
      return rowData;
    });
  };

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (!file) return;

    try {
      setIsUploading(true);
      setProcessingStage('Reading file...');
      setUploadProgress(10);

      if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
        const reader = new FileReader();

        reader.onload = async (e) => {
          try {
            setProcessingStage('Converting Excel data...');
            setUploadProgress(30);

            const data = e.target?.result;
            const workbook = XLSX.read(data, { type: 'array' });
            const firstSheet = workbook.Sheets[workbook.SheetNames[0]];

            setProcessingStage('Processing data...');
            setUploadProgress(60);

            const jsonData = XLSX.utils.sheet_to_json(firstSheet, {
              header: 1,
              raw: false,
              dateNF: 'yyyy-mm-dd'
            }) as any[][];

            if (jsonData.length < 2) {
              throw new Error('File appears to be empty or contains only headers');
            }

            const headers = validateAndProcessHeaders(jsonData[0]);
            const rows = processExcelData(jsonData.slice(1), headers);

            setProcessingStage('Finalizing...');
            setUploadProgress(90);

            onDataLoaded(rows, headers, file.name);
            toast.success('Excel file uploaded successfully!');
          } catch (error) {
            console.error('Excel processing error:', error);
            toast.error(`Failed to process Excel file: ${error instanceof Error ? error.message : 'Unknown error'}`);
          }
        };

        reader.onerror = () => {
          toast.error('Failed to read Excel file');
        };

        reader.readAsArrayBuffer(file);
      } else {
        // Handle CSV files
        Papa.parse(file, {
          complete: (results) => {
            try {
              setProcessingStage('Processing CSV data...');
              setUploadProgress(60);

              if (results.data.length < 2) {
                throw new Error('File appears to be empty or contains only headers');
              }

              const rawHeaders = results.data[0] as string[];
              const headers = validateAndProcessHeaders(rawHeaders);

              const rows = (results.data as any[][]).slice(1).map(row => {
                const rowData: Record<string, any> = {};
                headers.forEach((header, index) => {
                  let value = row[index];
                  rowData[header] = (value === undefined || value === '') ? null : String(value);
                });
                return rowData;
              });

              setProcessingStage('Finalizing...');
              setUploadProgress(90);

              onDataLoaded(rows, headers, file.name);
              toast.success('CSV file uploaded successfully!');
            } catch (error) {
              console.error('CSV processing error:', error);
              toast.error(`Failed to process CSV file: ${error instanceof Error ? error.message : 'Unknown error'}`);
            }
          },
          error: (error) => {
            console.error('CSV parsing error:', error);
            toast.error('Failed to parse CSV file');
          }
        });
      }
    } catch (error) {
      console.error('File processing error:', error);
      toast.error('Failed to process file');
    } finally {
      setTimeout(() => {
        setIsUploading(false);
        setUploadProgress(0);
        setProcessingStage('');
      }, 1000);
    }
  }, [onDataLoaded]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/csv': ['.csv'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/vnd.ms-excel': ['.xls']
    },
    multiple: false
  });

  return (
    <div className="w-full px-0 relative">
      <div className="w-full pt-2">
        <div className="flex items-center justify-between mb-2">
          <div>
            <h1 className="text-2xl font-bold">Upload Your Data</h1>
            <p className="text-muted-foreground text-sm">Drop your CSV or Excel file here or click to browse</p>
          </div>
          <Button
            variant="default"
            size="sm"
            className="ml-auto"
            onClick={() => {
              setShowUploadCard(true);
              setTimeout(() => uploadRef.current?.scrollIntoView({ behavior: 'smooth', block: 'center' }), 100);
            }}
          >
            <FileUp className="h-4 w-4 mr-1" /> Import
          </Button>
        </div>
        {/* Upload Box - only visible when showUploadCard is true */}
        {showUploadCard && (
          <div ref={uploadRef} className="w-full mb-2 relative">
            <div className="absolute top-2 right-2 z-10">
              <Button size="icon" variant="ghost" onClick={() => setShowUploadCard(false)}>
                <X className="h-4 w-4" />
              </Button>
            </div>
            <FileUpload
              onUploadSuccess={async (file) => {
                if (!file) return;
                try {
                  setIsUploading(true);
                  setProcessingStage('Reading file...');
                  setUploadProgress(10);
                  if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
                    const reader = new FileReader();
                    reader.onload = async (e) => {
                      try {
                        setProcessingStage('Converting Excel data...');
                        setUploadProgress(30);
                        const data = e.target?.result;
                        const workbook = XLSX.read(data, { type: 'array' });
                        const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
                        setProcessingStage('Processing data...');
                        setUploadProgress(60);
                        const jsonData = XLSX.utils.sheet_to_json(firstSheet, {
                          header: 1,
                          raw: false,
                          dateNF: 'yyyy-mm-dd'
                        }) as any[][];
                        if (jsonData.length < 2) {
                          throw new Error('File appears to be empty or contains only headers');
                        }
                        const headers = validateAndProcessHeaders(jsonData[0]);
                        const rows = processExcelData(jsonData.slice(1), headers);
                        setProcessingStage('Finalizing...');
                        setUploadProgress(90);
                        onDataLoaded(rows, headers, file.name);
                        toast.success('Excel file uploaded successfully!');
                        setShowUploadCard(false);
                      } catch (error) {
                        console.error('Excel processing error:', error);
                        toast.error(`Failed to process Excel file: ${error instanceof Error ? error.message : 'Unknown error'}`);
                      }
                    };
                    reader.onerror = () => {
                      toast.error('Failed to read Excel file');
                    };
                    reader.readAsArrayBuffer(file);
                  } else {
                    Papa.parse(file, {
                      complete: (results) => {
                        try {
                          setProcessingStage('Processing CSV data...');
                          setUploadProgress(60);
                          if (results.data.length < 2) {
                            throw new Error('File appears to be empty or contains only headers');
                          }
                          const rawHeaders = results.data[0] as string[];
                          const headers = validateAndProcessHeaders(rawHeaders);
                          const rows = (results.data as any[][]).slice(1).map(row => {
                            const rowData: Record<string, any> = {};
                            headers.forEach((header, index) => {
                              let value = row[index];
                              rowData[header] = (value === undefined || value === '') ? null : String(value);
                            });
                            return rowData;
                          });
                          setProcessingStage('Finalizing...');
                          setUploadProgress(90);
                          onDataLoaded(rows, headers, file.name);
                          toast.success('CSV file uploaded successfully!');
                          setShowUploadCard(false);
                        } catch (error) {
                          console.error('CSV processing error:', error);
                          toast.error(`Failed to process CSV file: ${error instanceof Error ? error.message : 'Unknown error'}`);
                        }
                      },
                      error: (error) => {
                        console.error('CSV parsing error:', error);
                        toast.error('Failed to parse CSV file');
                      }
                    });
                  }
                } catch (error) {
                  console.error('File processing error:', error);
                  toast.error('Failed to process file');
                } finally {
                  setTimeout(() => {
                    setIsUploading(false);
                    setUploadProgress(0);
                    setProcessingStage('');
                  }, 1000);
                }
              }}
              acceptedFileTypes={['.csv', '.xlsx', '.xls']}
              maxFileSize={5 * 1024 * 1024}
              className="w-full"
            />
          </div>
        )}
        {/* Upload Progress */}
        {isUploading && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-2 mb-6"
          >
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">{processingStage}</span>
              <span className="font-medium">{uploadProgress}%</span>
            </div>
            <Progress value={uploadProgress} className="h-2" />
          </motion.div>
        )}
        {/* Dataset Info Section - full width, full screen height */}
        {savedDatasets.length > 0 && (
          <>
            {renderDatasetInfo()}
          </>
        )}
      </div>
    </div>
  );
};



