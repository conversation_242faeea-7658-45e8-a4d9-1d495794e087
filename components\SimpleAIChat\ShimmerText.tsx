'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'

interface ShimmerTextProps {
  text?: string
  className?: string
  speed?: number
}

export function ShimmerText({ 
  text = "AI is thinking...", 
  className,
  speed = 2 
}: ShimmerTextProps) {
  return (
    <div className={cn("relative inline-block", className)}>
      <span className="text-transparent bg-clip-text bg-gradient-to-r from-muted-foreground via-foreground to-muted-foreground">
        {text}
      </span>
      <motion.div
        className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent"
        animate={{
          x: ['-100%', '100%']
        }}
        transition={{
          duration: speed,
          repeat: Infinity,
          ease: "linear"
        }}
        style={{
          background: 'linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.4) 50%, transparent 100%)'
        }}
      />
    </div>
  )
}

interface TypingIndicatorProps {
  className?: string
}

export function TypingIndicator({ className }: TypingIndicatorProps) {
  return (
    <div className={cn("flex items-center gap-1", className)}>
      <div className="flex gap-1">
        {[0, 1, 2].map((i) => (
          <motion.div
            key={i}
            className="w-2 h-2 bg-muted-foreground rounded-full"
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.5, 1, 0.5]
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              delay: i * 0.2
            }}
          />
        ))}
      </div>
      <ShimmerText 
        text="AI is analyzing your data..." 
        className="text-sm text-muted-foreground ml-2"
        speed={1.5}
      />
    </div>
  )
}

interface StreamingTextProps {
  text: string
  isComplete: boolean
  className?: string
}

export function StreamingText({ text, isComplete, className }: StreamingTextProps) {
  return (
    <div className={cn("relative", className)}>
      <span>{text}</span>
      {!isComplete && (
        <motion.span
          className="inline-block w-0.5 h-4 bg-primary ml-1"
          animate={{ opacity: [1, 0] }}
          transition={{ duration: 0.8, repeat: Infinity }}
        />
      )}
    </div>
  )
}
