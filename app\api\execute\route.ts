import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const { code, datasetIds, language, datasets } = body; // Extract datasets data directly
    const { getToken } = auth();

    // Validate required fields - datasets are now optional for Python
    if (!code || !language) {
      return NextResponse.json(
        { error: 'Missing required fields: code and language are required' },
        { status: 400 }
      );
    }

    // Check if this is a SQL query sent to the wrong endpoint
    if (language === 'sql') {
      return NextResponse.json(
        { error: 'SQL queries should be sent to /api/query endpoint, not /api/execute' },
        { status: 400 }
      );
    }

    // Only support Python execution in this endpoint
    if (language !== 'python') {
      return NextResponse.json(
        { error: `Unsupported language: ${language}. This endpoint only supports Python execution.` },
        { status: 400 }
      );
    }

    // For Python, we can work without datasets (user can load their own data)
    const datasetId = datasetIds && datasetIds.length > 0 ? datasetIds[0] : body.datasetId || 'no-dataset';

    // Get auth token for Python backend
    const token = await getToken();
    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Call the Python backend with retry logic
    const pythonBackendUrl = process.env.PYTHON_BACKEND_URL || 'http://127.0.0.1:8000';

    // Add retry logic for the Python backend
    const MAX_RETRIES = 2;
    let retries = 0;
    let response: Response | undefined;

    while (retries <= MAX_RETRIES) {
      try {
        // Call the Python backend
        response = await fetch(`${pythonBackendUrl}/api/execute`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': token,
          },
          body: JSON.stringify({
            query: code,
            language: language,
            datasetId: datasetId,
            datasetIds: datasetIds, // Pass the full array of dataset IDs
            datasets: datasets, // Pass the actual dataset data
          }),
          // Add a timeout to prevent hanging
          signal: AbortSignal.timeout(30000), // 30 second timeout for dataset loading
        });

        // If successful, break out of the retry loop
        break;
      } catch (fetchError) {
        retries++;
        console.error(`Fetch attempt ${retries} failed:`, fetchError);

        // If we've reached max retries, throw the error
        if (retries > MAX_RETRIES) {
          throw new Error(`Failed to connect to Python server after ${MAX_RETRIES} attempts. The server might be down.`);
        }

        // Wait before retrying (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, 1000 * retries));
      }
    }

    // Ensure response is defined before using it
    if (!response) {
      throw new Error('Failed to connect to Python server. No response received.');
    }

    if (!response.ok) {
      // Safely try to parse the error response as JSON
      let errorMessage = `HTTP error ${response.status}`;
      try {
        const errorText = await response.text();
        // Try to parse as JSON
        try {
          const errorData = JSON.parse(errorText);
          errorMessage = errorData.error || errorMessage;
        } catch (jsonError) {
          // If it's not valid JSON, use the text directly (truncated if too long)
          errorMessage = errorText.length > 200
            ? `${errorText.substring(0, 200)}...`
            : errorText;
        }
      } catch (textError) {
        // If we can't even get the text, use a generic error
        console.error('Failed to read error response:', textError);
      }

      return NextResponse.json(
        { error: errorMessage },
        { status: response.status }
      );
    }

    // Safely parse the response as JSON
    let result: any;
    try {
      const responseText = await response.text();
      try {
        result = JSON.parse(responseText);
      } catch (jsonError) {
        console.error('Failed to parse response as JSON:', jsonError);
        console.log('Response text:', responseText.substring(0, 200) + '...');
        throw new Error('Invalid JSON response from server');
      }
    } catch (textError) {
      console.error('Failed to read response:', textError);
      throw new Error('Failed to read response from server');
    }
    return NextResponse.json(result);
  } catch (error) {
    console.error('Code execution error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to execute code' },
      { status: 500 }
    );
  }
}




