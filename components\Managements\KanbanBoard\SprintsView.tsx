"use client"

import { useState } from "react"
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON> 
} from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { 
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alog<PERSON>itle,
} from "@/components/ui/dialog"
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow 
} from "@/components/ui/table"
import { 
  GitBranch, 
  Plus, 
  Edit,
  Trash,
  CalendarClock,
  ArrowLeft 
} from "lucide-react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { format } from "date-fns"
import { toast } from "sonner"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"

interface Sprint {
  id: string
  name: string
  startDate: Date
  endDate: Date
  status: "planning" | "active" | "completed"
}

interface SprintsViewProps {
  sprints: Sprint[]
  onCreateSprint: (sprint: Omit<Sprint, 'id'>) => void
  onUpdateSprint: (id: string, updates: Partial<Sprint>) => void
  onDeleteSprint: (id: string) => void
  onBack: () => void
}

export function SprintsView({
  sprints,
  onCreateSprint,
  onUpdateSprint,
  onDeleteSprint,
  onBack
}: SprintsViewProps) {
  const [isCreating, setIsCreating] = useState(false)
  const [isEditing, setIsEditing] = useState(false)
  const [editingSprint, setEditingSprint] = useState<Sprint | null>(null)
  
  const [sprintName, setSprintName] = useState("")
  const [startDate, setStartDate] = useState(format(new Date(), 'yyyy-MM-dd'))
  const [endDate, setEndDate] = useState(format(new Date(Date.now() + 12096e5), 'yyyy-MM-dd'))
  const [status, setStatus] = useState<"planning" | "active" | "completed">("planning")
  
  const handleCreateSprint = () => {
    if (!sprintName) return
    
    onCreateSprint({
      name: sprintName,
      startDate: new Date(startDate),
      endDate: new Date(endDate),
      status
    })
    
    setIsCreating(false)
    resetForm()
    toast.success(`Sprint "${sprintName}" created successfully`)
  }
  
  const handleUpdateSprint = () => {
    if (!editingSprint) return
    
    onUpdateSprint(editingSprint.id, {
      name: sprintName,
      startDate: new Date(startDate),
      endDate: new Date(endDate),
      status
    })
    
    setIsEditing(false)
    resetForm()
    toast.success(`Sprint "${sprintName}" updated successfully`)
  }
  
  const handleEditClick = (sprint: Sprint) => {
    setEditingSprint(sprint)
    setSprintName(sprint.name)
    setStartDate(format(new Date(sprint.startDate), 'yyyy-MM-dd'))
    setEndDate(format(new Date(sprint.endDate), 'yyyy-MM-dd'))
    setStatus(sprint.status)
    setIsEditing(true)
  }
  
  const handleDeleteClick = (sprint: Sprint) => {
    if (confirm(`Are you sure you want to delete sprint "${sprint.name}"?`)) {
      onDeleteSprint(sprint.id)
      toast.success(`Sprint "${sprint.name}" deleted successfully`)
    }
  }
  
  const resetForm = () => {
    setSprintName("")
    setStartDate(format(new Date(), 'yyyy-MM-dd'))
    setEndDate(format(new Date(Date.now() + 12096e5), 'yyyy-MM-dd'))
    setStatus("planning")
    setEditingSprint(null)
  }
  
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "planning":
        return <Badge variant="outline" className="bg-blue-50 text-blue-600">Planning</Badge>
      case "active":
        return <Badge variant="outline" className="bg-green-50 text-green-600">Active</Badge>
      case "completed":
        return <Badge variant="outline" className="bg-slate-50 text-slate-600">Completed</Badge>
      default:
        return null
    }
  }
  
  return (
    <>
      <Card>
        <CardHeader className="px-6 py-4 flex flex-row items-center justify-between space-y-0">
          <div className="space-y-1.5 flex items-center">
            <Button 
              variant="ghost" 
              size="sm" 
              className="mr-2"
              onClick={onBack}
            >
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back
            </Button>
            <div>
              <h3 className="font-semibold leading-none tracking-tight">Sprint Management</h3>
              <p className="text-sm text-muted-foreground">
                Plan and manage your development cycles
              </p>
            </div>
          </div>
          <Button onClick={() => setIsCreating(true)}>
            <Plus className="h-4 w-4 mr-2" />
            New Sprint
          </Button>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Sprint Name</TableHead>
                <TableHead>Start Date</TableHead>
                <TableHead>End Date</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Duration</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {sprints.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8">
                    <div className="flex flex-col items-center justify-center">
                      <GitBranch className="h-8 w-8 text-muted-foreground mb-2" />
                      <p className="text-lg font-medium mb-2">No sprints found</p>
                      <p className="text-sm text-muted-foreground mb-4">
                        Create your first sprint to start planning your work
                      </p>
                      <Button onClick={() => setIsCreating(true)}>
                        <Plus className="h-4 w-4 mr-2" />
                        New Sprint
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                sprints.map(sprint => {
                  const durationDays = Math.ceil(
                    (new Date(sprint.endDate).getTime() - new Date(sprint.startDate).getTime()) / 
                    (1000 * 60 * 60 * 24)
                  )
                  
                  return (
                    <TableRow key={sprint.id}>
                      <TableCell className="font-medium flex items-center">
                        <GitBranch className="h-4 w-4 mr-2 text-primary" />
                        {sprint.name}
                      </TableCell>
                      <TableCell>
                        {new Date(sprint.startDate).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        {new Date(sprint.endDate).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(sprint.status)}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <CalendarClock className="h-4 w-4 mr-1 text-muted-foreground" />
                          {durationDays} days
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleEditClick(sprint)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="text-destructive"
                            onClick={() => handleDeleteClick(sprint)}
                          >
                            <Trash className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  )
                })
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
      
      {/* Sprint Create Dialog */}
      <Dialog open={isCreating} onOpenChange={setIsCreating}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Sprint</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="sprintName">Sprint Name</Label>
              <Input 
                id="sprintName" 
                value={sprintName} 
                onChange={(e) => setSprintName(e.target.value)}
                placeholder="e.g. Sprint 2"
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="startDate">Start Date</Label>
                <Input
                  id="startDate"
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="endDate">End Date</Label>
                <Input
                  id="endDate"
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                />
              </div>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="status">Status</Label>
              <Select value={status} onValueChange={(value: any) => setStatus(value)}>
                <SelectTrigger id="status">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="planning">Planning</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreating(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateSprint}>Create Sprint</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Sprint Edit Dialog */}
      <Dialog open={isEditing} onOpenChange={setIsEditing}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Sprint</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="edit-sprintName">Sprint Name</Label>
              <Input 
                id="edit-sprintName" 
                value={sprintName} 
                onChange={(e) => setSprintName(e.target.value)}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="edit-startDate">Start Date</Label>
                <Input
                  id="edit-startDate"
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-endDate">End Date</Label>
                <Input
                  id="edit-endDate"
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                />
              </div>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="edit-status">Status</Label>
              <Select value={status} onValueChange={(value: any) => setStatus(value)}>
                <SelectTrigger id="edit-status">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="planning">Planning</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditing(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateSprint}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
} 