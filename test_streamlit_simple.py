#!/usr/bin/env python3
"""
Simple test for Streamlit functionality
"""

import requests
import json

# Test the backend
backend_url = "https://flopbackend.onrender.com"

# Test code
test_code = """
import streamlit as st
import pandas as pd
import numpy as np

st.title("Hello World!")
st.write("This is a test Streamlit app")

# Create some data
data = pd.DataFrame({
    'x': np.random.randn(10),
    'y': np.random.randn(10)
})

st.dataframe(data)
st.line_chart(data)

result = create_streamlit_app('''
import streamlit as st
st.title("Test App")
st.write("Hello from Streamlit!")
''', 'test123')
"""

# Test the execute endpoint
try:
    response = requests.post(f"{backend_url}/api/execute", 
        json={
            "query": test_code,
            "language": "python",
            "datasetId": "test",
            "datasetIds": ["test"]
        },
        headers={
            "Authorization": "test-token",
            "Content-Type": "application/json"
        },
        timeout=30
    )
    
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print("Success!")
        print(f"Plots: {len(result.get('plots', []))}")
        if result.get('plots'):
            for i, plot in enumerate(result['plots']):
                print(f"Plot {i+1}: {plot[:100]}...")
    else:
        print(f"Error: {response.text}")
        
except Exception as e:
    print(f"Request failed: {e}")