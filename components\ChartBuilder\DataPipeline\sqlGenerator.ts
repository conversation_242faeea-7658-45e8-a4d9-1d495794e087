import { Node, <PERSON> } from 'reactflow'
import { PipelineNodeData } from './types'

export function generateSQL(nodes: Node<PipelineNodeData>[], edges: Edge[]): string {
  if (nodes.length === 0) {
    return '-- No nodes in pipeline'
  }

  try {
    // Find the output node or the last node in the pipeline
    const outputNode = nodes.find(node => node.data.type === 'output')
    if (!outputNode) {
      return '-- No output node found'
    }

    // Build the SQL by traversing backwards from the output node
    const sql = buildSQLFromNode(outputNode, nodes, edges, new Set())
    return sql || '-- Unable to generate SQL'
  } catch (error) {
    console.error('Error generating SQL:', error)
    return '-- Error generating SQL: ' + (error as Error).message
  }
}

function buildSQLFromNode(
  node: Node<PipelineNodeData>,
  allNodes: Node<PipelineNodeData>[],
  allEdges: Edge[],
  visited: Set<string>
): string {
  if (visited.has(node.id)) {
    return `-- Circular reference detected at ${node.id}`
  }
  visited.add(node.id)

  // Find incoming edges to this node
  const incomingEdges = allEdges.filter(edge => edge.target === node.id)
  
  if (node.data.type === 'dataset') {
    // Base case: dataset node
    return `SELECT * FROM "${node.data.dataset?.name || 'unknown_dataset'}"`
  }

  if (incomingEdges.length === 0) {
    return `-- No input for ${node.data.type} node`
  }

  // Get source nodes
  const sourceNodes = incomingEdges.map(edge => 
    allNodes.find(n => n.id === edge.source)
  ).filter(Boolean) as Node<PipelineNodeData>[]

  if (sourceNodes.length === 0) {
    return `-- No source nodes found`
  }

  switch (node.data.type) {
    case 'concat':
      return generateConcatSQL(sourceNodes, allNodes, allEdges, visited, node.data.config)
    
    case 'join':
      return generateJoinSQL(sourceNodes, allNodes, allEdges, visited, node.data.config)
    
    case 'transform':
      return generateTransformSQL(sourceNodes[0], allNodes, allEdges, visited, node.data.config)
    
    case 'output':
      // For output node, just return the SQL from its input
      return buildSQLFromNode(sourceNodes[0], allNodes, allEdges, new Set(visited))
    
    default:
      return `-- Unknown node type: ${node.data.type}`
  }
}

function generateConcatSQL(
  sourceNodes: Node<PipelineNodeData>[],
  allNodes: Node<PipelineNodeData>[],
  allEdges: Edge[],
  visited: Set<string>,
  config: any
): string {
  const sourceSQLs = sourceNodes.map(node => {
    const sql = buildSQLFromNode(node, allNodes, allEdges, new Set(visited))
    return `(${sql})`
  })

  const concatType = config?.concatType || 'union'
  
  if (concatType === 'union') {
    return sourceSQLs.join('\nUNION ALL\n')
  } else {
    // For append, we'll use UNION ALL (same as union in this context)
    return sourceSQLs.join('\nUNION ALL\n')
  }
}

function generateJoinSQL(
  sourceNodes: Node<PipelineNodeData>[],
  allNodes: Node<PipelineNodeData>[],
  allEdges: Edge[],
  visited: Set<string>,
  config: any
): string {
  if (sourceNodes.length < 2) {
    return '-- Join requires at least 2 inputs'
  }

  const leftSQL = buildSQLFromNode(sourceNodes[0], allNodes, allEdges, new Set(visited))
  const rightSQL = buildSQLFromNode(sourceNodes[1], allNodes, allEdges, new Set(visited))
  
  const joinType = config?.joinType || 'inner'
  const joinKeys = config?.joinKeys || []

  let joinClause = 'ON 1=1' // Default join condition
  if (joinKeys.length > 0) {
    const conditions = joinKeys.map((key: any) => 
      `left_table.${key.left} = right_table.${key.right}`
    )
    joinClause = `ON ${conditions.join(' AND ')}`
  }

  return `
SELECT *
FROM (${leftSQL}) AS left_table
${joinType.toUpperCase()} JOIN (${rightSQL}) AS right_table
${joinClause}`.trim()
}

function generateTransformSQL(
  sourceNode: Node<PipelineNodeData>,
  allNodes: Node<PipelineNodeData>[],
  allEdges: Edge[],
  visited: Set<string>,
  config: any
): string {
  const sourceSQL = buildSQLFromNode(sourceNode, allNodes, allEdges, new Set(visited))
  const transformType = config?.transformType || 'filter'

  switch (transformType) {
    case 'filter':
      const filters = config?.filters || []
      if (filters.length === 0) {
        return `SELECT * FROM (${sourceSQL}) AS source_table`
      }
      
      const whereConditions = filters.map((filter: any) => {
        const { column, operator, value } = filter
        const quotedValue = typeof value === 'string' ? `'${value}'` : value
        return `${column} ${operator} ${quotedValue}`
      })
      
      return `
SELECT *
FROM (${sourceSQL}) AS source_table
WHERE ${whereConditions.join(' AND ')}`.trim()

    case 'select':
      const selectedColumns = config?.selectedColumns || ['*']
      const columnList = selectedColumns.join(', ')
      
      return `
SELECT ${columnList}
FROM (${sourceSQL}) AS source_table`.trim()

    case 'aggregate':
      const aggregations = config?.aggregations || []
      if (aggregations.length === 0) {
        return `SELECT * FROM (${sourceSQL}) AS source_table`
      }
      
      const aggColumns = aggregations.map((agg: any) => 
        `${agg.function}(${agg.column}) AS ${agg.function}_${agg.column}`
      )
      
      return `
SELECT ${aggColumns.join(', ')}
FROM (${sourceSQL}) AS source_table`.trim()

    case 'sort':
      const sortBy = config?.sortBy || []
      if (sortBy.length === 0) {
        return `SELECT * FROM (${sourceSQL}) AS source_table`
      }
      
      const orderBy = sortBy.map((sort: any) => 
        `${sort.column} ${sort.direction.toUpperCase()}`
      )
      
      return `
SELECT *
FROM (${sourceSQL}) AS source_table
ORDER BY ${orderBy.join(', ')}`.trim()

    default:
      return `SELECT * FROM (${sourceSQL}) AS source_table`
  }
}
