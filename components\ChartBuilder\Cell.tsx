'use client'

import { useState, useRef, useEffect, useMemo } from 'react'
import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Play, Trash2, Plus, Database, ChevronDown, Loader2, Code2, Server, BarChart3, Maximize2, MoveUp, MoveDown, Bold, Italic, List, ListOrdered, Code, Link, Image as ImageIcon, Table as TableIcon, Square, FileText } from "lucide-react"
import Editor from '@monaco-editor/react'
import { useTheme } from 'next-themes'
import { toast } from 'sonner'
import { Table } from "@/components/ui/table"
import { Dataset } from '@/types/index'
import { QueryResult } from './QueryResult'
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select"
import { cn } from "@/lib/utils"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { formatDistanceToNow } from 'date-fns'
import _ from 'lodash'
import { Checkbox } from "@/components/ui/checkbox"
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import { Separator } from "@/components/ui/separator"
import { MarkdownCell } from './MarkdownCell'
import { AIAssistant } from './AIAssistant'
import { useKeyboardShortcuts, KeyboardShortcutHandlers } from './hooks/useKeyboardShortcuts'
import { KeyboardShortcutsHelp } from './KeyboardShortcutsHelp'

interface ServerStatus {
  status: 'healthy' | 'error' | 'warning'
  message: string
}

interface CellProps {
  id: string
  content: string
  result?: {
    data: any[];
    output?: string;
    plots?: string[];
    error?: string;
    errorDetails?: {
      message: string;
      code?: string;
      stack?: string;
      serverTrace?: string;
    };
    executionTime?: number;
    result?: any;
  };
  onRun: (id: string, code: string, shouldShowGraphicWalker?: boolean) => Promise<void>
  onDelete: (id: string) => void
  onAddCell: (id: string, cellType?: 'code' | 'markdown') => void
  onSelectDatasets: (ids: string[]) => Promise<{ selectedList: Dataset[], datasetIds: string[] } | void>
  language: string
  onLanguageChange: (language: string) => void
  isSuccess?: boolean
  selectedDatasets: Dataset[]
  availableDatasets: Dataset[]
  showGraphicWalker?: boolean
  onSaveChart?: (data: any[], config: any, chartType: 'line' | 'bar' | 'pie' | 'area', chartId?: string) => void
  onSaveTable?: (data: any[], columns: string[], tableId?: string) => void
  onSavePlot?: (plotUrl: string, plotTitle: string, plotId?: string) => void
  lastRun?: Date;
  lastUpdate?: Date;
  chartType?: 'line' | 'bar' | 'pie' | 'area'
  onChartTypeChange?: (type: 'line' | 'bar' | 'pie' | 'area') => void
  onContentChange?: (value: string) => void
  viewMode?: 'table' | 'chart' | 'output' | 'graphicwalker'
  onViewModeChange?: (mode: 'table' | 'chart' | 'output' | 'graphicwalker') => void
  notes?: string;
  onUpdateNotes?: (id: string, notes: string) => void;
  cellType?: 'code' | 'markdown';
  onConvertCellType?: (id: string, targetType: 'code' | 'markdown') => void;
  index?: number;
  dragHandleProps?: any;
  onMoveUp?: (id: string) => void;
  onMoveDown?: (id: string) => void;
}

// Add this function to check if the SQL query has the --#graphicwalker comment
function hasGraphicWalkerComment(code: string): boolean {
  return code.includes("--#graphicwalker") || code.includes("-- #graphicwalker");
}

// Add this function to check if the SQL query has the loopchart command
function hasLoopchartCommand(code: string): boolean {
  return code.includes("--loopchart") || code.includes("-- loopchart");
}

export function Cell({
  id,
  content,
  result,
  onRun,
  onDelete,
  onAddCell,
  onSelectDatasets,
  language,
  onLanguageChange,
  isSuccess,
  selectedDatasets,
  availableDatasets,
  showGraphicWalker,
  onSaveChart,
  onSaveTable,
  onSavePlot,
  chartType,
  onChartTypeChange,
  onContentChange,
  viewMode,
  onViewModeChange,
  notes = '[]',
  onUpdateNotes,
  index,
  dragHandleProps,
  onMoveUp,
  onMoveDown,
  cellType = 'code',
  onConvertCellType
}: CellProps) {
  const { theme } = useTheme()
  const [isHovered, setIsHovered] = useState(false)
  const [isRunning, setIsRunning] = useState(false)
  const [isLoadingDatasets, setIsLoadingDatasets] = useState(false)
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const [editorHeight, setEditorHeight] = useState('50px')
  const [serverStatus, setServerStatus] = useState<ServerStatus>({
    status: 'warning',
    message: 'Checking server status...'
  })
  const editorRef = useRef<any>(null)
  const [executionTime, setExecutionTime] = useState<{
    startTime?: Date;
    endTime?: Date;
  }>({})
  const updateTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const [isEditingMarkdown, setIsEditingMarkdown] = useState(false)
  const [windowWidth, setWindowWidth] = useState<number>(typeof window !== 'undefined' ? window.innerWidth : 0)

  // Language cycling for shortcuts
  const languages = ['sql', 'python', 'javascript'];
  const currentLanguageIndex = languages.indexOf(language);

  // Check if data is available for chart view
  const hasChartableData = result?.data && result.data.length > 0 && Object.keys(result.data[0] || {}).some(
    key => typeof result.data[0][key] === 'number'
  )

  // Window resize handler
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth)
    }

    const handleSidebarToggle = () => {
      // Small delay to allow sidebar animation to complete
      setTimeout(() => {
        setWindowWidth(window.innerWidth)
      }, 300)
    }

    window.addEventListener('resize', handleResize);
    window.addEventListener('sidebar-toggle', handleSidebarToggle);
    
    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('sidebar-toggle', handleSidebarToggle);
    };
  }, []);

  // Server status check - Check Python backend health
  useEffect(() => {
    const checkServerStatus = async () => {
      try {
        // Check Python backend health
        const response = await fetch('http://localhost:8000/', {
          method: 'GET',
          mode: 'cors'
        })
        if (response.ok) {
          const data = await response.json()
          if (data.message && data.status === 'running') {
            setServerStatus({ status: 'healthy', message: 'Python backend is running' })
          } else {
            setServerStatus({ status: 'warning', message: 'Backend response unexpected' })
          }
        } else {
          setServerStatus({ status: 'error', message: 'Python backend error' })
        }
      } catch (error) {
        setServerStatus({ status: 'error', message: 'Python backend unreachable' })
      }
    }

    checkServerStatus()
    const interval = setInterval(checkServerStatus, 30000) // Check every 30 seconds
    return () => clearInterval(interval)
  }, [])

  // Auto-adjust editor height based on content
  const handleEditorDidMount = (editor: any) => {
    editorRef.current = editor
    
    const updateHeight = () => {
      const contentHeight = Math.min(1000, Math.max(50, editor.getContentHeight()))
      setEditorHeight(`${contentHeight}px`)
    }

    editor.onDidContentSizeChange(updateHeight)
    updateHeight()
  }

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element
      if (!target.closest('.dataset-dropdown')) {
        setIsDropdownOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])



  // Remove the datasets fetch since we now get them from props
  const handleDropdownToggle = () => {
    setIsDropdownOpen(!isDropdownOpen)
  }

  const handleDatasetSelection = async (datasetIds: string[]) => {
    setIsLoadingDatasets(true)
    try {
      await onSelectDatasets(datasetIds)
    } catch (error) {
      console.error('Error selecting datasets:', error)
      toast.error('Failed to load datasets')
    } finally {
      setIsLoadingDatasets(false)
      setIsDropdownOpen(false)
    }
  }

  const handleRunCell = async () => {
    if (isRunning) return;

    const code = editorRef.current?.getValue() || content;
    
    // Validation checks
    if (!code.trim()) {
      toast.error('Please enter some code to execute');
      return;
    }

    // Check for language mismatch
    if (language === 'python' && code.trim().startsWith('SELECT')) {
      toast.error('You are using SQL syntax with Python selected. Please change to SQL or update your code.');
      return;
    }

    if (language === 'sql' && (code.includes('import ') || code.includes('def ') || code.includes('print('))) {
      toast.error('You are using Python syntax with SQL selected. Please change to Python or update your code.');
      return;
    }

    if (language === 'python') {
      // Check for common SQL patterns in Python mode
      if (code.trim().startsWith('--') || code.includes('FROM dataset') || code.includes('SELECT *')) {
        toast.error('You are using the SQL template with Python selected. Please change to SQL or update your code.');
        return;
      }

      // For matplotlib plots, ensure the code includes plt.show() if it uses plt
      if (code.includes('plt.') && !code.includes('plt.show()') && !code.includes('.plot(')) {
        // If the code uses matplotlib but doesn't call plt.show(), show a helpful tip
        toast.info('Tip: Add plt.show() at the end to display your matplotlib plot.');
      }
    }

    setIsRunning(true);
    const startTime = new Date();
    setExecutionTime({ startTime, endTime: undefined });

    // Check for GraphicWalker comment or loopchart command
    const shouldShowGraphicWalker = language === 'sql' &&
      (hasGraphicWalkerComment(code) || hasLoopchartCommand(code));

    try {
      // For Python, ensure we have a valid code that won't cause syntax errors
      let codeToRun = code;
      if (language === 'python' && code.trim().startsWith('--')) {
        // If somehow we still have SQL comments in Python mode, convert them to Python comments
        codeToRun = code.replace(/^--/gm, '#');
      }

      // For Python plots, the Jupyter kernel will automatically capture them
      if (language === 'python') {
        // Add a simple working example if the code is just the default
        if (code.trim() === 'import matplotlib.pyplot as plt\nimport numpy as np\n\n\nx = np.linspace(0, 10, 100)\ny = np.sin(x)\n\n\nplt.figure(figsize=(10, 6))\nplt.plot(x, y)\nplt.title(\'Sine Wave\')\nplt.xlabel(\'X\')\nplt.ylabel(\'Y\')\nplt.grid(True)\n\n\nresult = get_plot()') {
          // This is the exact example code - make sure it works by adding tight_layout
          codeToRun = 'import matplotlib.pyplot as plt\nimport numpy as np\n\n# Create data\nx = np.linspace(0, 10, 100)\ny = np.sin(x)\n\n# Create plot\nplt.figure(figsize=(10, 6))\nplt.plot(x, y)\nplt.title(\'Sine Wave\')\nplt.xlabel(\'X\')\nplt.ylabel(\'Y\')\nplt.grid(True)\nplt.tight_layout()\n\n# Display the plot\nplt.show()\n\nprint("Plot generated successfully!")\n';
        }
      }

      // Execute the code with proper error handling
      try {
        await onRun(id, codeToRun, shouldShowGraphicWalker);
        
        // Check if plots were generated and switch to output tab if so
        if (result && result.plots && Array.isArray(result.plots) && result.plots.length > 0) {
          // Switch to output tab if we have plots (now unified with output)
          if (onViewModeChange) {
            onViewModeChange('output');
          }
          toast.success('Code executed successfully. Plot generated!');
        } else {
          toast.success('Code executed successfully');
        }
        
        const endTime = new Date();
        setExecutionTime({ startTime, endTime });
      } catch (error) {
        console.error('Execution error:', error);
        const endTime = new Date();
        setExecutionTime({ startTime, endTime });
        
        // Show error toast
        if (error instanceof Error) {
          if (error.message.includes('Unexpected token')) {
            toast.error('Server returned an invalid response. The Python server might be down or experiencing issues.');
          } else {
            toast.error(`Execution failed: ${error.message}`);
          }
        } else {
          toast.error('Unknown execution error');
        }
      } finally {
        setIsRunning(false);
      }
    } catch (error) {
      console.error('Outer execution error:', error);
      const endTime = new Date();
      setExecutionTime({ startTime, endTime });
      setIsRunning(false);
      toast.error('Failed to execute code');
    }
  };

  // Keyboard shortcut handlers
  const keyboardHandlers: KeyboardShortcutHandlers = {
    onRun: handleRunCell,
    onDelete: () => onDelete(id),
    onAddCellAbove: () => onAddCell(id, 'code'), // Add above current cell
    onAddCellBelow: () => onAddCell(id, 'code'), // Add below current cell
    onMoveUp: onMoveUp ? () => onMoveUp(id) : undefined,
    onMoveDown: onMoveDown ? () => onMoveDown(id) : undefined,
    onConvertToMarkdown: onConvertCellType ? () => onConvertCellType(id, 'markdown') : undefined,
    onConvertToCode: onConvertCellType ? () => onConvertCellType(id, 'code') : undefined,
    onFocusEditor: () => {
      if (editorRef.current) {
        editorRef.current.focus();
      }
    },
    onToggleLanguage: () => {
      if (cellType === 'code') {
        const nextIndex = (currentLanguageIndex + 1) % languages.length;
        onLanguageChange(languages[nextIndex]);
      }
    }
  };

  // Use keyboard shortcuts hook
  useKeyboardShortcuts(keyboardHandlers, {
    cellId: id,
    isHovered,
    isRunning,
    cellType,
    language,
    editorRef,
    disabled: false
  });

  // In the Cell component, update the editor onChange handler to use debouncing
  const debouncedContentChange = useRef(
    _.debounce((value: string) => {
      if (onContentChange) {
        onContentChange(value);
      }
    }, 200)  // 200ms debounce
  ).current;

  // Add a useEffect for cleanup
  useEffect(() => {
    return () => {
      // Clean up the timeout when component unmounts
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
    };
  }, []);

  // Handle notes update
  const handleNotesUpdate = (newNotes: string) => {
    if (onUpdateNotes) {
      onUpdateNotes(id, newNotes);
    }
  };

  // Handle chart type change and auto-switch to chart view
  const handleChartTypeChange = (newType: 'line' | 'bar' | 'pie' | 'area') => {
    if (onChartTypeChange) {
      onChartTypeChange(newType);
      
      // Auto-switch to chart view when chart type is changed
      if (onViewModeChange && hasChartableData) {
        onViewModeChange('chart');
      }
    }
  };

  return (
    <div
      className="relative group my-2"
      data-cell-id={id}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Hover buttons - positioned absolutely */}
      {isHovered && (
        <div className="absolute right-2 top-2 z-20 flex items-center gap-1 bg-background/90 backdrop-blur-sm border border-border rounded-md p-1 shadow-sm">
          {/* Run Button - only for code cells */}
          {cellType === 'code' && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    onClick={handleRunCell}
                    disabled={isRunning}
                    size="sm"
                    variant="ghost"
                    className="h-6 w-6 p-0"
                  >
                    {isRunning ? (
                      <Loader2 className="h-3 w-3 animate-spin" />
                    ) : (
                      <Play className="h-3 w-3" />
                    )}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p className="text-xs">{isRunning ? 'Running...' : 'Run cell'}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          {/* Add Cell Button */}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                      <Plus className="h-3 w-3" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => onAddCell(id, 'code')}>
                      <Code2 className="h-3 w-3 mr-2" />
                      Add Code Cell
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onAddCell(id, 'markdown')}>
                      <FileText className="h-3 w-3 mr-2" />
                      Add Markdown Cell
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TooltipTrigger>
              <TooltipContent>
                <p className="text-xs">Add cell</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          {/* Move Up/Down buttons */}
          {onMoveUp && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0"
                    onClick={() => onMoveUp(id)}
                  >
                    <MoveUp className="h-3 w-3" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p className="text-xs">Move cell up (Ctrl+Shift+↑)</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          {onMoveDown && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0"
                    onClick={() => onMoveDown(id)}
                  >
                    <MoveDown className="h-3 w-3" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p className="text-xs">Move cell down (Ctrl+Shift+↓)</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          {/* Delete Button */}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                  onClick={() => onDelete(id)}
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p className="text-xs">Delete cell</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      )}

      <Card className={cn(
        "relative transition-all duration-200",
        isHovered ? "ring-1 ring-blue-200 dark:ring-blue-800" : "",
        isRunning ? "ring-2 ring-blue-400 dark:ring-blue-600 ring-pulse" : "",
        result?.error ? "border-red-200 dark:border-red-800" : "",
        isSuccess && !result?.error ? "border-green-200 dark:border-green-800" : ""
      )}>
        <div className="relative">
          {/* Cell number indicator - positioned absolutely */}
          <div className="absolute left-2 top-2 z-10 flex items-center">
            <div className="text-xs text-muted-foreground bg-background/80 backdrop-blur-sm px-1 rounded">
              [{index !== undefined ? index + 1 : '?'}]
            </div>
          </div>

          {/* Header with controls */}
          <div className="flex items-center justify-between p-2 border-b border-border/50">
            {/* Left side - Language selector and dataset info */}
            <div className="flex items-center gap-2">
              {/* Language Selector */}
              <Select value={language} onValueChange={onLanguageChange}>
                <SelectTrigger className="w-24 h-6 text-xs">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="sql">SQL</SelectItem>
                  <SelectItem value="python">Python</SelectItem>
                  <SelectItem value="javascript">JS</SelectItem>
                  <SelectItem value="markdown">Markdown</SelectItem>
                </SelectContent>
              </Select>

              {/* Dataset Selection - only for code cells */}
              {cellType === 'code' && (
                <div className="dataset-dropdown relative">
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-6 text-xs px-2 gap-1"
                    onClick={handleDropdownToggle}
                    disabled={isLoadingDatasets}
                  >
                    <Database className="h-3 w-3" />
                    {isLoadingDatasets ? (
                      <Loader2 className="h-3 w-3 animate-spin" />
                    ) : (
                      <>
                        {selectedDatasets.length > 0 ? `${selectedDatasets.length} selected` : 'Select data'}
                        <ChevronDown className="h-3 w-3" />
                      </>
                    )}
                  </Button>

                  {isDropdownOpen && (
                    <div className="absolute top-full left-0 mt-1 w-64 bg-background border border-border rounded-md shadow-lg z-50 max-h-64 overflow-y-auto">
                      <div className="p-2">
                        <div className="text-xs font-medium mb-2">Select Datasets:</div>
                        {availableDatasets.length === 0 ? (
                          <div className="text-xs text-muted-foreground">No datasets available</div>
                        ) : (
                          <div className="space-y-1">
                            {availableDatasets.map((dataset) => (
                              <div key={dataset.id} className="flex items-center space-x-2">
                                <Checkbox
                                  id={`dataset-${dataset.id}`}
                                  checked={selectedDatasets.some(d => d.id === dataset.id)}
                                  onCheckedChange={(checked) => {
                                    const currentIds = selectedDatasets.map(d => d.id);
                                    const newIds = checked
                                      ? [...currentIds, dataset.id]
                                      : currentIds.filter(id => id !== dataset.id);
                                    handleDatasetSelection(newIds);
                                  }}
                                />
                                <label
                                  htmlFor={`dataset-${dataset.id}`}
                                  className="text-xs cursor-pointer flex-1 truncate"
                                  title={dataset.name}
                                >
                                  {dataset.name}
                                </label>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Server Status Indicator */}
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex items-center gap-1">
                      <Server className={cn(
                        "h-3 w-3",
                        serverStatus.status === 'healthy' ? "text-green-500" :
                        serverStatus.status === 'warning' ? "text-yellow-500" : "text-red-500"
                      )} />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="text-xs">{serverStatus.message}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>

            {/* Right side - Essential controls */}
            <div className="flex items-center gap-1">
              {/* Cell Type Conversion */}
              {onConvertCellType && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0"
                        onClick={() => onConvertCellType(id, cellType === 'code' ? 'markdown' : 'code')}
                      >
                        {cellType === 'code' ? <FileText className="h-3 w-3" /> : <Code2 className="h-3 w-3" />}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="text-xs">Convert to {cellType === 'code' ? 'Markdown' : 'Code'}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}

              {/* AI Assistant Button - only for code cells */}
              {cellType === 'code' && onContentChange && (
                <AIAssistant
                  selectedDatasets={selectedDatasets}
                  language={language as 'sql' | 'python' | 'javascript' | 'markdown'}
                  onCodeGenerated={(code) => {
                    onContentChange(code);
                    toast.success('AI-generated code inserted!');
                  }}
                  editorRef={editorRef}
                />
              )}

              {/* Keyboard Shortcuts Help */}
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div>
                      <KeyboardShortcutsHelp />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="text-xs">Keyboard shortcuts</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              {/* Execution status and time */}
              <div className="flex items-center gap-2">
                {/* Execution time display */}
                {executionTime.startTime && executionTime.endTime && (
                  <div className="text-xs text-muted-foreground">
                    {((executionTime.endTime.getTime() - executionTime.startTime.getTime()) / 1000).toFixed(2)}s
                  </div>
                )}

                {/* Last run time - using execution time as fallback */}
                {executionTime.endTime && (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="text-xs text-muted-foreground">
                          {formatDistanceToNow(executionTime.endTime, { addSuffix: true })}
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="text-xs">Last executed: {executionTime.endTime.toLocaleString()}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}

                {/* Execution status indicator */}
                {result && (
                  <div className={cn(
                    "w-2 h-2 rounded-full",
                    result.error ? "bg-red-500" :
                    isSuccess ? "bg-green-500" : "bg-yellow-500"
                  )} />
                )}
              </div>
            </div>
          </div>

          {/* Editor Area - Adjusted padding for cell number only */}
          <div className="p-0 pl-6"> {/* Add left padding to accommodate cell number */}
            {cellType === 'markdown' ? (
              <MarkdownCell
                content={content}
                onContentChange={onContentChange}
                isEditing={isEditingMarkdown}
                setIsEditing={setIsEditingMarkdown}
              />
            ) : (
              <Editor
                height={editorHeight}
                defaultLanguage={language}
                defaultValue={content || ''}
                theme={theme === 'dark' ? 'vs-dark' : 'light'}
                options={{
                  minimap: { enabled: false },
                  lineNumbers: 'on',
                  folding: false,
                  scrollBeyondLastLine: false,
                  wordWrap: 'on',
                  contextmenu: false,
                  fontSize: 11,
                  lineHeight: 15,
                  padding: { top: 1, bottom: 1 },
                }}
                onMount={handleEditorDidMount}
                onChange={(value) => {
                  if (typeof value === 'string') {
                    debouncedContentChange(value);
                  }
                }}
              />
            )}
          </div>

          {/* Results - More compact */}
          <div className={cn("mt-0 relative", result ? "border-t border-t-border/50" : "")} data-cell-id={id}>
            {result && cellType === 'code' && (
              <QueryResult
                data={result.data}
                output={result.output}
                plots={result.plots}
                result={result.result}
                error={result.error}
                errorDetails={result.errorDetails}
                executionTime={result.executionTime}
                isSuccess={isSuccess && !result.error}
                showGraphicWalker={showGraphicWalker}
                onSaveChart={onSaveChart}
                onSaveTable={onSaveTable}
                onSavePlot={onSavePlot}
                chartType={chartType}
                onChartTypeChange={onChartTypeChange}
                viewMode={viewMode}
                onViewModeChange={onViewModeChange}
                cellId={id}
                language={language} // Pass language to QueryResult
                // Pass cell control props
                onMoveUp={onMoveUp}
                onMoveDown={onMoveDown}
                notes={notes}
                onUpdateNotes={onUpdateNotes}
              />
            )}

          </div>
        </div>
      </Card>
    </div>
  );
}
