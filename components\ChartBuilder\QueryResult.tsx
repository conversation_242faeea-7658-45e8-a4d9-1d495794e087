'use client'

import { useState, useMemo, useRef, useEffect } from 'react'
import { Button } from "@/components/ui/button"
import { BarChart3, AlertCircle, TableIcon, ImageIcon, LineChart, PieChart, AreaChart, Maximize2 } from "lucide-react"
import { NotesEditor } from './NotesEditor'
import { ChartVisualizer } from './ChartVisualizer'
import { Card } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { cn } from "@/lib/utils"
import { GraphicWalkerVisualization } from './GraphicWalker'
import { toast } from "sonner"
import { RichDataTable } from './RichDataTable'

// Default chart colors array
const COLORS = [
  'hsl(var(--chart-1))',
  'hsl(var(--chart-2))',
  'hsl(var(--chart-3))',
  'hsl(var(--chart-4))',
  'hsl(var(--chart-5))'
];

// Streamlit app data interface
interface StreamlitAppData {
  type: 'streamlit_app';
  embed_url: string;
  open_url: string;
  title: string;
  app_id?: string;
  status?: string;
  host_type?: string;
}

interface QueryResultProps {
  data?: any[]
  error?: string
  output?: string
  plots?: string[]
  result?: any
  isSuccess?: boolean
  showGraphicWalker?: boolean
  onSaveChart?: (data: any[], config: any, chartType: 'line' | 'bar' | 'pie' | 'area', chartId?: string) => void
  onSaveTable?: (data: any[], columns: string[], tableId?: string) => void
  onSavePlot?: (plotUrl: string, plotTitle: string, plotId?: string) => void
  errorDetails?: {
    message: string;
    code?: string;
    stack?: string;
    serverTrace?: string;
  };
  executionTime?: number
  chartType?: 'line' | 'bar' | 'pie' | 'area'
  onChartTypeChange?: (type: 'line' | 'bar' | 'pie' | 'area') => void
  viewMode?: 'table' | 'chart' | 'output' | 'graphicwalker'
  onViewModeChange?: (mode: 'table' | 'chart' | 'output' | 'graphicwalker') => void
  cellId?: string
  language?: string
  onMoveUp?: (id: string) => void
  onMoveDown?: (id: string) => void
  notes?: string
  onUpdateNotes?: (id: string, notes: string) => void
}

// Keep track of chart configurations in memory
const chartConfigCache: Record<string, any> = {};

export function QueryResult({
  data,
  error,
  output,
  plots,
  result,
  isSuccess,
  showGraphicWalker = false,
  onSaveChart,
  onSaveTable,
  onSavePlot,
  errorDetails,
  executionTime,
  chartType: propChartType = 'bar',
  onChartTypeChange,
  viewMode: propViewMode = 'table',
  onViewModeChange,
  cellId = 'default',
  language = 'sql',
  onMoveUp,
  onMoveDown,
  notes = '[]',
  onUpdateNotes
}: QueryResultProps) {
  // Track rendered state to prevent unnecessary updates
  const hasRenderedChart = useRef(false);

  // State for tracking iframe loading
  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>({});

  // Use the props as initial values, but check cache first
  const [viewMode, setViewMode] = useState<'table' | 'chart' | 'output' | 'graphicwalker'>(propViewMode);
  const [chartType, setChartType] = useState<'line' | 'bar' | 'pie' | 'area'>(() => {
    return chartConfigCache[cellId]?.type || propChartType;
  });

  const [chartConfig, setChartConfig] = useState<any>(() => {
    return chartConfigCache[cellId] || null;
  });

  // Simple cache-based handler for chart config updates
  const handleChartConfigUpdate = (newConfig: any) => {
    setChartConfig(newConfig);
    chartConfigCache[cellId] = newConfig;
  };

  // Handle chart type change and update cache
  const handleChartTypeChange = (type: 'line' | 'bar' | 'pie' | 'area') => {
    setChartType(type);
    const updatedConfig = { ...chartConfig, type };
    chartConfigCache[cellId] = updatedConfig;
    setChartConfig(updatedConfig);
    if (onChartTypeChange) {
      onChartTypeChange(type);
    }
  };

  // Process data for charting
  const processedData = useMemo(() => {
    if (!data || !Array.isArray(data) || data.length === 0) return [];

    try {
      return data.map((row, index) => {
        if (!row || typeof row !== 'object') {
          return { _index: index };
        }

        const newRow = { ...row, _index: index };

        // Only convert boolean values to numbers for charting
        Object.keys(row).forEach(key => {
          if (typeof row[key] === 'boolean') {
            newRow[`${key}_value`] = row[key] ? 1 : 0;
          }
        });

        return newRow;
      });
    } catch (error) {
      console.error('Error processing data for chart:', error);
      return [];
    }
  }, [data]);

  // Find suitable numeric columns
  const numericColumns = useMemo(() => {
    if (!processedData || processedData.length === 0) return ['_index'];

    const firstRow = processedData[0];
    const numericCols = Object.keys(firstRow).filter(key => {
      const value = firstRow[key];
      return typeof value === 'number' ||
        (typeof value === 'string' && !isNaN(Number(value)) && value.trim() !== '');
    });

    if (!numericCols.includes('_index')) {
      numericCols.push('_index');
    }

    return numericCols;
  }, [processedData]);

  // Find suitable category columns
  const categoryColumns = useMemo(() => {
    if (!processedData || processedData.length === 0) return [];

    const firstRow = processedData[0];
    return Object.keys(firstRow).filter(key =>
      typeof firstRow[key] === 'string' &&
      processedData.length < 100
    );
  }, [processedData]);

  // Get columns for chart configuration
  const columns = useMemo(() =>
    processedData.length > 0 ? Object.keys(processedData[0]) : [],
    [processedData]
  );

  // Simple chart config with default values
  const defaultChartConfig = useMemo(() => ({
    xAxis: categoryColumns[0] || columns[0] || '_index',
    yAxis: numericColumns[0] || '_index',
    title: 'Data Visualization',
    description: 'Chart visualization',
    showLegend: true,
    showLabels: false,
    showGrid: true,
  }), [categoryColumns, numericColumns, columns]);

  // Update the parent when viewMode changes
  const handleViewModeChange = (mode: 'table' | 'chart' | 'output' | 'graphicwalker') => {
    if (viewMode !== mode) {
      setViewMode(mode);
      if (onViewModeChange) {
        onViewModeChange(mode);
      }

      // Reset chart rendering flag when switching away from chart
      if (mode !== 'chart') {
        hasRenderedChart.current = false;
      }
    }
  };

  // Auto-switch to output view if there's output but no plots
  useEffect(() => {
    if (output && !plots?.length && viewMode !== 'output') {
      setViewMode('output');
      if (onViewModeChange) {
        onViewModeChange('output');
      }
    }
  }, [output, plots]);

  // Check if data is available
  const hasData = useMemo(() =>
    data && data.length > 0,
    [data]
  );

  // Handle saving chart to dashboard
  const handleSaveChart = () => {
    if (onSaveChart && processedData.length > 0) {
      const currentConfig = chartConfig || defaultChartConfig;
      const completeConfig = {
        xAxis: currentConfig.xAxis || defaultChartConfig.xAxis,
        yAxis: currentConfig.yAxis || defaultChartConfig.yAxis,
        title: currentConfig.title || defaultChartConfig.title,
        description: currentConfig.description || defaultChartConfig.description,
        showLegend: currentConfig.showLegend !== undefined ? currentConfig.showLegend : defaultChartConfig.showLegend,
        showLabels: currentConfig.showLabels !== undefined ? currentConfig.showLabels : defaultChartConfig.showLabels,
        showGrid: currentConfig.showGrid !== undefined ? currentConfig.showGrid : defaultChartConfig.showGrid,
        type: chartType,
        color: currentConfig.color || COLORS[0],
        ...currentConfig
      };

      const chartId = Date.now().toString(16).padStart(24, '0');
      onSaveChart(processedData, completeConfig, chartType, chartId);
      toast.success("Chart saved to dashboard");
    } else {
      if (!processedData.length) {
        toast.error("No data available to save");
      } else if (!onSaveChart) {
        toast.error("Save functionality not available");
      }
    }
  };

  // Handle saving table to dashboard
  const handleSaveTable = () => {
    if (onSaveTable && data && data.length > 0) {
      const tableId = `table-${cellId}-${Date.now()}`;
      const columns = data.length > 0 ? Object.keys(data[0]) : [];
      onSaveTable(data, columns, tableId);
      toast.success("Table saved to dashboard");
    } else {
      if (!data || data.length === 0) {
        toast.error("No table data available to save");
      } else if (!onSaveTable) {
        toast.error("Save functionality not available");
      }
    }
  };

  // Handle saving plot to dashboard
  const handleSavePlot = (plotUrl: string, plotTitle: string) => {
    if (onSavePlot && plotUrl) {
      const plotId = `plot-${cellId}-${Date.now()}`;
      onSavePlot(plotUrl, plotTitle, plotId);
      toast.success(`${plotTitle} saved to dashboard`);
    } else {
      if (!plotUrl) {
        toast.error("Invalid plot data");
      } else if (!onSavePlot) {
        toast.error("Save functionality not available");
      }
    }
  };

  // Handle cleanup of Streamlit apps
  const handleCleanupStreamlitApp = async (appId: string) => {
    try {
      const response = await fetch('/api/cleanup-streamlit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ app_id: appId }),
      });

      if (response.ok) {
        toast.success('Streamlit app stopped successfully');
      } else {
        toast.error('Failed to stop Streamlit app');
      }
    } catch (error) {
      console.error('Error stopping Streamlit app:', error);
      toast.error('Error stopping Streamlit app');
    }
  };

  // Render empty state if no data
  if (!hasData && !output && !plots?.length && !error) {
    return null;
  }

  return (
    <Card className={cn(
      "w-full overflow-hidden group relative shadow-none border-l-2 border-l-muted border-t-0 border-r-0 border-b-0 rounded-none",
      error ? "border-l-red-500" : isSuccess ? "border-l-green-500" : "border-l-muted"
    )}>

      {/* Controls */}
      <div className="flex flex-wrap items-center justify-between px-2 py-1 border-b bg-muted/30 gap-1">
        <span className="text-xs text-muted-foreground">
          {data && data.length > 0 ? `${data.length} rows` : ''}
          {output ? ' | Has output' : ''}
          {plots && plots.length > 0 ? ` | ${plots.length} plot${plots.length > 1 ? 's' : ''}` : ''}
          {executionTime ? ` | ${(executionTime / 1000).toFixed(2)}s` : ''}
        </span>

        <div className="flex flex-wrap gap-1">
          {error && (
            <span className="text-sm text-red-500 font-medium">
              Execution Failed
            </span>
          )}
          {isSuccess && (
            <span className="text-sm text-green-500 font-medium">
              Execution Successful
            </span>
          )}

          {/* Table button with Notes icon next to it */}
          <div className="flex items-center gap-1">
            {hasData && (
              <Button
                variant={viewMode === 'table' ? 'default' : 'outline'}
                size="sm"
                className="h-6 px-1.5 text-[10px]"
                onClick={() => handleViewModeChange('table')}
              >
                <TableIcon className="h-3 w-3 mr-1" />
                Table
              </Button>
            )}

            {/* Notes Button */}
            {onUpdateNotes && (
              <div className="h-6 flex items-center">
                <NotesEditor
                  notes={notes}
                  onSave={(updatedNotes) => onUpdateNotes(cellId, updatedNotes)}
                />
              </div>
            )}
          </div>

          {/* Chart button */}
          {hasData && (
            <Button
              variant={viewMode === 'chart' ? 'default' : 'outline'}
              size="sm"
              className="h-6 px-1.5 text-[10px]"
              onClick={() => handleViewModeChange('chart')}
            >
              <BarChart3 className="h-3 w-3 mr-1" />
              Chart
            </Button>
          )}

          {/* Visual Explorer button */}
          {hasData && (
            <Button
              variant={viewMode === 'graphicwalker' ? 'default' : 'outline'}
              size="sm"
              className="h-6 px-1.5 text-[10px]"
              onClick={() => handleViewModeChange('graphicwalker')}
            >
              <Maximize2 className="h-3 w-3 mr-1" />
              Visual Explorer
            </Button>
          )}

          {/* Output button */}
          {(output || error || (language === 'python')) && (
            <Button
              variant={viewMode === 'output' ? 'default' : 'outline'}
              size="sm"
              className="h-6 px-1.5 text-[10px]"
              onClick={() => handleViewModeChange('output')}
            >
              <ImageIcon className="h-3 w-3 mr-1" />
              Output
            </Button>
          )}
        </div>
      </div>

      {/* Chart Type Selector - Only visible when chart view is active */}
      {viewMode === 'chart' && (
        <div className="flex flex-wrap items-center gap-1 p-0.5 border-b bg-muted/20">
          {/* Add Save to Dashboard button */}
          {onSaveChart && (
            <Button
              variant="outline"
              size="sm"
              className="h-6 px-1.5 ml-auto text-[10px]"
              onClick={handleSaveChart}
            >
              Save to Dashboard
            </Button>
          )}
        </div>
      )}

      {/* Content Area */}
      <div className="relative">
        {/* Error View */}
        {error && (
          <div className="p-1">
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="ml-2">
                <div className="font-medium">{error}</div>
                {errorDetails && (
                  <div className="mt-2 space-y-2">
                    {errorDetails.code && (
                      <div className="text-xs">
                        <span className="font-semibold">Error code:</span> {errorDetails.code}
                      </div>
                    )}
                    {errorDetails.serverTrace && (
                      <div className="overflow-auto max-h-[150px] text-xs font-mono whitespace-pre-wrap p-1 bg-background/40 rounded-md border">
                        {errorDetails.serverTrace}
                      </div>
                    )}
                    {executionTime && (
                      <div className="text-xs mt-1">
                        Execution time: {(executionTime / 1000).toFixed(2)}s
                      </div>
                    )}
                  </div>
                )}
              </AlertDescription>
            </Alert>
          </div>
        )}

        {/* Main Content Grid */}
        <div className="grid gap-1 px-2 py-1 grid-cols-1">
          {/* Charts Section */}
          {viewMode === 'chart' && hasData && (
            <div className="col-span-full border rounded-md overflow-hidden">
              <div className="h-full">
                <ChartVisualizer
                  key={`chart-${cellId}`}
                  data={processedData}
                  initialChartType={chartType}
                  chartConfig={chartConfig || defaultChartConfig}
                  showConfig={true}
                  onConfigChange={handleChartConfigUpdate}
                  cellId={cellId}
                />
              </div>
            </div>
          )}

          {/* Table View */}
          {viewMode === 'table' && hasData && (
            <RichDataTable
              data={data || []}
              onSaveTable={onSaveTable}
              onSaveToTable={handleSaveTable}
              maxHeight="350px"
            />
          )}

          {/* GraphicWalker View */}
          {viewMode === 'graphicwalker' && hasData && (
            <div className="col-span-full p-0 border rounded-md">
              <GraphicWalkerVisualization
                data={data || []}
                title="Interactive Data Explorer"
                onBack={() => handleViewModeChange('table')}
              />
            </div>
          )}

          {/* Output View - Console output only */}
          {viewMode === 'output' && (output || error) && (
            <div className="border rounded-md">
              <div className="p-3 space-y-3">
                {/* Console Output Section */}
                <div>
                  <div className="text-xs text-muted-foreground mb-2 font-medium flex items-center gap-1">
                    <span>📄 Console Output:</span>
                    {language === 'python' && (
                      <span className="text-[10px] bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 px-1 rounded">
                        Python
                      </span>
                    )}
                  </div>
                  <pre className={cn(
                    "whitespace-pre-wrap p-3 rounded-md text-sm font-mono min-h-[60px]",
                    error ? "bg-red-50 dark:bg-red-950/20 text-red-600 dark:text-red-400 border border-red-200 dark:border-red-800" :
                      output ? "bg-muted border" : "bg-gray-50 dark:bg-gray-900 border border-dashed text-muted-foreground"
                  )}>
                    {error ? `❌ Error:\n${error}` :
                      output ? output :
                        "No console output (code executed silently)"}
                  </pre>
                </div>

                {/* Simple Result Value Display */}
                {result && typeof result !== 'object' && (
                  <div>
                    <div className="text-xs text-muted-foreground mb-2 font-medium flex items-center gap-1">
                      <span>📤 Result:</span>
                      <span className="text-[10px] bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 px-1 rounded">
                        {typeof result}
                      </span>
                    </div>
                    <div className="bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 rounded-md p-3">
                      <pre className="text-sm font-mono text-green-800 dark:text-green-200">
                        {String(result)}
                      </pre>
                    </div>
                  </div>
                )}

                {/* Complex Result Object Display */}
                {result && typeof result === 'object' && !result.type && (
                  <div>
                    <div className="text-xs text-muted-foreground mb-2 font-medium flex items-center gap-1">
                      <span>📤 Result:</span>
                      <span className="text-[10px] bg-purple-100 dark:bg-purple-900 text-purple-700 dark:text-purple-300 px-1 rounded">
                        Object
                      </span>
                    </div>
                    <div className="bg-purple-50 dark:bg-purple-950/20 border border-purple-200 dark:border-purple-800 rounded-md p-3">
                      <pre className="text-sm font-mono text-purple-800 dark:text-purple-200 whitespace-pre-wrap">
                        {JSON.stringify(result, null, 2)}
                      </pre>
                    </div>
                  </div>
                )}

                {/* HTML Outputs (DataFrames, etc.) */}
                {result && typeof result === 'object' && result.html_outputs && result.html_outputs.length > 0 && (
                  <div>
                    <div className="text-xs text-muted-foreground mb-2 font-medium flex items-center gap-1">
                      <span>📊 Data Output:</span>
                      <span className="text-[10px] bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 px-1 rounded">
                        HTML
                      </span>
                    </div>
                    <div className="space-y-2">
                      {result.html_outputs.map((html: string, index: number) => (
                        <div
                          key={index}
                          className="bg-white dark:bg-gray-900 border rounded-md p-3 overflow-auto max-h-96"
                          dangerouslySetInnerHTML={{ __html: html }}
                        />
                      ))}
                    </div>
                  </div>
                )}

                {/* Execution Count Display */}
                {result && typeof result === 'object' && result.execution_count && (
                  <div className="text-xs text-muted-foreground mt-2">
                    In [{result.execution_count}]:
                  </div>
                )}

                {/* Streamlit App Result Display */}
                {result && typeof result === 'object' && (result.type === 'streamlit_app' || result.type === 'streamlit_app_info') && (
                  <div>
                    <div className="text-xs text-muted-foreground mb-2 font-medium flex items-center gap-1">
                      <span>🎈 Streamlit App:</span>
                      <span className="text-[10px] bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 px-1 rounded">
                        Interactive
                      </span>
                    </div>
                    <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
                      {/* App Header */}
                      <div className="flex justify-between items-center mb-3">
                        <div className="flex items-center gap-2">
                          <span className="text-lg">🎈</span>
                          <span className="text-sm font-medium text-blue-700 dark:text-blue-300">
                            {result.title || 'Streamlit App'}
                          </span>
                          <span className="text-xs bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 px-2 py-1 rounded-full">
                            ● {result.status === 'running' ? 'Running' : 'Ready'}
                          </span>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-xs h-6 bg-white dark:bg-gray-800"
                            onClick={() => window.open(result.open_url || result.url, '_blank')}
                          >
                            🔗 Open in New Tab
                          </Button>
                          {onSavePlot && (
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-xs h-6 bg-white dark:bg-gray-800"
                              onClick={() => handleSavePlot(JSON.stringify(result), result.title || 'Streamlit App')}
                            >
                              💾 Save to Dashboard
                            </Button>
                          )}
                          {result.app_id && (
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-xs h-6 text-red-600 hover:text-red-700 bg-white dark:bg-gray-800"
                              onClick={() => handleCleanupStreamlitApp(result.app_id)}
                            >
                              🛑 Stop
                            </Button>
                          )}
                        </div>
                      </div>

                      {/* Embedded App */}
                      <div className="relative bg-white dark:bg-gray-900 rounded-md border border-blue-200 dark:border-blue-700 overflow-hidden">
                        <div className="bg-blue-100 dark:bg-blue-900 px-3 py-1 text-xs text-blue-800 dark:text-blue-200 border-b border-blue-200 dark:border-blue-700">
                          📱 Interactive Streamlit Application
                        </div>
                        <iframe
                          src={result.embed_url}
                          className="w-full border-0"
                          style={{ height: '500px' }}
                          title={result.title || 'Streamlit App'}
                          allow="camera; microphone; geolocation; clipboard-read; clipboard-write"
                          sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-downloads allow-modals"
                          onLoad={() => {
                            console.log('Streamlit app loaded successfully');
                            toast.success(`${result.title || 'Streamlit app'} loaded successfully!`);
                          }}
                          onError={() => {
                            console.warn('Failed to load Streamlit app');
                            toast.error(`Failed to load ${result.title || 'Streamlit app'}`);
                          }}
                        />
                      </div>

                      {/* App Info */}
                      <div className="mt-2 text-xs text-blue-600 dark:text-blue-400">
                        💡 This is your Streamlit app. You can interact with all widgets and features directly here.
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Dedicated Plots Section - Separated from output and always visible when plots exist */}
        {plots && plots.length > 0 && (
          <div className="border rounded-md mt-2 mx-2 mb-2">
            <div className="bg-muted/30 px-3 py-2 border-b">
              <div className="flex justify-between items-center">
                <div className="text-sm font-medium flex items-center gap-2">
                  🎨 Plots ({plots.length})
                </div>
                {onSavePlot && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-xs h-6"
                    onClick={() => {
                      plots.forEach((plot, index) => {
                        if (plot.length >= 100 || plot.startsWith('http')) {
                          handleSavePlot(plot, `Plot ${index + 1}`);
                        }
                      });
                      toast.success(`Saved ${plots.length} plots to dashboard`);
                    }}
                  >
                    💾 Save All Plots
                  </Button>
                )}
              </div>
            </div>
            <div className="p-3">
              <div className="grid gap-4">
                {plots.map((plot, index) => {
                  // Skip invalid plots with better debugging
                  if (plot.length < 100 && !plot.startsWith('http')) {
                    console.warn(`Plot ${index + 1} is invalid:`, {
                      length: plot.length,
                      preview: plot.substring(0, 50),
                      startsWithData: plot.startsWith('data:'),
                      startsWithHttp: plot.startsWith('http')
                    });

                    return (
                      <div key={`invalid-plot-${index}`} className="border rounded-md p-3 bg-yellow-50 dark:bg-yellow-950/20 text-center">
                        <div className="text-yellow-600 dark:text-yellow-400 text-sm">
                          <p>⚠️ Plot {index + 1}: No image data generated</p>
                          <p className="text-xs mt-1">Make sure to use <code>plt.show()</code> after creating your plot</p>
                        </div>
                      </div>
                    );
                  }

                  // Handle Streamlit apps in plots
                  if (plot.includes('streamlit') || plot.includes('"type":"streamlit_app"')) {
                    try {
                      let streamlitAppData: StreamlitAppData;

                      if (plot.startsWith('{') && plot.includes('"type":"streamlit_app"')) {
                        streamlitAppData = JSON.parse(plot);
                      } else if (plot.startsWith('http') && plot.includes('streamlit')) {
                        const embedUrl = plot.includes('?embed=true') ? plot : `${plot}?embed=true&embed_options=hide_loading_screen`;
                        streamlitAppData = {
                          type: 'streamlit_app',
                          embed_url: embedUrl,
                          open_url: plot.split('?')[0],
                          title: `Streamlit App ${index + 1}`
                        };
                      } else {
                        return null;
                      }

                      return (
                        <div key={`streamlit-plot-${index}`} className="border rounded-md p-3 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20">
                          <div className="flex justify-between items-center mb-2">
                            <div className="text-sm font-medium text-blue-700 dark:text-blue-300 flex items-center gap-2">
                              🎈 {streamlitAppData.title}
                            </div>
                            <div className="flex gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                className="text-xs h-6"
                                onClick={() => window.open(streamlitAppData.open_url, '_blank')}
                              >
                                🔗 Open
                              </Button>
                              {onSavePlot && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="text-xs h-6"
                                  onClick={() => handleSavePlot(plot, streamlitAppData.title)}
                                >
                                  💾 Save
                                </Button>
                              )}
                            </div>
                          </div>
                          <div className="bg-white dark:bg-gray-900 rounded border">
                            <iframe
                              src={streamlitAppData.embed_url}
                              className="w-full border-0 rounded"
                              style={{ height: '400px' }}
                              title={streamlitAppData.title}
                              allow="camera; microphone; geolocation; clipboard-read; clipboard-write"
                              sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-downloads allow-modals"
                            />
                          </div>
                        </div>
                      );
                    } catch (error) {
                      console.error('Error parsing Streamlit app:', error);
                      return null;
                    }
                  }

                  // Handle regular image plots
                  let imgSrc = plot;
                  if (!plot.startsWith('data:') && !plot.startsWith('http')) {
                    imgSrc = `data:image/png;base64,${plot}`;
                  }

                  return (
                    <div key={`plot-${index}`} className="border rounded-md p-3 bg-background">
                      <div className="flex justify-between items-center mb-2">
                        <div className="text-sm font-medium text-muted-foreground">
                          Plot {index + 1}
                        </div>
                        {onSavePlot && (
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-xs h-6"
                            onClick={() => handleSavePlot(plot, `Plot ${index + 1}`)}
                          >
                            💾 Save
                          </Button>
                        )}
                      </div>
                      <div className="flex justify-center">
                        <img
                          src={imgSrc}
                          alt={`Plot ${index + 1}`}
                          className="max-w-full h-auto rounded border shadow-sm"
                          style={{ maxHeight: '500px' }}
                          onError={(e) => {
                            const img = e.currentTarget;
                            const container = img.parentElement?.parentElement;
                            if (container) {
                              container.innerHTML = `
                                <div class="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded text-center">
                                  <div class="text-red-600 dark:text-red-400">
                                    <p>❌ Failed to load Plot ${index + 1}</p>
                                    <p class="text-xs mt-1">Image data may be corrupted</p>
                                  </div>
                                </div>
                              `;
                            }
                          }}
                        />
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        )}
      </div>
    </Card>
  );
}
