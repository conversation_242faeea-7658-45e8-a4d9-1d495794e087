"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Plus } from "lucide-react"
import { useRouter } from "next/navigation"

export function CreateWorkflowButton() {
  const router = useRouter()
  
  return (
    <Button
      onClick={() => router.push('/hr/workspace/workflow')}
      className="hover:shadow-md transition-shadow"
    >
      <Plus className="w-4 h-4 mr-2" />
      Create Workflow
    </Button>
  )
}

export function OpenWorkflowButton({ id, className }: { id: string, className?: string }) {
  const router = useRouter()
  
  return (
    <Button
      onClick={() => router.push(`/hr/workspace/workflow?id=${id}`)}
      variant="outline"
      size="sm"
      className={className || "w-full group-hover:border-primary/50 transition-colors"}
    >
      Open Workflow
    </Button>
  )
}