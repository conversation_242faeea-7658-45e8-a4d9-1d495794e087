'use client'

import React, { useMemo, memo } from 'react'
import { ChartConfig, Series, ChartType } from '../types'
import { ZoomableChart } from './ZoomableChart'
import { MultiSeriesChart } from './MultiSeriesChart'
import { TableView } from './TableView'
import { Table } from "lucide-react"
import { Button } from "@/components/ui/button"

interface ChartPreviewProps {
  config: ChartConfig
  series: Series[]
  data: Record<string, any>[]
  isConfigMode: boolean
  fullHeight?: boolean
  isDashboardChart?: boolean // Flag to indicate if this is used in a dashboard card
}

// Use memo to prevent unnecessary re-renders
export const ChartPreview = memo(function ChartPreview({
  config,
  series,
  data,
  isConfigMode,
  fullHeight = false,
  isDashboardChart = false
}: ChartPreviewProps) {
  // If we don't have data or essential config, show empty state
  if (
    data.length === 0 ||
    !config.xAxis ||
    !config.yAxis
  ) {
    return (
      <div className={`flex items-center justify-center text-muted-foreground p-4 bg-muted/20 rounded-md ${isConfigMode ? 'aspect-video' : 'h-full'}`}>
        {!config.xAxis || !config.yAxis
          ? 'Configure X and Y axes to create a chart'
          : 'No data available with current filters and settings'
        }
      </div>
    )
  }

  // If table view is enabled, show the table view
  if (config.showAsTable) {
    return (
      <div
        className={`w-full ${isConfigMode ? 'aspect-video' : ''} ${fullHeight ? 'h-full' : 'min-h-[250px]'} ${isDashboardChart ? 'dashboard-chart' : 'notebook-chart'}`}
        style={{
          height: fullHeight ? '100%' : 'auto',
          position: isDashboardChart ? 'absolute' : 'relative',
          ...(isDashboardChart ? { inset: 0 } : {}),
          overflow: 'hidden'
        }}
      >
        <TableView
          data={data}
          xAxis={config.xAxis}
          yAxis={config.yAxis}
          series={series}
          isMultiSeries={config.multiSeries && series.length > 1}
          title={config.title}
          description={config.description}
          isDashboardChart={isDashboardChart}
        />
      </div>
    )
  }

  // Determine whether to show multi-series or single series chart
  const isMultiSeries = config.multiSeries && config.type !== 'pie' && series.length > 1

  // Use chartTypes array if available, otherwise fall back to single type
  const chartTypes = config.chartTypes && config.chartTypes.length > 0
    ? config.chartTypes
    : [config.type];

  // For multiple chart types, we need to create a combined chart
  // Currently we'll use the first chart type for multi-series charts
  // and implement the combined chart rendering in the chart components
  const primaryChartType = chartTypes[0];

  return (
    <div
      className={`w-full ${isConfigMode ? 'aspect-video' : ''} ${fullHeight ? 'h-full' : 'min-h-[250px]'} ${isDashboardChart ? 'dashboard-chart' : 'notebook-chart'}`}
      style={{
        height: fullHeight ? '100%' : 'auto',
        position: isDashboardChart ? 'absolute' : 'relative',
        ...(isDashboardChart ? { inset: 0 } : {}),
        overflow: 'hidden'
      }}
    >
      {isMultiSeries ? (
        <MultiSeriesChart
          data={data}
          chartType={primaryChartType as 'line' | 'bar' | 'area'}
          chartTypes={chartTypes.filter(type =>
            ['line', 'bar', 'area'].includes(type)
          ) as ('line' | 'bar' | 'area')[]}
          xAxis={config.xAxis}
          xAxisColumns={config.xAxisColumns || [config.xAxis]}
          series={series}
          showGrid={config.showGrid}
          showLegend={config.showLegend}
          showLabels={config.showLabels}
          enableZoom={config.enableZoom}
          isDashboardChart={isDashboardChart}
          chartTypeStyles={config.chartTypeStyles}
          showMean={config.showMean}
          showMedian={config.showMedian}
          showStdDev={config.showStdDev}
        />
      ) : (
        <ZoomableChart
          data={data}
          chartType={primaryChartType as 'line' | 'bar' | 'pie' | 'area'}
          chartTypes={chartTypes.filter(type =>
            ['line', 'bar', 'pie', 'area'].includes(type)
          ) as ('line' | 'bar' | 'pie' | 'area')[]}
          xAxis={config.xAxis}
          xAxisColumns={config.xAxisColumns || [config.xAxis]}
          yAxis={config.yAxis}
          color={config.color}
          chartTypeStyles={config.chartTypeStyles}
          showGrid={config.showGrid}
          showLegend={config.showLegend}
          showLabels={config.showLabels}
          customLabel={config.customLabel || config.yAxis}
          isZoomable={config.enableZoom}
          isDashboardChart={isDashboardChart}
          showMean={config.showMean}
          showMedian={config.showMedian}
          showStdDev={config.showStdDev}
        />
      )}
    </div>
  )
});