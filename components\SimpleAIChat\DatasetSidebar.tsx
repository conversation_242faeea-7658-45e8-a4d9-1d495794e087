'use client'

import React from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Checkbox } from '@/components/ui/checkbox'
import { 
  Database, 
  BarChart3, 
  FileText,
  Layers,
  TrendingUp
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface Dataset {
  id: string
  name: string
  description?: string
  data: any[]
  headers: string[]
}

interface DatasetSidebarProps {
  datasets: Dataset[]
  selectedDatasets: string[]
  onDatasetToggle: (datasetId: string) => void
}

export function DatasetSidebar({ 
  datasets, 
  selectedDatasets, 
  onDatasetToggle 
}: DatasetSidebarProps) {
  const selectedDatasetsInfo = datasets.filter(d => selectedDatasets.includes(d.id))
  const totalRows = selectedDatasetsInfo.reduce((sum, d) => sum + (d.data?.length || 0), 0)
  const totalColumns = selectedDatasetsInfo.reduce((sum, d) => sum + (d.headers?.length || 0), 0)

  return (
    <div className="w-80 flex-shrink-0 flex flex-col h-full bg-card border-r">
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center gap-3 mb-3">
          <div className="p-2 bg-primary rounded-lg shadow-sm">
            <Database className="h-5 w-5 text-primary-foreground" />
          </div>
          <div>
            <h2 className="font-semibold text-lg">Datasets</h2>
            <p className="text-sm text-muted-foreground">
              Select data sources for analysis
            </p>
          </div>
        </div>

        {/* Quick Stats */}
        {selectedDatasets.length > 0 && (
          <div className="grid grid-cols-2 gap-2">
            <div className="p-2 bg-muted rounded-lg">
              <div className="flex items-center gap-1">
                <Layers className="h-3 w-3 text-primary" />
                <span className="text-xs font-medium text-primary">Selected</span>
              </div>
              <p className="text-sm font-bold text-foreground">
                {selectedDatasets.length}
              </p>
            </div>
            <div className="p-2 bg-muted rounded-lg">
              <div className="flex items-center gap-1">
                <TrendingUp className="h-3 w-3 text-primary" />
                <span className="text-xs font-medium text-primary">Rows</span>
              </div>
              <p className="text-sm font-bold text-foreground">
                {totalRows.toLocaleString()}
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Dataset List */}
      <div className="flex-1 flex flex-col min-h-0">
        {datasets.length === 0 ? (
          <div className="flex-1 flex flex-col items-center justify-center p-8 text-center">
            <div className="w-16 h-16 bg-muted rounded-2xl flex items-center justify-center mb-4">
              <Database className="h-8 w-8 text-muted-foreground" />
            </div>
            <h3 className="font-medium text-foreground mb-2">
              No datasets available
            </h3>
            <p className="text-sm text-muted-foreground">
              Upload some data to get started with AI analysis
            </p>
          </div>
        ) : (
          <ScrollArea className="flex-1 p-4">
            <div className="space-y-3">
              {datasets.map((dataset) => {
                const isSelected = selectedDatasets.includes(dataset.id)
                const rowCount = dataset.data?.length || 0
                const columnCount = dataset.headers?.length || 0

                return (
                  <Card
                    key={dataset.id}
                    className={cn(
                      "cursor-pointer transition-all duration-200 hover:shadow-md border-2",
                      isSelected
                        ? "border-primary bg-muted shadow-md"
                        : "border-border hover:border-primary/50"
                    )}
                    onClick={() => onDatasetToggle(dataset.id)}
                  >
                    <CardHeader className="pb-2">
                      <div className="flex items-start gap-3">
                        <Checkbox
                          checked={isSelected}
                          onChange={() => {}} // Handled by card click
                          className="mt-1"
                        />
                        <div className="flex-1 min-w-0">
                          <CardTitle className="text-sm font-medium truncate">
                            {dataset.name}
                          </CardTitle>
                          {dataset.description && (
                            <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                              {dataset.description}
                            </p>
                          )}
                        </div>
                      </div>
                    </CardHeader>
                    
                    <CardContent className="pt-0">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="flex items-center gap-1">
                            <BarChart3 className="h-3 w-3 text-muted-foreground" />
                            <span className="text-xs text-muted-foreground">
                              {rowCount.toLocaleString()} rows
                            </span>
                          </div>
                          <div className="flex items-center gap-1">
                            <FileText className="h-3 w-3 text-muted-foreground" />
                            <span className="text-xs text-muted-foreground">
                              {columnCount} cols
                            </span>
                          </div>
                        </div>

                        {isSelected && (
                          <Badge variant="default" className="text-xs">
                            Selected
                          </Badge>
                        )}
                      </div>

                      {/* Column Preview */}
                      {dataset.headers && dataset.headers.length > 0 && (
                        <div className="mt-2 pt-2 border-t">
                          <p className="text-xs text-muted-foreground mb-1">Columns:</p>
                          <div className="flex flex-wrap gap-1">
                            {dataset.headers.slice(0, 3).map((header, i) => (
                              <Badge key={i} variant="outline" className="text-xs">
                                {header}
                              </Badge>
                            ))}
                            {dataset.headers.length > 3 && (
                              <Badge variant="outline" className="text-xs">
                                +{dataset.headers.length - 3} more
                              </Badge>
                            )}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </ScrollArea>
        )}
      </div>

      {/* Summary Footer */}
      {selectedDatasets.length > 0 && (
        <div className="p-4 border-t bg-muted/50">
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Analysis Ready</span>
              <Badge variant="secondary">
                {selectedDatasets.length} dataset{selectedDatasets.length !== 1 ? 's' : ''}
              </Badge>
            </div>
            <div className="grid grid-cols-2 gap-2 text-xs text-muted-foreground">
              <div>
                <span className="font-medium">{totalRows.toLocaleString()}</span> total rows
              </div>
              <div>
                <span className="font-medium">{totalColumns}</span> total columns
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
