#!/usr/bin/env python3
"""
Debug script for plot rendering issues
This script generates test plots and prints the base64 data
"""

import matplotlib.pyplot as plt
import numpy as np
import base64
import io
import sys
import json

def get_plot_as_base64(dpi=100, format='png'):
    """Convert matplotlib plot to base64 string"""
    buf = io.BytesIO()
    plt.savefig(buf, format=format, dpi=dpi, bbox_inches='tight')
    buf.seek(0)
    img_str = base64.b64encode(buf.read()).decode('utf-8')
    buf.close()
    return img_str

def create_test_plot():
    """Create a simple test plot"""
    plt.figure(figsize=(10, 6))
    x = np.linspace(0, 10, 100)
    y = np.sin(x)
    plt.plot(x, y, 'b-', linewidth=2)
    plt.title('Test Plot')
    plt.xlabel('X axis')
    plt.ylabel('Y axis')
    plt.grid(True)
    plt.tight_layout()
    
    # Get the plot as base64
    img_str = get_plot_as_base64()
    
    # Print info about the base64 string
    print(f"Base64 length: {len(img_str)}")
    print(f"First 50 chars: {img_str[:50]}...")
    print(f"Last 50 chars: {img_str[-50:]}...")
    
    # Return the full base64 string
    return img_str

def test_plot_rendering():
    """Test different plot rendering approaches"""
    # Create a simple plot
    plt.figure(figsize=(8, 5))
    x = np.arange(0, 10, 0.1)
    y = np.sin(x)
    plt.plot(x, y)
    plt.title('Sine Wave')
    plt.grid(True)
    plt.tight_layout()
    
    # Get base64 data
    base64_data = get_plot_as_base64()
    
    # Create different formats for testing
    formats = {
        'base64_only': base64_data,
        'data_url_png': f"data:image/png;base64,{base64_data}",
        'data_url_jpeg': f"data:image/jpeg;base64,{base64_data}",
    }
    
    # Print info about each format
    print("\nTest Plot Formats:")
    for name, data in formats.items():
        print(f"{name}: length={len(data)}, starts_with={data[:20]}...")
    
    # Return the formats as JSON
    return json.dumps(formats)

def main():
    """Main function"""
    print("Plot Rendering Debug Tool")
    print("========================")
    
    # Create and display a test plot
    base64_data = create_test_plot()
    
    # Test different rendering approaches
    formats_json = test_plot_rendering()
    
    # Print instructions
    print("\nInstructions:")
    print("1. Check that the base64 data is valid (should be long and contain only valid base64 characters)")
    print("2. Try rendering the plot with different formats")
    print("3. Use the data URL format in your application")
    
    # Return the base64 data as the result
    return f"data:image/png;base64,{base64_data}"

if __name__ == "__main__":
    result = main()
    print("\nFull data URL (first 100 chars):")
    print(result[:100] + "...")