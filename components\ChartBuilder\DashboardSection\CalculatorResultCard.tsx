'use client'

import React, { useState, useRef } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Edit2,
  Trash2,
  Copy,
  Calculator,
  CheckCircle,
  XCircle,
  Hash,
  GripVertical,
  TrendingUp,
  DollarSign,
  Users,
  Target,
  BarChart3,
  PieChart,
  Activity,
  Zap,
  Star,
  Award,
  Briefcase,
  Calendar,
  FileText,
  Settings,
  Heart,
  Shield,
  Globe,
  Home,
  Mail,
  Phone,
  MapPin,
  Camera,
  Music,
  Video,
  Image,
  Book,
  Bookmark,
  Flag,
  Gift,
  Key,
  Lock,
  Unlock,
  Search,
  Filter,
  Download,
  Upload,
  Share,
  Link,
  Bell,
  AlertCircle,
  Info,
  HelpCircle,
  Plus,
  Minus,
  X,
  Check,
  ChevronUp,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  ArrowUp,
  ArrowDown,
  ArrowLeft,
  ArrowRight,
  RefreshCw,
  RotateCcw,
  RotateCw,
  Maximize,
  Minimize,
  Eye,
  EyeOff,
  ThumbsUp,
  ThumbsDown,
  MessageCircle,
  Send,
  Paperclip,
  Smile,
  Frown,
  Meh
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import { CalculatorResultItem } from './types'
import { useDashboardStore } from '@/lib/dashboardStore'
import { toast } from 'sonner'
import { cn } from '@/lib/utils'
import './styles/dashboard-chart-fix.css'

// Available icons for calculator cards
const AVAILABLE_ICONS = [
  { name: 'Calculator', icon: Calculator, color: 'text-blue-600' },
  { name: 'TrendingUp', icon: TrendingUp, color: 'text-green-600' },
  { name: 'DollarSign', icon: DollarSign, color: 'text-emerald-600' },
  { name: 'Users', icon: Users, color: 'text-purple-600' },
  { name: 'Target', icon: Target, color: 'text-red-600' },
  { name: 'BarChart3', icon: BarChart3, color: 'text-indigo-600' },
  { name: 'PieChart', icon: PieChart, color: 'text-pink-600' },
  { name: 'Activity', icon: Activity, color: 'text-orange-600' },
  { name: 'Zap', icon: Zap, color: 'text-yellow-600' },
  { name: 'Star', icon: Star, color: 'text-amber-600' },
  { name: 'Award', icon: Award, color: 'text-cyan-600' },
  { name: 'Briefcase', icon: Briefcase, color: 'text-slate-600' },
  { name: 'Calendar', icon: Calendar, color: 'text-teal-600' },
  { name: 'FileText', icon: FileText, color: 'text-gray-600' },
  { name: 'Settings', icon: Settings, color: 'text-stone-600' },
  { name: 'Heart', icon: Heart, color: 'text-rose-600' },
  { name: 'Shield', icon: Shield, color: 'text-blue-700' },
  { name: 'Globe', icon: Globe, color: 'text-green-700' },
  { name: 'Home', icon: Home, color: 'text-blue-500' },
  { name: 'Hash', icon: Hash, color: 'text-gray-500' }
]

interface CalculatorResultCardProps {
  item: CalculatorResultItem
  isEditMode?: boolean
  onUpdateCalculator?: (calculatorId: string, updates: Partial<CalculatorResultItem>) => void
  onRemoveCalculator?: (calculatorId: string) => void
  onToggleFullscreen?: (calculatorId: string) => void
  onEdit?: (item: CalculatorResultItem) => void
  onDelete?: (id: string) => void
}

export function CalculatorResultCard({
  item,
  isEditMode = false,
  onUpdateCalculator,
  onRemoveCalculator,
  onToggleFullscreen,
  onEdit,
  onDelete
}: CalculatorResultCardProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editTitle, setEditTitle] = useState(item.title || '')
  const [editDescription, setEditDescription] = useState(item.description || '')
  const [selectedIcon, setSelectedIcon] = useState(item.icon || 'Calculator')
  const [isDragging, setIsDragging] = useState(false)
  const cardRef = useRef<HTMLDivElement>(null)
  const calculatorContainerRef = useRef<HTMLDivElement>(null)

  const { updateCalculatorResult, removeCalculatorResult } = useDashboardStore()

  // Function to trigger window resize
  const triggerResize = () => {
    window.dispatchEvent(new Event('resize'))
  }

  // Get icon component and color
  const getIconInfo = (iconName: string) => {
    const iconInfo = AVAILABLE_ICONS.find(icon => icon.name === iconName) || AVAILABLE_ICONS[0]
    return iconInfo
  }

  // Format the result for display
  const formatResult = (result: any, resultType?: string) => {
    if (result === null || result === undefined) return 'No result'

    if (resultType === 'error') return result

    if (typeof result === 'number') {
      // Format numbers with appropriate precision
      if (Number.isInteger(result)) return result.toString()
      if (Math.abs(result) >= 1000000) return result.toExponential(2)
      return result.toFixed(6).replace(/\.?0+$/, '')
    }

    return String(result)
  }

  // Get result type icon and color
  const getResultTypeInfo = (resultType?: string, result?: any) => {
    if (resultType === 'error' || (result === null && item.formula)) {
      return { icon: XCircle, color: 'text-red-500', bgColor: 'bg-red-50 dark:bg-red-900/20' }
    }
    if (typeof result === 'number') {
      return { icon: Hash, color: 'text-blue-500', bgColor: 'bg-blue-50 dark:bg-blue-900/20' }
    }
    return { icon: CheckCircle, color: 'text-green-500', bgColor: 'bg-green-50 dark:bg-green-900/20' }
  }

  const resultInfo = getResultTypeInfo(item.resultType, item.result)
  const ResultIcon = resultInfo.icon

  // Handle save edit
  const handleSaveEdit = () => {
    const updates = {
      title: editTitle.trim() || 'Calculator Result',
      description: editDescription.trim(),
      icon: selectedIcon
    }

    if (onUpdateCalculator) {
      onUpdateCalculator(item.id, updates)
    } else {
      updateCalculatorResult(item.id, updates)
    }

    setIsEditing(false)
    toast.success('Calculator result updated')
  }

  // Handle delete
  const handleDelete = () => {
    if (onRemoveCalculator) {
      onRemoveCalculator(item.id)
    } else if (onDelete) {
      onDelete(item.id)
    } else {
      removeCalculatorResult(item.id)
    }
    toast.success('Calculator result deleted')
  }

  // Copy result to clipboard
  const copyResult = () => {
    const textToCopy = `${item.formula} = ${formatResult(item.result, item.resultType)}`
    navigator.clipboard.writeText(textToCopy)
    toast.success('Result copied to clipboard')
  }

  // Copy formula to clipboard
  const copyFormula = () => {
    navigator.clipboard.writeText(item.formula)
    toast.success('Formula copied to clipboard')
  }

  // Format timestamp
  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleString()
  }

  // Get current icon info
  const currentIconInfo = getIconInfo(item.icon || selectedIcon)
  const CurrentIcon = currentIconInfo.icon

  return (
    <div
      ref={cardRef}
      className={cn(
        "relative rounded-lg bg-card text-card-foreground dashboard-card-group",
        isDragging ? "z-50 cursor-grabbing" : isEditMode ? "cursor-grab" : "",
        isEditMode ? "border shadow-sm hover:border-primary" : "border-0 shadow-none",
        "dashboard-item-container", // Add this class for grid layout integration
        !isEditMode && "no-border" // Add no-border class in view mode for cleaner appearance
      )}
      data-item-type="calculator"
      data-item-id={item.id}
      data-is-dragging={isDragging}
      data-is-resizing={false}
      style={{
        width: '100%',
        height: '100%',
        display: 'grid',
        gridTemplateRows: item.description
          ? '24px 1fr 20px'
          : '24px 1fr',
        overflow: 'hidden',
        transform: 'translate3d(0,0,0)', // Force GPU acceleration
        willChange: isEditMode ? 'transform, box-shadow' : 'auto'
      }}
    >
      {/* Draggable Handle - Positioned at the top */}
      {isEditMode && (
        <div className="draggable-handle absolute top-0 left-0 w-full h-6 flex items-center justify-center cursor-grab rounded-t-md z-10 bg-muted/50">
          <GripVertical className="h-4 w-4 text-primary" />
        </div>
      )}

      {/* Card Header */}
      <div className={`flex items-center justify-between px-1 py-0.5 ${isEditMode ? 'border-b border-border' : ''} bg-card`}>
        <div className="flex items-center gap-1">
          <CurrentIcon className={`h-3 w-3 ${currentIconInfo.color} flex-shrink-0`} />
          <h3 className="text-xs font-medium truncate text-foreground">{item.title || 'Calculator Result'}</h3>
          <span className="text-[10px] text-muted-foreground bg-muted/20 px-1 py-0.5 rounded ml-1">
            {item.resultType || 'calc'}
          </span>
        </div>
        <div className="flex items-center gap-1">
          {isEditMode && (
            <Button
              variant="ghost"
              size="sm"
              className="h-5 w-5 p-0 hover:bg-blue-100 hover:text-blue-600 transition-colors non-draggable"
              onClick={() => setIsEditing(!isEditing)}
            >
              <Edit2 className="h-3 w-3" />
            </Button>
          )}
        </div>
      </div>

      {/* Hover-based Remove Button */}
      {isEditMode && (
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="card-remove-button h-6 w-6 p-0 hover:bg-red-100 hover:text-red-600 transition-all non-draggable"
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Delete Calculator Result</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to delete this calculator result? This action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDelete}
                className="bg-red-600 hover:bg-red-700"
              >
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}

      {/* Calculator Content Area */}
      <div
        ref={calculatorContainerRef}
        className="calculator-container dark:bg-black bg-white"
        style={{
          width: '100%',
          height: '100%',
          overflow: 'hidden',
          display: 'flex',
          flexDirection: 'column',
          marginTop: isEditMode ? '20px' : '0', // Add margin when in edit mode to account for the drag handle
          minHeight: '60px', // Ensure minimum height for small cards
          position: 'relative' // Ensure proper positioning context
        }}
      >
        {/* VIEW MODE - Simple display with icon, title and result */}
        {!isEditMode && !isEditing ? (
          <div className="h-full flex flex-col justify-center items-center p-2 text-center">
            {/* Icon */}
            <div className="mb-2">
              <CurrentIcon className={`h-6 w-6 ${currentIconInfo.color}`} />
            </div>
            {/* Title */}
            <div className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2 line-clamp-2">
              {item.title || 'Calculator Result'}
            </div>
            {/* Result - Large and prominent */}
            <div className="text-xl font-bold text-gray-900 dark:text-gray-100">
              {formatResult(item.result, item.resultType)}
            </div>
          </div>
        ) : (
          /* EDIT MODE - Full interface with all options */
          <div className="h-full flex flex-col p-2 space-y-2 overflow-y-auto">{/* Icon Selection - only show when editing */}
            {isEditing && (
              <div className="space-y-2">
                <label className="text-xs font-medium text-muted-foreground">Choose Icon:</label>
                <div className="grid grid-cols-4 gap-1 max-h-20 overflow-y-auto">
                  {AVAILABLE_ICONS.slice(0, 12).map((iconInfo) => {
                    const IconComponent = iconInfo.icon
                    return (
                      <Button
                        key={iconInfo.name}
                        variant={selectedIcon === iconInfo.name ? "default" : "outline"}
                        size="sm"
                        className="h-6 w-6 p-0"
                        onClick={() => setSelectedIcon(iconInfo.name)}
                      >
                        <IconComponent className={`h-3 w-3 ${iconInfo.color}`} />
                      </Button>
                    )
                  })}
                </div>
              </div>
            )}

            {/* Title editing */}
            {isEditing && (
              <div className="space-y-1">
                <label className="text-xs font-medium text-muted-foreground">Title:</label>
                <Input
                  value={editTitle}
                  onChange={(e) => setEditTitle(e.target.value)}
                  className="h-6 text-xs"
                  placeholder="Calculator Result"
                />
              </div>
            )}

            {/* Formula display */}
            <div className="space-y-1">
              <label className="text-xs font-medium text-muted-foreground">Formula:</label>
              <div className="text-xs font-mono bg-muted/20 p-1 rounded border truncate">
                {item.formula}
              </div>
            </div>

            {/* Result display */}
            <div className="flex-1 flex flex-col justify-center items-center">
              <div className="text-lg font-bold text-center">
                {formatResult(item.result, item.resultType)}
              </div>
              <div className="text-xs text-muted-foreground">
                {formatTimestamp(item.timestamp)}
              </div>
            </div>

            {/* Edit Actions */}
            {isEditing && (
              <div className="flex gap-1">
                <Button size="sm" onClick={handleSaveEdit} className="h-6 text-xs flex-1">
                  Save
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setIsEditing(false)}
                  className="h-6 text-xs flex-1"
                >
                  Cancel
                </Button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Calculator Footer - Only shown if there's a description */}
      {item.description && (
        <div className={`${isEditMode ? 'border-t dark:border-gray-700' : ''} dark:bg-black bg-white text-[10px] text-muted-foreground overflow-hidden`}>
          <p className="truncate px-1">{item.description}</p>
        </div>
      )}

      {/* Resize Handles */}
      {isEditMode && (
        <div
          className="absolute bottom-0 right-0 w-4 h-4 cursor-nwse-resize react-resizable-handle react-resizable-handle-se"
          style={{
            backgroundColor: 'rgba(0, 0, 0, 0.05)',
            zIndex: 10,
            borderRadius: '0 0 4px 0'
          }}
        />
      )}
    </div>
  )
}
