"use client"

import React, { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { MessageSquare, FileText, Plus, Globe, Search, Clock, User2, Kanban, Calendar, Folder } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { toast } from "sonner"
import { motion } from "framer-motion"
import { createChannel, getChannels } from "@/actions/chatActions"
import { fetchNotesTreeData } from "@/actions/treeActions"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { GoWorkflow } from "react-icons/go"
import { formatDistanceToNow, format } from "date-fns"
import { ActivityGraph } from "@/components/workspace/activity-graph"
import { getAllHistoricalActivityData } from "@/app/actions/activity"
import { getSavedDiagrams } from "@/app/actions/diagram"
import { getNotes } from "@/actions/actions"
import Image from "next/image"

interface Channel {
  id: string
  name: string
  description?: string
  role: string
  isCreator: boolean
}

interface WorkflowNode {
  id: string;
  // Add other node properties as needed
}

interface WorkflowEdge {
  id: string;
  // Add other edge properties as needed
}

interface WorkflowContent {
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
}

interface Workflow {
  id: string;
  title: string;
  content: WorkflowContent;
  clerkId: string;
  isPublic: boolean;
  publicId: string | null;
  createdAt: Date;
  updatedAt: Date;
}

interface TreeNode {
  id: string;
  name: string;
  type: 'folder' | 'file';
  children?: TreeNode[];
  content?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

interface Note {
  id: string;
  title: string;
  content: string;
  coverImage: string | null;
  createdAt: Date;
  updatedAt: Date;
}

interface ActivityData {
  id: string;
  // Add other necessary properties based on your API
}

export function QuickAccessCards() {
  return (
    <>
      {/* Card 1: Rag System */}
      <Link href="/hr/ragbook">
        <Card className="group relative overflow-hidden rounded-3xl border border-border shadow-sm hover:shadow-lg transition-all duration-500 backdrop-blur-lg bg-background/80">
          {/* Glassmorphism Overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
          <CardHeader className="relative z-10 space-y-4 p-6">
            <div className="flex items-center justify-between">
              <CardTitle className="text-2xl font-semibold text-foreground group-hover:text-blue-400 transition-colors duration-300">
                Rag System
              </CardTitle>
              <Kanban className="w-8 h-8 text-blue-400 group-hover:scale-110 transition-transform duration-300" />
            </div>
            <CardDescription className="text-muted-foreground group-hover:text-foreground transition-colors duration-300">
              Store PDFs as RAG for AI retrieval.
            </CardDescription>
          </CardHeader>
        </Card>
      </Link>

      {/* Card 2: Calendar */}
      <Link href="/hr/workspace/callend">
        <Card className="group relative overflow-hidden rounded-3xl border border-border shadow-sm hover:shadow-lg transition-all duration-500 backdrop-blur-lg bg-background/80">
          {/* Glassmorphism Overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-pink-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
          <CardHeader className="relative z-10 space-y-4 p-6">
            <div className="flex items-center justify-between">
              <CardTitle className="text-2xl font-semibold text-foreground group-hover:text-purple-400 transition-colors duration-300">
                Calendar
              </CardTitle>
              <Calendar className="w-8 h-8 text-purple-400 group-hover:scale-110 transition-transform duration-300" />
            </div>
            <CardDescription className="text-muted-foreground group-hover:text-foreground transition-colors duration-300">
              Schedule and manage events efficiently.
            </CardDescription>
          </CardHeader>
        </Card>
      </Link>

      {/* Card 3: Workflow */}
      <Link href="/hr/workspace/workflow">
        <Card className="group relative overflow-hidden rounded-3xl border border-border shadow-sm hover:shadow-lg transition-all duration-500 backdrop-blur-lg bg-background/80">
          {/* Glassmorphism Overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/10 to-green-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
          <CardHeader className="relative z-10 space-y-4 p-6">
            <div className="flex items-center justify-between">
              <CardTitle className="text-2xl font-semibold text-foreground group-hover:text-emerald-400 transition-colors duration-300">
                Workflow
              </CardTitle>
              <GoWorkflow className="w-8 h-8 text-emerald-400 group-hover:scale-110 transition-transform duration-300" />
            </div>
            <CardDescription className="text-muted-foreground group-hover:text-foreground transition-colors duration-300">
              Manage business processes seamlessly.
            </CardDescription>
          </CardHeader>
        </Card>
      </Link>
    </>
  );
}

async function ActivitySection() {
  const activityData = await getAllHistoricalActivityData();
  
  const formattedData = activityData.success && activityData.data 
    ? {
        notes: activityData.data.notes || [],
        workflows: activityData.data.workflows || [],
        chats: activityData.data.chats || []
      } 
    : { notes: [], workflows: [], chats: [] };
    
  return (
    <ActivityGraph
      data={formattedData}
      isLoading={false}
    />
  );
}

async function WorkflowSection() {
  const router = useRouter();
  const workflowsResult = await getSavedDiagrams();
  
  const workflows = workflowsResult.success && workflowsResult.data 
    ? workflowsResult.data.map(workflow => {
        const content = workflow.content || {};
        const safeContent = {
          nodes: Array.isArray((content as any)?.nodes) ? (content as any).nodes : [],
          edges: Array.isArray((content as any)?.edges) ? (content as any).edges : []
        };
        
        return {
          id: workflow.id,
          title: workflow.title,
          content: safeContent,
          clerkId: workflow.clerkId,
          isPublic: workflow.isPublic,
          publicId: workflow.publicId,
          createdAt: workflow.createdAt,
          updatedAt: workflow.updatedAt
        };
      }) 
    : [];
  
  return (
    <Card className="relative border-border/50">
      <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500/20 via-primary/20 to-transparent" />
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div>
          <CardTitle className="text-2xl">My Workflows</CardTitle>
          <CardDescription>Your saved workflow diagrams</CardDescription>
        </div>
        <Button
          onClick={() => router.push('/hr/workspace/workflow')}
          className="hover:shadow-md transition-shadow"
        >
          <Plus className="w-4 h-4 mr-2" />
          Create Workflow
        </Button>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {workflows.map((workflow) => (
            <motion.div
              key={workflow.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <Card className="group hover:shadow-lg transition-all duration-300 border-border/50">
                <CardHeader className="pb-3">
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-lg font-semibold line-clamp-1">
                        {workflow.title}
                      </CardTitle>
                      <CardDescription className="mt-1">
                        Created {formatDistanceToNow(new Date(workflow.createdAt))} ago
                      </CardDescription>
                    </div>
                    {workflow.isPublic && (
                      <Badge variant="outline">Public</Badge>
                    )}
                  </div>
                </CardHeader>
                <CardContent className="pt-4">
                  <div className="space-y-4">
                    <div className="flex items-center text-sm text-muted-foreground">
                      <div className="flex items-center">
                        <div className="w-2 h-2 rounded-full bg-primary/60 mr-2" />
                        <span>{workflow.content.nodes.length} Nodes</span>
                      </div>
                      <div className="flex items-center ml-4">
                        <div className="w-2 h-2 rounded-full bg-primary/60 mr-2" />
                        <span>{workflow.content.edges.length} Connections</span>
                      </div>
                    </div>
                    <Button
                      onClick={() => router.push(`/hr/workspace/workflow?id=${workflow.id}`)}
                      variant="outline"
                      size="sm"
                      className="w-full group-hover:border-primary/50 transition-colors"
                    >
                      Open Workflow
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

export function ChatSection({ initialChannels, currentChannelId }: { initialChannels: Channel[], currentChannelId?: string }) {
  const router = useRouter();
  const [newChannelName, setNewChannelName] = useState("");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [channels] = useState<Channel[]>(initialChannels);

  const handleCreateChannel = async (e: React.FormEvent) => {
    e.preventDefault();
    if (newChannelName.trim() === "") {
      toast.error("Please enter a channel name");
      return;
    }

    toast.promise(
      createChannel(newChannelName),
      {
        loading: 'Creating channel...',
        success: (result) => {
          if (result.success) {
            setNewChannelName("");
            setIsDialogOpen(false);
            if (result.channel?.id) {
              router.push(`/hr/workspace/chats/${result.channel.id}`);
            }
            return `Channel "${newChannelName}" created successfully!`;
          } else {
            throw new Error(result.error || "Failed to create channel");
          }
        },
        error: "Failed to create channel. Please try again."
      }
    );
  };

  return (
    <Card className="relative border-border/50">
      <div className="absolute top-0 right-0 w-full h-1 bg-gradient-to-l from-purple-500/20 via-primary/20 to-transparent" />
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-xl">Team Chat</CardTitle>
            <CardDescription>Active channels</CardDescription>
          </div>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm" variant="outline">
                <Plus className="w-4 h-4 mr-2" />
                Channel
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create a New Chat Channel</DialogTitle>
              </DialogHeader>
              <form onSubmit={handleCreateChannel} className="space-y-4">
                <Input
                  placeholder="Enter channel name"
                  value={newChannelName}
                  onChange={(e) => setNewChannelName(e.target.value)}
                />
                <Button type="submit" className="w-full">Create Channel</Button>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[400px] pr-4">
          <div className="space-y-2">
            {channels.length > 0 ? (
              channels.map((channel) => (
                <Button
                  key={channel.id}
                  variant="ghost"
                  className="w-full justify-start text-left group hover:bg-primary/5"
                  onClick={() => router.push(`/hr/workspace/chats/${channel.id}`)}
                >
                  <MessageSquare className="w-4 h-4 mr-2 text-primary" />
                  <span className="flex-1">{channel.name}</span>
                  {channel.isCreator && (
                    <Badge variant="outline" className="ml-2">Owner</Badge>
                  )}
                </Button>
              ))
            ) : (
              <p className="text-sm text-muted-foreground text-center py-4">
                No channels available. Create one to get started!
              </p>
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}

async function NotesSection() {
  const router = useRouter();
  const notesData = await getNotes();
  const treeData = await fetchNotesTreeData();

  const safeTreeData = treeData && 'data' in treeData && Array.isArray(treeData.data)
    ? treeData.data
    : [];

  const notes = notesData.success && notesData.notes ? notesData.notes.map(note => ({
    id: note.id,
    title: note.title,
    content: String(note.content),
    coverImage: note.coverImage,
    createdAt: note.createdAt,
    updatedAt: note.updatedAt,
    isFolder: note.isFolder,
    parentId: note.parentId
  })) : [];

  // Helper function to get folder path
  const getFolderPath = (noteId: string, allNotes: any[]): string => {
    const note = allNotes.find(n => n.id === noteId);
    if (!note || !note.parentId) return '';

    const parent = allNotes.find(n => n.id === note.parentId);
    if (!parent) return '';

    const parentPath = getFolderPath(parent.id, allNotes);
    return parentPath ? `${parentPath} / ${parent.title}` : parent.title;
  };

  // Helper function to get files inside a folder
  const getFilesInFolder = (folderId: string, allNotes: any[]): any[] => {
    return allNotes.filter(note => note.parentId === folderId && !note.isFolder);
  };

  // Helper function to get first file in folder for navigation
  const getFirstFileInFolder = (folderId: string, allNotes: any[]): any | null => {
    const filesInFolder = getFilesInFolder(folderId, allNotes);
    return filesInFolder.length > 0 ? filesInFolder[0] : null;
  };

  // Separate folders and files
  const folders = notes.filter(note => note.isFolder && !note.parentId); // Root folders only
  const rootFiles = notes.filter(note => !note.isFolder && !note.parentId); // Root files only

  const [searchNotes, setSearchNotes] = useState("");

  const filteredNotes = safeTreeData.filter((note: TreeNode) =>
    note.type === 'file' &&
    note.name.toLowerCase().includes(searchNotes.toLowerCase())
  );
  
  return (
    <Card className="relative border-border/50">
      <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-emerald-500/20 via-primary/20 to-transparent" />
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-xl">Notes Evolution</CardTitle>
            <CardDescription>Track your notes progress</CardDescription>
          </div>
          <div className="flex items-center gap-4">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search notes..."
                value={searchNotes}
                onChange={(e) => setSearchNotes(e.target.value)}
                className="pl-8 w-64"
              />
            </div>
            <Button asChild>
              <Link href="/hr/workspace/note">
                <Plus className="w-4 h-4 mr-2" />
                New Note
              </Link>
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Folders Section */}
          {folders.length > 0 && (
            <div>
              <h3 className="text-sm font-semibold text-muted-foreground mb-3 uppercase tracking-wide flex items-center">
                <Folder className="w-4 h-4 mr-2" />
                Folders ({folders.length})
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {folders.map((folder) => {
                  const filesInFolder = getFilesInFolder(folder.id, notes);
                  const firstFile = getFirstFileInFolder(folder.id, notes);

                  return (
                    <motion.div
                      key={folder.id}
                      initial={{ opacity: 0, scale: 0.95 }}
                      animate={{ opacity: 1, scale: 1 }}
                      whileHover={{ scale: 1.02 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Card
                        className="cursor-pointer hover:shadow-lg transition-all group border-border/50 bg-gradient-to-br from-blue-50/50 to-indigo-50/50 dark:from-blue-950/20 dark:to-indigo-950/20"
                        onClick={() => router.push(firstFile ? `/hr/workspace/note/${firstFile.id}` : `/hr/workspace/note`)}
                      >
                        <CardHeader className="pb-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                              <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900/30">
                                <Folder className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                              </div>
                              <div>
                                <CardTitle className="text-base line-clamp-1 group-hover:text-blue-600 dark:group-hover:text-blue-400">
                                  {folder.title}
                                </CardTitle>
                                <p className="text-xs text-muted-foreground mt-1">
                                  {filesInFolder.length} file{filesInFolder.length !== 1 ? 's' : ''}
                                </p>
                              </div>
                            </div>
                          </div>
                        </CardHeader>
                        <CardFooter className="pt-0 text-xs text-muted-foreground">
                          <div className="flex items-center justify-between w-full">
                            <div className="flex items-center">
                              <Clock className="w-3 h-3 mr-1" />
                              {new Date(folder.createdAt).toLocaleDateString()}
                            </div>
                            <div className="flex items-center">
                              <User2 className="w-3 h-3 mr-1" />
                              You
                            </div>
                          </div>
                        </CardFooter>
                      </Card>
                    </motion.div>
                  );
                })}
              </div>
            </div>
          )}

          {/* Files Section */}
          <div>
            <h3 className="text-sm font-semibold text-muted-foreground mb-3 uppercase tracking-wide flex items-center">
              <FileText className="w-4 h-4 mr-2" />
              Recent Files ({Math.min(filteredNotes.length, 6)})
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredNotes.map((note: TreeNode) => (
                <motion.div
                  key={note.id}
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  whileHover={{ scale: 1.02 }}
                  transition={{ duration: 0.2 }}
                >
                  <Card
                    className="cursor-pointer hover:shadow-lg transition-all group border-border/50"
                    onClick={() => router.push(`/hr/workspace/note/${note.id}`)}
                  >
                    <CardHeader className="pb-2">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-base line-clamp-1">{note.name}</CardTitle>
                      </div>
                      {/* Show folder path if note is in a folder */}
                      {(() => {
                        const folderPath = getFolderPath(note.id, notes);
                        return folderPath ? (
                          <div className="text-xs text-muted-foreground mt-1 flex items-center">
                            <span className="opacity-60">📁 {folderPath}</span>
                          </div>
                        ) : null;
                      })()}
                    </CardHeader>
                    <CardFooter className="pt-2 text-xs text-muted-foreground">
                      <div className="flex items-center justify-between w-full">
                        <div className="flex items-center">
                          <Clock className="w-3 h-3 mr-1" />
                          {new Date(note.createdAt || '').toLocaleDateString()}
                        </div>
                        <div className="flex items-center">
                          <User2 className="w-3 h-3 mr-1" />
                          You
                        </div>
                      </div>
                    </CardFooter>
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3 mt-6">
            {notes.map((note) => (
              <Card
                key={note.id}
                className="hover:shadow-lg transition-shadow overflow-hidden cursor-pointer group"
                onClick={() => router.push(`/hr/workspace/note/${note.id}`)}
              >
                <div className="relative w-full h-24">
                  {note.coverImage ? (
                    <Image
                      src={note.coverImage}
                      alt={note.title}
                      fill
                      className="object-cover"
                      sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 16vw"
                    />
                  ) : (
                    <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                      <FileText className="w-6 h-6 text-gray-400" />
                    </div>
                  )}
                </div>
                <div className="p-2">
                  <h3 className="text-sm font-medium line-clamp-1 group-hover:text-blue-600">
                    {note.title}
                  </h3>
                  {/* Show folder path for small cards too */}
                  {(() => {
                    const folderPath = getFolderPath(note.id, notes);
                    return folderPath ? (
                      <p className="text-xs text-gray-400 mt-1 truncate">
                        📁 {folderPath}
                      </p>
                    ) : null;
                  })()}
                  <p className="text-xs text-gray-500 mt-1">
                    {format(new Date(note.createdAt), 'MMM dd, yyyy')}
                  </p>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default function WorkspaceContent() {
  return null;
} 