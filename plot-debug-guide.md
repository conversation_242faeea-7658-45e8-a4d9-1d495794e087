# Plot Debugging Guide

## Issue: "⚠️ Plot 1: No image data generated"

This error occurs when matplotlib plots are not being captured properly. Here are the fixes I've implemented and troubleshooting steps:

## ✅ **Backend Fixes Applied**

1. **Fixed double plot closing**: Removed duplicate `plt.close('all')` calls
2. **Added debugging**: Enhanced logging to show plot capture process
3. **Improved error handling**: Better detection of empty plot data
4. **Added alternative methods**: Multiple ways to capture plots

## 🔧 **Troubleshooting Steps**

### 1. **Basic Plot Test**
```python
import matplotlib.pyplot as plt
import numpy as np

# Simple test
x = [1, 2, 3, 4, 5]
y = [2, 4, 6, 8, 10]

plt.figure(figsize=(8, 6))
plt.plot(x, y, 'b-o')
plt.title('Test Plot')
plt.xlabel('X values')
plt.ylabel('Y values')
plt.grid(True)
plt.show()  # This should capture the plot
```

### 2. **Check Debug Output**
The backend now logs detailed information. Look for these messages in the console:
- "plt.show() called - attempting to capture plot..."
- "Found X matplotlib figures"
- "Successfully captured plot and added to plots array"

### 3. **Manual Plot Capture**
If `plt.show()` doesn't work, try these alternatives:

```python
import matplotlib.pyplot as plt
import numpy as np

# Create your plot
plt.figure()
plt.plot([1, 2, 3], [1, 4, 2])
plt.title('Manual Capture Test')

# Method 1: Manual capture
plot_data = get_plot()  # This manually captures the current plot
print(f"Captured plot: {len(plot_data) if plot_data else 0} characters")

# Method 2: Check figures
check_figures()  # Shows active matplotlib figures

# Method 3: Save to plots array
save_plot()  # Manually adds current plot to plots array
```

### 4. **Alternative Plotting Function**
Use the new `plot_and_show()` function:

```python
import numpy as np

x = np.linspace(0, 10, 100)
y = np.sin(x)

# This automatically captures the plot
plot_and_show(x, y, title="Sine Wave")
```

### 5. **Debug Information**
Check what's happening:

```python
# Before plotting
check_figures()  # Should show: "Active figures: []"

# Create a plot
plt.figure()
plt.plot([1, 2, 3], [1, 4, 2])

# Check again
check_figures()  # Should show: "Active figures: [1]"

# Show the plot
plt.show()

# Check captured plots
list_plots()  # Shows how many plots were captured
```

## 🐛 **Common Issues & Solutions**

### Issue 1: No figures detected
**Problem**: "No matplotlib figures found when plt.show() was called"
**Solution**: Make sure you create a figure before calling show:
```python
plt.figure()  # Create a figure first
plt.plot(x, y)
plt.show()
```

### Issue 2: Empty plot data
**Problem**: "Failed to capture plot data or data too small"
**Solution**: Ensure your plot has content:
```python
plt.figure(figsize=(8, 6))  # Specify size
plt.plot([1, 2, 3], [1, 4, 2])  # Add actual data
plt.title('My Plot')  # Add title
plt.show()
```

### Issue 3: Plot closes too early
**Problem**: Plot disappears before capture
**Solution**: The backend now handles this automatically, but you can also use:
```python
plt.figure()
plt.plot(x, y)
# Don't call plt.close() manually
plt.show()  # This handles closing automatically
```

## 📝 **Working Examples**

### Example 1: Simple Line Plot
```python
import matplotlib.pyplot as plt
import numpy as np

x = np.linspace(0, 2*np.pi, 100)
y = np.sin(x)

plt.figure(figsize=(10, 6))
plt.plot(x, y, 'b-', linewidth=2, label='sin(x)')
plt.title('Sine Wave')
plt.xlabel('x')
plt.ylabel('sin(x)')
plt.legend()
plt.grid(True, alpha=0.3)
plt.show()
```

### Example 2: Multiple Plots
```python
import matplotlib.pyplot as plt
import numpy as np

# Plot 1
plt.figure()
plt.plot([1, 2, 3, 4], [1, 4, 2, 3], 'ro-')
plt.title('Plot 1')
plt.show()

# Plot 2
plt.figure()
plt.bar(['A', 'B', 'C', 'D'], [3, 7, 2, 5])
plt.title('Plot 2')
plt.show()
```

### Example 3: Subplots
```python
import matplotlib.pyplot as plt
import numpy as np

fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))

x = np.linspace(0, 10, 100)

ax1.plot(x, np.sin(x))
ax1.set_title('Sine')

ax2.plot(x, np.cos(x))
ax2.set_title('Cosine')

plt.tight_layout()
plt.show()
```

## 🔍 **If Still Not Working**

1. **Check the browser console** for JavaScript errors
2. **Look at the backend logs** for Python errors
3. **Try the manual capture methods** above
4. **Test with the simplest possible plot** first
5. **Make sure matplotlib is properly installed**: `pip install matplotlib`

The enhanced debugging should now show you exactly what's happening during plot capture!
