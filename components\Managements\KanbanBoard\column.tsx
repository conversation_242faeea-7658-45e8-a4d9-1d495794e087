"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { TaskCard } from "./TaskCard";
import { useDroppable } from "@dnd-kit/core";
import { SortableContext, verticalListSortingStrategy } from "@dnd-kit/sortable";
import { Column as ColumnType, Task } from "./types";
import { Plus, Trash } from "lucide-react";
import { useState } from "react";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Priority } from "./types";
import { format } from "date-fns";

interface ColumnProps {
    column: ColumnType;
    tasks: Task[];
    onAddTask: (columnId: string, newTask: Task) => void;
    onEditTask: (taskId: string, updates: Partial<Task>) => void;
    onDeleteTask: (taskId: string) => void;
    onDeleteColumn?: (columnId: string) => void;
}

export function Column({ column, tasks, onAddTask, onEditTask, onDeleteTask, onDeleteColumn }: ColumnProps) {
    const { setNodeRef, isOver } = useDroppable({ 
        id: column.id,
        data: { 
            type: 'column', 
            columnId: column.id,
            accepts: ['task']
        }
    });
    
    const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
    
    const handleCreateTask = (formData: FormData) => {
        const title = formData.get('title') as string;
        const description = formData.get('description') as string;
        const priority = formData.get('priority') as Priority;
        const type = formData.get('type') as string;
        const sprint = formData.get('sprint') as string;
        const assigneeId = formData.get('assignee') as string;
        const dueDateStr = formData.get('dueDate') as string;
        
        // Find the assignee from the users array
        // @ts-ignore
        const assignee = users.find(user => user.id === assigneeId) || users[0];
        
        onAddTask(column.id, {
            id: `task-${Date.now()}`,
            title,
            description,
            priority,
            assignee,
            dueDate: dueDateStr ? new Date(dueDateStr) : new Date(),
            commentsCount: 0,
            createdAt: new Date(),
            // Add the new properties
            type,
            sprint
        } as Task);
        
        setIsCreateDialogOpen(false);
    };

    return (
        <Card 
            ref={setNodeRef} 
            className={`h-full flex flex-col ${isOver ? 'column-drag-over pulse-animation' : ''}`}
            data-column-id={column.id}
        >
            <CardHeader className="pb-2 px-3">
                <div className="flex justify-between items-center">
                    <h2 className="text-lg font-semibold">{column.title}</h2>
                    <div className="flex items-center gap-2">
                        <Badge variant="secondary">{tasks.length}</Badge>
                        {onDeleteColumn && column.id !== "todo" && column.id !== "inprogress" && column.id !== "done" && (
                            <Button
                                variant="ghost"
                                size="sm"
                                className="h-7 w-7 p-0 text-destructive"
                                onClick={() => {
                                    if (confirm(`Delete column "${column.title}"?`)) {
                                        onDeleteColumn(column.id);
                                    }
                                }}
                            >
                                <Trash className="h-3.5 w-3.5" />
                            </Button>
                        )}
                    </div>
                </div>
            </CardHeader>
            <CardContent 
                className={`flex-grow overflow-y-auto max-h-[calc(100vh-220px)] min-h-[300px] column-content ${
                    isOver ? 'bg-accent/10' : ''
                }`} 
                data-column-id={column.id}
            >
                <SortableContext 
                    items={tasks.map(task => task.id)} 
                    strategy={verticalListSortingStrategy}
                >
                    {tasks.map((task) => (
                        <TaskCard
                            key={task.id}
                            task={task}
                            columnId={column.id}
                            onEdit={onEditTask}
                            onDelete={onDeleteTask}
                        />
                    ))}
                    {tasks.length === 0 && (
                        <div className="flex items-center justify-center h-32 border-2 border-dashed rounded-md text-muted-foreground text-sm my-6">
                            <div className="text-center">
                                <p>Drag tasks here</p>
                                <p className="text-xs mt-1">or click below to add a new task</p>
                            </div>
                        </div>
                    )}
                </SortableContext>
            </CardContent>
            <div className="p-2 mt-2">
                <Button 
                    variant="outline" 
                    className="w-full flex items-center justify-center bg-background/80 hover:bg-background"
                    onClick={() => setIsCreateDialogOpen(true)}
                >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Task
                </Button>
            </div>
            
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Add Task to {column.title}</DialogTitle>
                    </DialogHeader>
                    <form action={(formData) => handleCreateTask(formData)}>
                        <div className="grid gap-4 py-4">
                            <div className="grid gap-2">
                                <Label htmlFor="title">Title</Label>
                                <Input id="title" name="title" required />
                            </div>
                            <div className="grid gap-2">
                                <Label htmlFor="description">Description</Label>
                                <Textarea id="description" name="description" required />
                            </div>
                            <div className="grid grid-cols-2 gap-4">
                                <div className="grid gap-2">
                                    <Label htmlFor="priority">Priority</Label>
                                    <Select name="priority" defaultValue="medium">
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select priority" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="low">Low</SelectItem>
                                            <SelectItem value="medium">Medium</SelectItem>
                                            <SelectItem value="high">High</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                                
                                <div className="grid gap-2">
                                    <Label htmlFor="type">Type</Label>
                                    <Select name="type" defaultValue="task">
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select type" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="task">Task</SelectItem>
                                            <SelectItem value="feature">Feature</SelectItem>
                                            <SelectItem value="bug">Bug</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                            </div>
                            <div className="grid grid-cols-2 gap-4">
                                <div className="grid gap-2">
                                    <Label htmlFor="sprint">Sprint</Label>
                                    <Select name="sprint" defaultValue="none">
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select sprint" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="none">None</SelectItem>
                                            <SelectItem value="current">Current Sprint</SelectItem>
                                            <SelectItem value="backlog">Backlog</SelectItem>
                                            <SelectItem value="next">Next Sprint</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                                
                                <div className="grid gap-2">
                                    <Label htmlFor="assignee">Assignee</Label>
                                    <Select name="assignee" defaultValue="user1">
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select assignee" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="user1">Alice</SelectItem>
                                            <SelectItem value="user2">Bob</SelectItem>
                                            <SelectItem value="user3">Charlie</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                            </div>
                            <div className="grid gap-2">
                                <Label htmlFor="dueDate">Due Date</Label>
                                <Input id="dueDate" name="dueDate" type="date" required />
                            </div>
                        </div>
                        <DialogFooter>
                            <Button type="submit">Create Task</Button>
                        </DialogFooter>
                    </form>
                </DialogContent>
            </Dialog>
        </Card>
    );
}