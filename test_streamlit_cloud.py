#!/usr/bin/env python3
"""
Test script to verify cloud-compatible Streamlit functionality
"""

import os
import sys
import json
import tempfile
import base64
from pathlib import Path

# Add the backend directory to the path
sys.path.append(str(Path(__file__).parent / 'backend'))

try:
    from main import create_streamlit_app, is_cloud_environment
    print("✅ Successfully imported backend functions")
except ImportError as e:
    print(f"❌ Failed to import backend functions: {e}")
    sys.exit(1)

def test_cloud_environment_detection():
    """Test cloud environment detection"""
    print("\n🔍 Testing cloud environment detection...")
    
    # Test local environment (should be False)
    is_cloud = is_cloud_environment()
    print(f"Current environment detected as cloud: {is_cloud}")
    
    # Test with mock cloud environment variables
    os.environ['RENDER'] = 'true'
    is_cloud_mock = is_cloud_environment()
    print(f"Mock cloud environment detected: {is_cloud_mock}")
    
    # Clean up
    if 'RENDER' in os.environ:
        del os.environ['RENDER']
    
    return True

def test_streamlit_app_creation():
    """Test Streamlit app creation in both local and cloud modes"""
    print("\n🎈 Testing Streamlit app creation...")
    
    # Sample Streamlit code
    sample_code = """
import streamlit as st
import pandas as pd
import numpy as np

st.title("Test Streamlit App")
st.write("This is a test Streamlit application!")

# Create some sample data
data = pd.DataFrame({
    'x': np.random.randn(100),
    'y': np.random.randn(100)
})

st.line_chart(data)
st.write("Chart created successfully!")
"""
    
    app_id = "test_app_001"
    
    # Test local mode (if not in cloud)
    print("Testing local mode...")
    try:
        result = create_streamlit_app(sample_code, app_id)
        print(f"Local mode result: {json.dumps(result, indent=2)}")
        
        if result.get('type') == 'streamlit_app':
            print("✅ Local Streamlit app created successfully")
        elif result.get('type') == 'error':
            print(f"⚠️ Local mode error (expected in cloud): {result.get('message')}")
        else:
            print(f"❓ Unexpected result type: {result.get('type')}")
            
    except Exception as e:
        print(f"❌ Error in local mode: {e}")
    
    # Test cloud mode by setting environment variable
    print("\nTesting cloud mode...")
    os.environ['RENDER'] = 'true'
    
    try:
        result = create_streamlit_app(sample_code, app_id + "_cloud")
        print(f"Cloud mode result type: {result.get('type')}")
        print(f"Cloud mode status: {result.get('status')}")
        print(f"Cloud mode title: {result.get('title')}")
        
        if result.get('type') == 'streamlit_app':
            print("✅ Cloud Streamlit app created successfully")
            
            # Check if embed_url is HTML-based
            embed_url = result.get('embed_url', '')
            if embed_url.startswith('data:text/html;base64,'):
                print("✅ HTML-based embed URL created for cloud compatibility")
                
                # Decode and check HTML content
                try:
                    html_content = base64.b64decode(embed_url.split(',')[1]).decode('utf-8')
                    if 'Streamlit App' in html_content and sample_code[:50] in html_content:
                        print("✅ HTML content contains expected Streamlit code")
                    else:
                        print("⚠️ HTML content may not contain expected code")
                except Exception as e:
                    print(f"⚠️ Could not decode HTML content: {e}")
            else:
                print(f"❓ Unexpected embed URL format: {embed_url[:100]}...")
                
        else:
            print(f"❌ Unexpected cloud result: {result}")
            
    except Exception as e:
        print(f"❌ Error in cloud mode: {e}")
    finally:
        # Clean up environment variable
        if 'RENDER' in os.environ:
            del os.environ['RENDER']
    
    return True

def test_html_generation():
    """Test HTML generation for cloud-compatible Streamlit apps"""
    print("\n🌐 Testing HTML generation...")
    
    sample_code = """
import streamlit as st
st.title("Hello Cloud!")
st.write("This app runs in the cloud!")
"""
    
    # Force cloud mode
    os.environ['RENDER'] = 'true'
    
    try:
        result = create_streamlit_app(sample_code, "html_test")
        
        if result.get('type') == 'streamlit_app':
            embed_url = result.get('embed_url', '')
            
            if embed_url.startswith('data:text/html;base64,'):
                # Decode HTML
                html_content = base64.b64decode(embed_url.split(',')[1]).decode('utf-8')
                
                # Check for expected HTML elements
                expected_elements = [
                    '<!DOCTYPE html>',
                    'Streamlit App',
                    'copyCode()',
                    'openDemo()',
                    'streamlit.io',
                    sample_code
                ]
                
                all_found = True
                for element in expected_elements:
                    if element in html_content:
                        print(f"✅ Found expected element: {element[:30]}...")
                    else:
                        print(f"❌ Missing expected element: {element}")
                        all_found = False
                
                if all_found:
                    print("✅ All expected HTML elements found")
                else:
                    print("⚠️ Some HTML elements missing")
                    
                # Save HTML to file for manual inspection
                with open('test_streamlit_output.html', 'w', encoding='utf-8') as f:
                    f.write(html_content)
                print("📄 HTML content saved to test_streamlit_output.html")
                
            else:
                print(f"❌ Expected HTML data URL, got: {embed_url[:100]}...")
        else:
            print(f"❌ Expected streamlit_app, got: {result.get('type')}")
            
    except Exception as e:
        print(f"❌ Error testing HTML generation: {e}")
    finally:
        if 'RENDER' in os.environ:
            del os.environ['RENDER']
    
    return True

def main():
    """Run all tests"""
    print("🧪 Starting Streamlit Cloud Compatibility Tests")
    print("=" * 50)
    
    tests = [
        test_cloud_environment_detection,
        test_streamlit_app_creation,
        test_html_generation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
                print(f"✅ {test.__name__} passed")
            else:
                print(f"❌ {test.__name__} failed")
        except Exception as e:
            print(f"❌ {test.__name__} failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"🏁 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Cloud-compatible Streamlit is working correctly.")
        return 0
    else:
        print("⚠️ Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())