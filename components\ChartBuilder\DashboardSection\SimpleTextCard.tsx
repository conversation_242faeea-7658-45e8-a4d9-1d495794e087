'use client'

/**
 * SimpleTextCard Component
 *
 * A modern text editor card for the dashboard with BI-style design.
 * Supports rich text editing, formatting options, and seamless integration
 * with the dashboard grid layout system.
 *
 * Features:
 * - Double-click to edit text content
 * - Rich text formatting (bold, italic, underline)
 * - Text alignment options
 * - Hover-based remove button
 * - Modern BI dashboard styling
 * - Responsive design
 */

import { useState, useEffect, useRef } from 'react';
import { TextItem } from './types';
import { Trash2, Bold, Italic, Underline, AlignLeft, AlignCenter, AlignRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface SimpleTextCardProps {
  textItem: TextItem;
  isEditMode: boolean;
  onUpdateText: (textId: string, updates: Partial<TextItem>) => void;
  onRemoveText: (textId: string) => void;
}

export function SimpleTextCard({ textItem, isEditMode, onUpdateText, onRemoveText }: SimpleTextCardProps) {
  // Component state
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(textItem.content || '');
  const [isBold, setIsBold] = useState(textItem.isBold || false);
  const [isItalic, setIsItalic] = useState(textItem.isItalic || false);
  const [isUnderline, setIsUnderline] = useState(textItem.isUnderline || false);
  const [textAlign, setTextAlign] = useState<'left' | 'center' | 'right'>(
    (textItem.textAlign as 'left' | 'center' | 'right') || 'left'
  );

  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Initialize component state
  useEffect(() => {
    setEditValue(textItem.content || '');
    setIsBold(textItem.isBold || false);
    setIsItalic(textItem.isItalic || false);
    setIsUnderline(textItem.isUnderline || false);
    setTextAlign((textItem.textAlign as 'left' | 'center' | 'right') || 'left');

    // Auto-enter edit mode for new items
    if (textItem.isNew) {
      setTimeout(() => {
        setIsEditing(true);
        textareaRef.current?.focus();
      }, 100);
    }
  }, [textItem]);

  // Handle double click to edit
  const handleDoubleClick = () => {
    if (isEditMode && !isEditing) {
      setIsEditing(true);
      setTimeout(() => textareaRef.current?.focus(), 10);
    }
  };

  // Handle save
  const handleSave = () => {
    try {
      onUpdateText(textItem.id, {
        content: editValue,
        isBold,
        isItalic,
        isUnderline,
        textAlign,
        isNew: false
      });
      setIsEditing(false);
      // Only show toast for errors, not success
    } catch (error) {
      console.error('Error saving text:', error);
      toast.error('Failed to save text');
    }
  };

  // Handle cancel
  const handleCancel = () => {
    setEditValue(textItem.content || '');
    setIsEditing(false);
  };

  // Handle delete
  const handleDelete = () => {
    onRemoveText(textItem.id);
    // No toast for delete - keep UI clean
  };

  // Toggle formatting
  const toggleBold = () => setIsBold(!isBold);
  const toggleItalic = () => setIsItalic(!isItalic);
  const toggleUnderline = () => setIsUnderline(!isUnderline);

  // Handle alignment
  const handleAlignLeft = () => setTextAlign('left');
  const handleAlignCenter = () => setTextAlign('center');
  const handleAlignRight = () => setTextAlign('right');

  // Handle keyboard shortcuts
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (isEditing) {
      if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
        e.preventDefault();
        handleSave();
      } else if (e.key === 'Escape') {
        e.preventDefault();
        handleCancel();
      }
    }
  };

  // Determine if this is a title-style text
  const isTitleStyle = textItem.textStyle === 'title';

  return (
    <Card
      className={cn(
        "w-full h-full transition-all duration-200 dashboard-card-group",
        "bg-card text-card-foreground border border-border",
        "min-h-[120px]", // Ensure minimum height
        isEditMode ? "hover:border-primary" : "",
        isEditing ? "ring-2 ring-primary ring-offset-2" : "",
        isTitleStyle ? "title-text-card" : "content-text-card"
      )}
      onDoubleClick={handleDoubleClick}
      data-item-id={textItem.id}
      data-item-type="text"
      data-is-editing={isEditing ? 'true' : 'false'}
      data-edit-mode={isEditMode ? 'true' : 'false'}
      onKeyDown={handleKeyDown}
      style={{ minHeight: '120px' }} // Force minimum height
    >
      {/* Hover-based Remove Button */}
      {isEditMode && !isEditing && (
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="card-remove-button h-6 w-6 p-0 hover:bg-red-100 hover:text-red-600 transition-all non-draggable"
              title="Delete text card"
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Delete Text Card</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to delete this text card? This action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDelete}
                className="bg-red-600 hover:bg-red-700"
              >
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}

      {/* Content Area */}
      <div className={cn("h-full", isEditing ? "p-3" : "p-2")}>
        {isEditing ? (
          <div className="h-full flex flex-col gap-3">
            {/* Formatting Toolbar */}
            <div className="flex items-center gap-2 p-2 bg-muted/50 rounded-md border">
              {/* Text Formatting */}
              <div className="flex items-center gap-1">
                <Button
                  variant={isBold ? "default" : "ghost"}
                  size="sm"
                  onClick={toggleBold}
                  className="h-7 w-7 p-0"
                  title="Bold"
                >
                  <Bold className="h-3 w-3" />
                </Button>
                <Button
                  variant={isItalic ? "default" : "ghost"}
                  size="sm"
                  onClick={toggleItalic}
                  className="h-7 w-7 p-0"
                  title="Italic"
                >
                  <Italic className="h-3 w-3" />
                </Button>
                <Button
                  variant={isUnderline ? "default" : "ghost"}
                  size="sm"
                  onClick={toggleUnderline}
                  className="h-7 w-7 p-0"
                  title="Underline"
                >
                  <Underline className="h-3 w-3" />
                </Button>
              </div>

              {/* Divider */}
              <div className="w-px h-4 bg-border" />

              {/* Text Alignment */}
              <div className="flex items-center gap-1">
                <Button
                  variant={textAlign === 'left' ? "default" : "ghost"}
                  size="sm"
                  onClick={handleAlignLeft}
                  className="h-7 w-7 p-0"
                  title="Align Left"
                >
                  <AlignLeft className="h-3 w-3" />
                </Button>
                <Button
                  variant={textAlign === 'center' ? "default" : "ghost"}
                  size="sm"
                  onClick={handleAlignCenter}
                  className="h-7 w-7 p-0"
                  title="Align Center"
                >
                  <AlignCenter className="h-3 w-3" />
                </Button>
                <Button
                  variant={textAlign === 'right' ? "default" : "ghost"}
                  size="sm"
                  onClick={handleAlignRight}
                  className="h-7 w-7 p-0"
                  title="Align Right"
                >
                  <AlignRight className="h-3 w-3" />
                </Button>
              </div>

              {/* Spacer */}
              <div className="flex-1" />

              {/* Action Buttons */}
              <div className="flex items-center gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleCancel}
                  className="h-7 px-2 text-xs"
                >
                  Cancel
                </Button>
                <Button
                  variant="default"
                  size="sm"
                  onClick={handleSave}
                  className="h-7 px-2 text-xs"
                >
                  Save
                </Button>
              </div>
            </div>

            {/* Text Editor */}
            <Textarea
              ref={textareaRef}
              value={editValue}
              onChange={(e) => setEditValue(e.target.value)}
              placeholder="Enter your text here..."
              className={cn(
                "flex-1 resize-none border-0 focus-visible:ring-0 focus-visible:ring-offset-0 bg-transparent text-foreground placeholder:text-muted-foreground shadow-none",
                `text-${textAlign}`,
                isBold && "font-bold",
                isItalic && "italic",
                isUnderline && "underline"
              )}
              style={{
                minHeight: '100px',
                backgroundColor: 'transparent !important',
                boxShadow: 'none'
              }}
            />
          </div>
        ) : (
          /* Display Mode */
          <div
            className={cn(
              "h-full w-full whitespace-pre-wrap break-words text-foreground flex items-start",
              `text-${textAlign}`,
              isBold && "font-bold",
              isItalic && "italic",
              isUnderline && "underline",
              isTitleStyle ? "text-lg font-semibold text-primary" : "text-sm",
              !textItem.content && "text-muted-foreground"
            )}
          >
            <div className="w-full">
              {textItem.content || 'Double-click to edit text'}
            </div>
          </div>
        )}
      </div>
    </Card>
  );
}