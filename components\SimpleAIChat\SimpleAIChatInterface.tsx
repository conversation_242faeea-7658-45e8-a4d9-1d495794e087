"use client"

import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Trash2,
  Sparkles,
  Gem,
  Settings,
  ChevronDown,
  ChevronUp,
  Zap,
  Brain,
  BarChart3
} from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

// Import components
import { MessageBubble } from './MessageBubble';
import { ChatInput } from './ChatInput';
import { DatasetSidebar } from './DatasetSidebar';
import { TypingIndicator } from './ShimmerText';
import AiTextLoading from './AiTextLoading';

interface Message {
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  datasetsAnalyzed?: Array<{ name: string; totalRows: number }>;
  sources?: Array<{ dataset: string; reference: string; type: 'data' | 'calculation' | 'example' }>;
  images?: Array<{ name: string; url: string }>;
  model?: string;
  chart?: {
    type: 'bar' | 'line' | 'pie' | 'scatter';
    data: any;
    title?: string;
  };
  table?: {
    headers: string[];
    rows: any[][];
    title?: string;
  };
}

interface UploadedImage {
  name: string;
  url: string;
  file: File;
}

interface GeminiModel {
  id: string;
  name: string;
  description: string;
  supportsImages: boolean;
}

interface Dataset {
  id: string;
  name: string;
  description?: string;
  data: any[];
  headers: string[];
}

interface SimpleAIChatInterfaceProps {
  datasets: Dataset[];
  selectedDatasets: string[];
  onDatasetSelectionChange: (datasetIds: string[]) => void;
}

const GEMINI_MODELS: GeminiModel[] = [
  {
    id: 'gemini-1.5-pro',
    name: 'Gemini 1.5 Pro',
    description: 'Most capable model with excellent reasoning and image analysis',
    supportsImages: true
  },
  {
    id: 'gemini-1.5-flash',
    name: 'Gemini 1.5 Flash',
    description: 'Fast and efficient with good performance and image support',
    supportsImages: true
  },
  {
    id: 'gemini-1.0-pro',
    name: 'Gemini 1.0 Pro',
    description: 'Reliable and stable for general tasks',
    supportsImages: false
  }
];

const SimpleAIChatInterface: React.FC<SimpleAIChatInterfaceProps> = ({
  datasets,
  selectedDatasets,
  onDatasetSelectionChange
}) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedModel, setSelectedModel] = useState<string>('gemini-1.5-flash');
  const [uploadedImages, setUploadedImages] = useState<UploadedImage[]>([]);
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  }, [messages]);

  // Handle image upload
  const handleImageUpload = (files: File[]) => {
    const selectedModelData = GEMINI_MODELS.find(m => m.id === selectedModel);
    if (!selectedModelData?.supportsImages) {
      toast.error('Selected model does not support image analysis. Please choose Gemini 1.5 Pro or Flash.');
      return;
    }

    files.forEach(file => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const url = e.target?.result as string;
          setUploadedImages(prev => [...prev, { name: file.name, url, file }]);
          toast.success(`Image "${file.name}" uploaded successfully`);
        };
        reader.readAsDataURL(file);
      } else {
        toast.error('Please upload only image files');
      }
    });
  };

  // Remove uploaded image
  const removeImage = (index: number) => {
    setUploadedImages(prev => prev.filter((_, i) => i !== index));
  };

  const handleSendMessage = async () => {
    if ((!input.trim() && uploadedImages.length === 0) || isLoading) return;

    const userMessage: Message = {
      role: 'user',
      content: input.trim() || 'Analyze the uploaded image(s)',
      timestamp: new Date(),
      images: uploadedImages.map(img => ({ name: img.name, url: img.url })),
      model: selectedModel
    };

    setMessages(prev => [...prev, userMessage]);
    const currentInput = input.trim();
    const currentImages = [...uploadedImages];
    setInput('');
    setUploadedImages([]);
    setIsLoading(true);

    try {
      // Prepare images for API call
      const imageData = await Promise.all(
        currentImages.map(async (img) => {
          return {
            name: img.name,
            data: img.url.split(',')[1], // Remove data:image/...;base64, prefix
            mimeType: img.file.type
          };
        })
      );

      const response = await fetch('/api/simple-ai-chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: currentInput || 'Analyze the uploaded image(s)',
          selectedDatasets,
          conversationHistory: messages.slice(-6), // Last 6 messages for context
          model: selectedModel,
          images: imageData
        }),
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Failed to get AI response');
      }

      const assistantMessage: Message = {
        role: 'assistant',
        content: data.content,
        timestamp: new Date(),
        datasetsAnalyzed: data.datasetsAnalyzed,
        sources: data.sources || [],
        model: selectedModel,
        chart: data.chart,
        table: data.table
      };

      setMessages(prev => [...prev, assistantMessage]);

    } catch (error: any) {
      console.error('Error sending message:', error);
      toast.error(error.message || 'Failed to send message');
      
      const errorMessage: Message = {
        role: 'assistant',
        content: `Sorry, I encountered an error while analyzing your data: ${error.message}. ${error.message.includes('overloaded') ? 'The AI service is currently busy. Please try again in a moment.' : 'Please try again.'}`,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const clearChat = () => {
    setMessages([]);
    setUploadedImages([]);
    toast.success('Chat cleared');
  };

  const toggleDataset = (datasetId: string) => {
    const newSelection = selectedDatasets.includes(datasetId)
      ? selectedDatasets.filter(id => id !== datasetId)
      : [...selectedDatasets, datasetId];
    onDatasetSelectionChange(newSelection);
  };

  const selectedDatasetsInfo = datasets.filter(d => selectedDatasets.includes(d.id));

  return (
    <div className="flex h-screen bg-background">
      {/* Left Sidebar - Datasets */}
      <DatasetSidebar
        datasets={datasets}
        selectedDatasets={selectedDatasets}
        onDatasetToggle={toggleDataset}
      />

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col min-w-0 h-full">
        {/* Header */}
        <div className="p-4 border-b bg-card">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-3 bg-primary rounded-lg">
                <Sparkles className="h-6 w-6 text-primary-foreground" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-foreground">
                  AI Data Assistant
                </h1>
                <p className="text-sm text-muted-foreground">
                  Analyze your data with AI-powered insights
                </p>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsSettingsOpen(!isSettingsOpen)}
                className="flex items-center gap-2"
              >
                <Settings className="h-4 w-4" />
                Settings
                {isSettingsOpen ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
              </Button>
              {messages.length > 0 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearChat}
                  className="flex items-center gap-2 text-destructive hover:text-destructive"
                >
                  <Trash2 className="h-4 w-4" />
                  Clear
                </Button>
              )}
            </div>
          </div>

          {/* Settings Panel */}
          {isSettingsOpen && (
            <div className="mt-4 p-4 rounded-lg border bg-muted/50">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium mb-2 flex items-center gap-2">
                    <Gem className="h-4 w-4 text-primary" />
                    AI Model
                  </label>
                  <Select value={selectedModel} onValueChange={setSelectedModel}>
                    <SelectTrigger className="w-full">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {GEMINI_MODELS.map((model) => (
                        <SelectItem key={model.id} value={model.id}>
                          <div className="flex items-center gap-2">
                            <Gem className="h-4 w-4 text-primary" />
                            <div>
                              <div className="font-medium">{model.name}</div>
                              <div className="text-xs text-muted-foreground">{model.description}</div>
                            </div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 flex items-center gap-2">
                    <Zap className="h-4 w-4 text-primary" />
                    Quick Actions
                  </label>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setInput("Analyze the key trends in my data")}
                      className="flex-1"
                    >
                      <Brain className="h-4 w-4 mr-1" />
                      Analyze
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setInput("Create a summary report")}
                      className="flex-1"
                    >
                      <BarChart3 className="h-4 w-4 mr-1" />
                      Report
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Messages Area */}
        <div className="flex-1 flex flex-col min-h-0 bg-background">
          <ScrollArea className="flex-1 p-6" ref={scrollAreaRef}>
            <div className="max-w-4xl mx-auto space-y-6">
              {messages.length === 0 ? (
                <div className="text-center py-12">
                  <div className="w-20 h-20 mx-auto mb-6 rounded-2xl bg-primary flex items-center justify-center shadow-lg">
                    <Sparkles className="h-10 w-10 text-primary-foreground" />
                  </div>
                  <h3 className="text-2xl font-bold mb-3 text-foreground">
                    Ready to analyze your data
                  </h3>
                  <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                    Ask me anything! I can help with general questions, create charts, generate tables, or analyze your data if you select datasets.
                  </p>
                  <div className="flex flex-wrap gap-2 justify-center">
                    <Button
                      variant="outline"
                      onClick={() => setInput("Create a bar chart showing sales by month")}
                      className="text-sm"
                    >
                      <BarChart3 className="h-4 w-4 mr-2" />
                      Generate Chart
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => setInput("Create a table with sample data")}
                      className="text-sm"
                    >
                      <Brain className="h-4 w-4 mr-2" />
                      Create Table
                    </Button>
                    {selectedDatasets.length > 0 && (
                      <Button
                        variant="outline"
                        onClick={() => setInput("Analyze the key trends in my selected datasets")}
                        className="text-sm"
                      >
                        <Zap className="h-4 w-4 mr-2" />
                        Analyze Data
                      </Button>
                    )}
                  </div>
                </div>
              ) : (
                <>
                  {messages.map((message, index) => (
                    <MessageBubble
                      key={index}
                      message={message}
                      modelName={GEMINI_MODELS.find(m => m.id === selectedModel)?.name}
                    />
                  ))}

                  {isLoading && (
                    <div className="flex gap-3 justify-start">
                      <div className="flex-shrink-0 w-8 h-8 rounded-full bg-primary flex items-center justify-center shadow-lg">
                        <Sparkles className="h-4 w-4 text-primary-foreground" />
                      </div>
                      <div className="max-w-[85%] rounded-2xl p-3 bg-card border shadow-sm">
                        <AiTextLoading
                          texts={[
                            "Analyzing your request...",
                            "Processing data...",
                            "Generating response...",
                            "Creating visualization...",
                            "Almost ready..."
                          ]}
                          className="text-xs"
                          interval={1200}
                        />
                      </div>
                    </div>
                  )}
                </>
              )}
            </div>
          </ScrollArea>

          {/* Input Area */}
          <div className="p-6 border-t bg-card">
            <div className="max-w-4xl mx-auto">
              <ChatInput
                value={input}
                onChange={setInput}
                onSend={handleSendMessage}
                onImageUpload={handleImageUpload}
                uploadedImages={uploadedImages}
                onRemoveImage={removeImage}
                isLoading={isLoading}
                disabled={false}
                placeholder="Ask me anything, request charts, tables, or upload images for analysis..."
                supportsImages={GEMINI_MODELS.find(m => m.id === selectedModel)?.supportsImages}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimpleAIChatInterface;
