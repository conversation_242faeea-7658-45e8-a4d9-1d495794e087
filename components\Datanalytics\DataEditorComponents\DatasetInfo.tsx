'use client'

import React from 'react';
import { motion } from "framer-motion";
import { Database, Eye, GitBranch, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from '@/components/ui/badge';
import { cn } from "@/lib/utils";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { formatBytes, formatNumber, formatDate, calculateDataSize } from './utils';

interface DatasetInfoProps {
  savedDatasets: any[];
  allVersions: Record<string, any[]>;
  onDatasetSelect: (dataset: any) => void;
  onDeleteDataset: (datasetId: string) => void;
  onShowVersionHistory: (dataset: any) => void;
  loadVersionData: (datasetId: string, versionNumber: number) => void;
}

export const DatasetInfo: React.FC<DatasetInfoProps> = ({
  savedDatasets,
  allVersions,
  onDatasetSelect,
  onDeleteDataset,
  onShowVersionHistory,
  loadVersionData
}) => {
  if (!savedDatasets.length) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="w-full mt-2"
    >
      <table className="w-full text-sm">
        <thead>
          <tr className="bg-muted/30">
            <th className="font-semibold text-muted-foreground py-2 px-3 text-left">Dataset</th>
            <th className="font-semibold text-muted-foreground py-2 px-3 text-left">Info</th>
            <th className="font-semibold text-muted-foreground py-2 px-3 text-left">Version Timeline</th>
            <th className="font-semibold text-muted-foreground py-2 px-3 text-right">Actions</th>
          </tr>
        </thead>
        <tbody>
          {savedDatasets.map((dataset, index) => {
            const datasetVersions = allVersions[dataset.id] || [];
            return (
              <tr key={dataset.id} className="border-b hover:bg-muted/20 transition-colors">
                {/* Dataset Info */}
                <td className="py-2 px-3">
                  <div className="flex items-center gap-2">
                    <div className="h-7 w-7 rounded bg-gradient-to-br from-primary/10 to-primary/5 flex items-center justify-center">
                      <Database className="h-4 w-4 text-primary" />
                  </div>
                  <div className="min-w-0">
                      <p className="font-semibold truncate text-base leading-tight">{dataset.name}</p>
                      <p className="text-xs text-muted-foreground truncate mt-0.5">
                      {formatDate(dataset.createdAt)}
                    </p>
                  </div>
                </div>
                </td>
                {/* Stats */}
                <td className="py-2 px-3">
                  <div className="flex flex-col gap-0.5">
                  <span className="text-muted-foreground">
                      {formatNumber(dataset.data?.length ?? 0)} rows × {dataset.headers?.length ?? 0} cols
                  </span>
                  <span className="text-muted-foreground">
                      {formatBytes(calculateDataSize(dataset.data ?? []))}
                  </span>
                </div>
                </td>
                {/* Version Timeline */}
                <td className="py-2 px-3">
                  <div className="flex items-center gap-1">
                    {datasetVersions.length > 0 ? (
                      datasetVersions.map((version) => (
                          <motion.button
                          key={version.id}
                            onClick={() => loadVersionData(dataset.id, version.versionNumber)}
                          whileHover={{ scale: 1.1, y: -1 }}
                            className={cn(
                            "relative h-5 w-5 rounded-full flex items-center justify-center text-xs font-semibold border border-primary/20",
                              version.changes.length > 0
                                ? "bg-primary text-primary-foreground"
                                : "bg-muted text-muted-foreground"
                            )}
                          >
                            {version.versionNumber}
                            {version.changes.length > 0 && (
                            <span className="absolute -top-0.5 -right-0.5 h-1.5 w-1.5 rounded-full bg-green-500 border border-background" />
                            )}
                          </motion.button>
                      ))
                    ) : (
                      <span className="text-xs text-muted-foreground italic px-1">No version history</span>
                    )}
                  </div>
                </td>
                {/* Actions */}
                <td className="py-2 px-3 text-right">
                  <div className="flex items-center gap-1 justify-end">
                  <Button
                    variant="ghost"
                      size="icon"
                      className="h-7 w-7"
                    onClick={() => onDatasetSelect(dataset)}
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                      size="icon"
                      className="h-7 w-7"
                    onClick={() => {
                      onDatasetSelect(dataset);
                      onShowVersionHistory(dataset);
                    }}
                  >
                    <GitBranch className="h-4 w-4" />
                  </Button>
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button
                        variant="ghost"
                          size="icon"
                          className="h-7 w-7 text-destructive hover:text-destructive"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Delete Dataset</AlertDialogTitle>
                        <AlertDialogDescription>
                          Are you sure you want to delete this dataset? This action cannot be undone.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={() => onDeleteDataset(dataset.id)}
                          className="bg-destructive hover:bg-destructive/90"
                        >
                          Delete
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
                </td>
              </tr>
            );
          })}
        </tbody>
      </table>
    </motion.div>
  );
};

