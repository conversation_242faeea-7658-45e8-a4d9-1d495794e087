These are new fields need to be added in the action.ts and in the invoice page
date 2024-10-17

estLocataire      Boolean? // Indicates if the employee is renting
possedeAppartement Boolean? // Indicates if the employee owns an apartment
utiliseTransport  Boolean? // Indicates if the employee uses transport
typeTransport     String?  // Type of transport used (e.g., "PUBLIC", "PRIVATE", "COMPANY")
distanceDomicileTravail Float? // Distance between home and work in kilometers
numeroCIMR        String? // Caisse Interprofessionnelle Marocaine de Retraite number
groupeSanguin     String? // Blood type
situationFamiliale String? // Detailed family situation
zoneResidence     String? // Residential area or neighborhood
modePaiement      String? // Payment method (e.g., "BANK_TRANSFER", "CHEQUE")
echelonSalaire    String? // Salary grade or level
formationsContinues String[] // Ongoing or completed professional training

date 2024-10-21
- add the cover image to the editor
- add the title to the editor
- add the upload image button to the editor
- add the RIB number the invoice page [In progress] 

this the format should ai generate to visualize data: 
{
  "title": "Sample Data",
  "description": "Sample description",
  "data": [
    { "name": "Category A", "value": 30, "growth": 5 },
    { "name": "Category B", "value": 45, "growth": 8 },
    { "name": "Category C", "value": 60, "growth": 12 }
  ],
  "columns": [
    { "key": "name", "label": "Category" },
    { "key": "value", "label": "Value" },
    { "key": "growth", "label": "Growth %" }
  ],
  "insight": "Growing trend observed across categories",
  "footer": "Data from Q1 2024"
}

- Remoove chartconfig only keep chartvisulizer component [QueryResult] {DONE}
- implment a json output of chart config selection (x and y, title , color, chart type and all of them), and remove duplicate
also make the the config above inside a dropdown to hide it, so it will show only chart
[ChartVisualizer]

- Update hr/layout by adding screen animation when sidebar exapnd
- update app-sidebar by adding mainsidebar functionality
- update workspace navbar, and management minisidebar + testing UX 


github pass
<EMAIL>
BusinessBlock5@



<EMAIL>
Holylee01@

please is there a better way to do rag system for large dataset, i want u to really analyze the Chat tab, and remove csv analyzer, just only focus on chat and improve the deep search so even if user select multiple datasets and with pdf too and they are tooo large  the rag system can really search and give the right answer so please i want this to be the best rag system ever, please make sure user can select multiple datasets and also make sure the UI and UX of rag is the best and clean UI


what is the sum of rubrique 300 + 350 of matricule 00015 in Jan-25

when i attempt to drag a file i get : Drop on a folder to move this note please analyze well the files and fix this issue 




The problem is that when concatenating datasets, the data from different datasets appears in separate rows instead of being combined into the same rows. You want the selected columns from both datasets to appear in the same rows, not as separate rows at the end.

This is a fundamental issue with how the concat operation works. Currently it's doing a vertical concatenation (stacking rows), but you want a horizontal concatenation (merging columns).

Let me fix the concat executor to properly merge the datasets horizontally: