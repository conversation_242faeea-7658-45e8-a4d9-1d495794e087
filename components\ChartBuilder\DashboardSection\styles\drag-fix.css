/**
 * Drag Fix CSS
 * 
 * This file provides critical fixes for react-grid-layout's drag and drop
 * functionality, ensuring items follow the cursor exactly without jumping.
 * Also enables auto-scrolling when dragging items to the edge of the viewport.
 */

.react-grid-layout {
  position: relative;
  overflow: visible !important; /* Ensure overflow is visible to allow for auto-scrolling */
}

.react-grid-item {
  /* Use absolute positioning */
  position: absolute;
  
  /* Disable all transitions */
  transition: none !important;
  
  /* Force GPU acceleration but avoid transforms */
  will-change: left, top;
  
  /* Ensure transparent background so overlapping works properly */
  background: transparent;
}

.react-grid-item.react-draggable-dragging {
  /* Higher z-index than other items */
  z-index: 9999 !important;
  
  /* Force cursor */
  cursor: grabbing !important;
  
  /* Ensure no transitions or transforms */
  transition: none !important;
  transform: none !important;
  
  /* Make item slightly visible through it */
  opacity: 0.9;
  
  /* Add a subtle shadow for visual feedback */
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Fix drag handle to ensure it captures mouse events properly */
.draggable-handle {
  cursor: grab !important;
  touch-action: none !important;
  -webkit-user-select: none !important;
  user-select: none !important;
}

/* Improve placeholder visibility and animation */
.react-grid-placeholder {
  transition: all 150ms ease !important;
  z-index: 1 !important;
  opacity: 0.4 !important;
  border-radius: 8px !important;
  background-color: rgba(var(--primary-rgb), 0.2) !important;
  border: 2px dashed rgba(var(--primary-rgb), 0.4) !important;
  animation: pulse 1.5s infinite ease-in-out !important;
}

@keyframes pulse {
  0% { opacity: 0.3; }
  50% { opacity: 0.5; }
  100% { opacity: 0.3; }
}

/* Enhanced placeholder when actively dragging */
.active-placeholder {
  opacity: 0.6 !important;
  background-color: rgba(var(--primary-rgb), 0.3) !important;
  border: 2px solid rgba(var(--primary-rgb), 0.6) !important;
  box-shadow: 0 0 10px rgba(var(--primary-rgb), 0.2) !important;
  animation: pulse 0.8s infinite ease-in-out !important;
}

/* Prevent text selection during drag operations */
.react-grid-layout * {
  -webkit-user-select: none !important;
  user-select: none !important;
}

/* Enable auto-scrolling during drag operations */
.dashboard-wrapper {
  overflow: auto !important;
  scroll-behavior: smooth !important;
  height: auto !important;
  min-height: 100vh !important;
}

/* When dragging, ensure the dashboard container allows for scrolling */
.dashboard-container {
  overflow: visible !important;
  min-height: 100% !important;
}

/* When an item is being dragged, enable auto-scrolling */
body.dragging-dashboard-item {
  overflow: auto !important;
  height: auto !important;
}

/* Add smooth scrolling for better UX during drag operations */
.dragging-active .dashboard-container {
  scroll-behavior: auto !important;
}

/* Enhanced precision for drag operations */
.react-grid-layout {
  /* Ensure pixel-perfect positioning */
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Improve drag precision by reducing snap threshold */
.react-grid-item.react-draggable-dragging {
  /* Ensure exact positioning during drag */
  pointer-events: none;

  /* Reduce any potential lag */
  transform: translate3d(0, 0, 0) !important;

  /* Ensure smooth movement */
  transition: none !important;
  animation: none !important;
}

/* Fine-tune grid snapping for better precision */
.react-grid-layout .react-grid-item {
  /* Ensure items snap to exact grid positions */
  box-sizing: border-box;

  /* Prevent sub-pixel rendering issues */
  transform: translate3d(0, 0, 0);
}

/* Improve placeholder positioning accuracy */
.react-grid-placeholder {
  /* Ensure placeholder follows exact grid positioning */
  box-sizing: border-box !important;
  transform: translate3d(0, 0, 0) !important;
}

/* Visual feedback for items that will be repositioned */
.react-grid-item.will-move {
  transition: all 0.2s ease !important;
  opacity: 0.7 !important;
  border: 1px dashed rgba(var(--primary-rgb), 0.5) !important;
  background-color: rgba(var(--primary-rgb), 0.05) !important;
}

/* Enhanced visual feedback for smart layout */
.react-grid-layout.smart-layout-preview .react-grid-item {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Databricks/Superset style layout behavior */
.react-grid-layout {
  /* Ensure smooth transitions for layout changes */
  transition: height 0.2s ease !important;
}

.react-grid-item {
  /* Smooth positioning transitions when not dragging */
  transition: transform 0.2s ease, opacity 0.2s ease !important;
}

.react-grid-item.react-draggable-dragging {
  /* Disable transitions during active drag */
  transition: none !important;
}