"use client"

import { useState } from "react"
import { Task, Priority } from "./types"
import { useSortable } from "@dnd-kit/sortable"
import { CSS } from "@dnd-kit/utilities"
import { format } from "date-fns"

interface TaskCardProps {
  task: Task
  columnId?: string // Make optional for DragOverlay
  onEdit?: (taskId: string, updates: Partial<Task>) => void
  onDelete?: (taskId: string) => void
}

export function TaskCard({ task, columnId, onDelete, onEdit }: TaskCardProps) {
  // Setup sortable functionality
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging: isActiveDrag
  } = useSortable({
    id: task.id,
    data: {
      type: 'task',
      task,
      columnId
    }
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isActiveDrag ? 0.4 : 1,
    zIndex: isActiveDrag ? 999 : 1,
    position: isActiveDrag ? 'relative' : undefined,
    // Add shadow and scale for better visual during drag
    boxShadow: isActiveDrag ? '0 10px 25px -5px rgba(0, 0, 0, 0.1)' : undefined
  };
  
  // Format task due date
  const formatDate = (date: Date) => {
    if (!date) return "";
    return new Date(date).toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric' 
    });
  };

  return (
    <div
      ref={setNodeRef}
      style={style as React.CSSProperties}
      {...attributes}
      {...listeners}
      className={`task-card ${isActiveDrag ? 'dragging' : ''}`}
      data-task-id={task.id}
      data-column-id={columnId}
    >
      <div className="mb-2">
        <h3 className="text-base font-medium">{task.title}</h3>
      </div>
      
      {/* Task tags section */}
      <div className="flex flex-wrap gap-1 mb-2">
        {/* Only show tags if the properties exist */}
        {task.type && (
          <span className={`task-tag ${task.type === 'bug' ? 'bug' : 'feature'}`}>
            {task.type === 'bug' ? '🐛 Bug' : '✨ Feature'}
          </span>
        )}
        
        {task.sprint && task.sprint !== 'none' && (
          <span className={`task-tag ${task.sprint === 'current' ? 'sprint' : 'backlog'}`}>
            {task.sprint === 'current' ? '🏃 Sprint' : '📋 Backlog'}
          </span>
        )}
      </div>
      
      <p className="text-sm text-muted-foreground mb-3 line-clamp-2">{task.description}</p>
      
      <div className="flex items-center justify-between mt-auto">
        <span className={`priority ${task.priority}`}>
          {task.priority.charAt(0).toUpperCase() + task.priority.slice(1)}
        </span>
        
        <div className="flex items-center gap-2">
          {task.assignee && (
            <div className="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center text-xs text-primary">
              {task.assignee.name.charAt(0)}
            </div>
          )}
          <span className="text-xs text-muted-foreground">
            {formatDate(task.dueDate)}
          </span>
        </div>
      </div>
    </div>
  );
}