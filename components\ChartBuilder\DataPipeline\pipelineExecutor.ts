import { Node, <PERSON> } from 'reactflow'
import { PipelineNodeData, Dataset } from './types'

export interface PipelineResult {
  data: any[]
  headers: string[]
  datasetName?: string
  datasetId?: string
}

export async function executePipeline(
  nodes: Node<PipelineNodeData>[],
  edges: Edge[]
): Promise<PipelineResult> {
  if (nodes.length === 0) {
    return { data: [], headers: [] }
  }

  // Find the output node or the last node in the pipeline
  const outputNode = nodes.find(node => node.data.type === 'output')
  if (!outputNode) {
    throw new Error('No output node found in pipeline')
  }

  // Execute the pipeline by traversing from the output node
  const result = await executeNode(outputNode, nodes, edges, new Map())
  return result
}

async function executeNode(
  node: Node<PipelineNodeData>,
  allNodes: Node<PipelineNodeData>[],
  allEdges: Edge[],
  cache: Map<string, PipelineResult>
): Promise<PipelineResult> {
  // Check cache first
  if (cache.has(node.id)) {
    return cache.get(node.id)!
  }

  let result: PipelineResult

  if (node.data.type === 'dataset') {
    // Base case: dataset node
    if (!node.data.dataset) {
      throw new Error(`Dataset not found for node ${node.id}`)
    }
    
    result = {
      data: node.data.dataset.data,
      headers: node.data.dataset.headers,
      datasetName: node.data.dataset.name,
      datasetId: node.data.dataset.id
    }
  } else {
    // Find incoming edges to this node
    const incomingEdges = allEdges.filter(edge => edge.target === node.id)
    
    if (incomingEdges.length === 0) {
      throw new Error(`No input for ${node.data.type} node`)
    }

    // Get source nodes and execute them
    const sourceNodes = incomingEdges.map(edge => 
      allNodes.find(n => n.id === edge.source)
    ).filter(Boolean) as Node<PipelineNodeData>[]

    const sourceResults = await Promise.all(
      sourceNodes.map(sourceNode => executeNode(sourceNode, allNodes, allEdges, cache))
    )

    // Execute the current node based on its type
    console.log(`Executing ${node.data.type} node:`, node.id, 'with config:', node.data.config)

    switch (node.data.type) {
      case 'concat':
        if (sourceResults.length < 2) {
          throw new Error('Concat requires at least 2 inputs')
        }
        result = executeConcatNode(sourceResults, node.data.config)
        break

      case 'join':
        if (sourceResults.length < 2) {
          throw new Error('Join requires at least 2 inputs')
        }
        result = executeJoinNode(sourceResults, node.data.config)
        break

      case 'transform':
        if (sourceResults.length === 0) {
          throw new Error('Transform requires at least 1 input')
        }
        result = executeTransformNode(sourceResults[0], node.data.config)
        break

      case 'output':
        // For output node, just return the result from its input
        if (sourceResults.length === 0) {
          throw new Error('Output requires at least 1 input')
        }
        result = sourceResults[0]
        break

      default:
        throw new Error(`Unknown node type: ${node.data.type}`)
    }

    console.log(`${node.data.type} node result:`, {
      rows: result.data.length,
      columns: result.headers.length,
      headers: result.headers
    })
  }

  // Cache the result
  cache.set(node.id, result)
  return result
}

function executeConcatNode(
  sourceResults: PipelineResult[],
  config: any
): PipelineResult {
  if (sourceResults.length === 0) {
    return { data: [], headers: [] }
  }

  if (sourceResults.length === 1) {
    return sourceResults[0]
  }

  // Check if column selection is configured
  const columnSelection = config?.columnSelection
  let allSelectedColumns: string[] = []
  let processedResults: PipelineResult[] = []

  if (columnSelection && Object.keys(columnSelection).length > 0) {
    // Process each source result with column selection

    sourceResults.forEach((result, index) => {
      // Try to match by dataset name first, then fallback to index
      const datasetName = result.datasetName || result.datasetId || `dataset_${index}`
      let selectedColumns: string[] = columnSelection[datasetName] || []

      // If no specific selection found, try index-based key as fallback
      if (selectedColumns.length === 0) {
        const indexKey = `dataset_${index}`
        selectedColumns = columnSelection[indexKey] || []
      }

      // If still no selection, use all columns
      if (selectedColumns.length === 0) {
        selectedColumns = result.headers
      }

      console.log(`Processing dataset ${index}:`, {
        datasetName,
        selectedColumns,
        originalHeaders: result.headers,
        dataLength: result.data.length,
        columnSelectionKeys: Object.keys(columnSelection),
        columnSelectionConfig: columnSelection
      })

      // Filter columns for this dataset
      const filteredData = result.data.map(row => {
        const newRow: any = {}
        selectedColumns.forEach((col: string) => {
          // Make sure the column exists in the source data
          if (result.headers.includes(col)) {
            newRow[col] = row[col]
          } else {
            console.warn(`Column ${col} not found in dataset ${index}`)
            newRow[col] = null
          }
        })
        return newRow
      })

      processedResults.push({
        data: filteredData,
        headers: selectedColumns
      })

      // Add to all selected columns (avoiding duplicates)
      selectedColumns.forEach((col: string) => {
        if (!allSelectedColumns.includes(col)) {
          allSelectedColumns.push(col)
        }
      })
    })
  } else {
    // No column selection, use all columns from all datasets
    processedResults = sourceResults
    // Combine all unique headers
    sourceResults.forEach(result => {
      result.headers.forEach(header => {
        if (!allSelectedColumns.includes(header)) {
          allSelectedColumns.push(header)
        }
      })
    })
  }

  // Handle different concatenation types
  // If column selection is configured, default to horizontal concatenation
  const hasColumnSelection = columnSelection && Object.keys(columnSelection).length > 0
  const concatType = config?.concatType || (hasColumnSelection ? 'horizontal' : 'append')
  let data: any[] = []

  if (concatType === 'horizontal') {
    // Horizontal concatenation: merge columns side-by-side
    data = executeHorizontalConcat(processedResults, allSelectedColumns)
  } else {
    // Vertical concatenation (append/union): stack rows
    for (const result of processedResults) {
      console.log(`Concatenating data from result:`, {
        headers: result.headers,
        dataLength: result.data.length,
        sampleRow: result.data[0]
      })

      // Map data to match the unified header structure
      const mappedData = result.data.map(row => {
        const newRow: any = {}
        allSelectedColumns.forEach(header => {
          // Check if this header exists in the current result
          if (result.headers.includes(header)) {
            newRow[header] = row[header]
          } else {
            // Column doesn't exist in this dataset, set to null
            newRow[header] = null
          }
        })
        return newRow
      })

      console.log(`Mapped data sample:`, mappedData[0])
      data.push(...mappedData)
    }

    if (concatType === 'union') {
      // Remove duplicates by converting to JSON strings and back
      const uniqueRows = new Map()
      data.forEach(row => {
        const key = JSON.stringify(row)
        if (!uniqueRows.has(key)) {
          uniqueRows.set(key, row)
        }
      })
      data = Array.from(uniqueRows.values())
    }
  }

  return { data, headers: allSelectedColumns }
}

function executeHorizontalConcat(
  processedResults: PipelineResult[],
  allSelectedColumns: string[]
): any[] {
  if (processedResults.length === 0) {
    return []
  }

  // Find the maximum number of rows across all datasets
  const maxRows = Math.max(...processedResults.map(result => result.data.length))
  
  const data: any[] = []

  // Create rows by merging data horizontally
  for (let rowIndex = 0; rowIndex < maxRows; rowIndex++) {
    const mergedRow: any = {}
    
    // Initialize all columns with null
    allSelectedColumns.forEach(col => {
      mergedRow[col] = null
    })
    
    // Merge data from each dataset for this row index
    processedResults.forEach((result, datasetIndex) => {
      if (rowIndex < result.data.length) {
        const sourceRow = result.data[rowIndex]
        
        // Copy values from this dataset's row
        result.headers.forEach(header => {
          if (allSelectedColumns.includes(header)) {
            // If column exists in multiple datasets, prefer the first non-null value
            if (mergedRow[header] === null || mergedRow[header] === undefined) {
              mergedRow[header] = sourceRow[header]
            }
          }
        })
      }
    })
    
    data.push(mergedRow)
  }

  return data
}

function executeJoinNode(
  sourceResults: PipelineResult[],
  config: any
): PipelineResult {
  if (sourceResults.length < 2) {
    throw new Error('Join requires at least 2 inputs')
  }

  const leftResult = sourceResults[0]
  const rightResult = sourceResults[1]
  
  const joinType = config?.joinType || 'inner'
  const joinKeys = config?.joinKeys || []

  // If no join keys specified, do a cross join
  if (joinKeys.length === 0) {
    return executeCrossJoin(leftResult, rightResult)
  }

  // Execute the join based on join keys
  return executeKeyedJoin(leftResult, rightResult, joinKeys, joinType)
}

function executeCrossJoin(
  leftResult: PipelineResult,
  rightResult: PipelineResult
): PipelineResult {
  const headers = [...leftResult.headers, ...rightResult.headers]
  const data: any[] = []

  for (const leftRow of leftResult.data) {
    for (const rightRow of rightResult.data) {
      data.push({ ...leftRow, ...rightRow })
    }
  }

  return { data, headers }
}

function executeKeyedJoin(
  leftResult: PipelineResult,
  rightResult: PipelineResult,
  joinKeys: { left: string; right: string }[],
  joinType: string
): PipelineResult {
  const headers = [...leftResult.headers, ...rightResult.headers]
  const data: any[] = []

  // Create a map for the right table for efficient lookup
  const rightMap = new Map<string, any[]>()
  
  for (const rightRow of rightResult.data) {
    const key = joinKeys.map(jk => rightRow[jk.right]).join('|')
    if (!rightMap.has(key)) {
      rightMap.set(key, [])
    }
    rightMap.get(key)!.push(rightRow)
  }

  // Perform the join
  for (const leftRow of leftResult.data) {
    const key = joinKeys.map(jk => leftRow[jk.left]).join('|')
    const matchingRightRows = rightMap.get(key) || []

    if (matchingRightRows.length > 0) {
      // Found matches
      for (const rightRow of matchingRightRows) {
        data.push({ ...leftRow, ...rightRow })
      }
    } else if (joinType === 'left' || joinType === 'full') {
      // Left join: include left row with null values for right columns
      const nullRightRow: any = {}
      rightResult.headers.forEach(header => {
        nullRightRow[header] = null
      })
      data.push({ ...leftRow, ...nullRightRow })
    }
  }

  // For full outer join, add unmatched right rows
  if (joinType === 'full') {
    const leftKeys = new Set(
      leftResult.data.map(row => 
        joinKeys.map(jk => row[jk.left]).join('|')
      )
    )

    for (const rightRow of rightResult.data) {
      const key = joinKeys.map(jk => rightRow[jk.right]).join('|')
      if (!leftKeys.has(key)) {
        const nullLeftRow: any = {}
        leftResult.headers.forEach(header => {
          nullLeftRow[header] = null
        })
        data.push({ ...nullLeftRow, ...rightRow })
      }
    }
  }

  return { data, headers }
}

function executeTransformNode(
  sourceResult: PipelineResult,
  config: any
): PipelineResult {
  const transformType = config?.transformType || 'filter'

  switch (transformType) {
    case 'filter':
      return executeFilter(sourceResult, config?.filters || [])
    
    case 'select':
      return executeSelect(sourceResult, config?.selectedColumns || [])
    
    case 'aggregate':
      return executeAggregate(sourceResult, config?.aggregations || [])
    
    case 'sort':
      return executeSort(sourceResult, config?.sortBy || [])
    
    default:
      return sourceResult
  }
}

function executeFilter(
  sourceResult: PipelineResult,
  filters: { column: string; operator: string; value: any }[]
): PipelineResult {
  if (filters.length === 0) {
    return sourceResult
  }

  const filteredData = sourceResult.data.filter(row => {
    return filters.every(filter => {
      const value = row[filter.column]
      const filterValue = filter.value

      switch (filter.operator) {
        case '=':
        case '==':
          return value == filterValue
        case '!=':
          return value != filterValue
        case '>':
          return value > filterValue
        case '>=':
          return value >= filterValue
        case '<':
          return value < filterValue
        case '<=':
          return value <= filterValue
        case 'contains':
          return value && value.toString().toLowerCase().includes(filterValue.toString().toLowerCase())
        default:
          return true
      }
    })
  })

  return {
    data: filteredData,
    headers: sourceResult.headers
  }
}

function executeSelect(
  sourceResult: PipelineResult,
  selectedColumns: string[]
): PipelineResult {
  if (selectedColumns.length === 0) {
    // If no columns selected, return empty result
    return {
      data: [],
      headers: [],
      datasetName: sourceResult.datasetName,
      datasetId: sourceResult.datasetId
    }
  }

  if (selectedColumns.includes('*')) {
    return sourceResult
  }

  const validColumns = selectedColumns.filter(col =>
    sourceResult.headers.includes(col)
  )

  if (validColumns.length === 0) {
    return {
      data: [],
      headers: [],
      datasetName: sourceResult.datasetName,
      datasetId: sourceResult.datasetId
    }
  }

  const selectedData = sourceResult.data.map(row => {
    const newRow: any = {}
    validColumns.forEach(col => {
      newRow[col] = row[col]
    })
    return newRow
  })

  return {
    data: selectedData,
    headers: validColumns,
    datasetName: sourceResult.datasetName,
    datasetId: sourceResult.datasetId
  }
}

function executeAggregate(
  sourceResult: PipelineResult,
  aggregations: { column: string; function: string }[]
): PipelineResult {
  if (aggregations.length === 0) {
    return sourceResult
  }

  const result: any = {}
  const headers: string[] = []

  aggregations.forEach(agg => {
    const columnData = sourceResult.data.map(row => row[agg.column]).filter(val => val != null)
    const headerName = `${agg.function}_${agg.column}`
    headers.push(headerName)

    switch (agg.function.toLowerCase()) {
      case 'count':
        result[headerName] = columnData.length
        break
      case 'sum':
        result[headerName] = columnData.reduce((sum, val) => sum + (Number(val) || 0), 0)
        break
      case 'avg':
      case 'average':
        const sum = columnData.reduce((sum, val) => sum + (Number(val) || 0), 0)
        result[headerName] = columnData.length > 0 ? sum / columnData.length : 0
        break
      case 'min':
        result[headerName] = columnData.length > 0 ? Math.min(...columnData.map(Number)) : null
        break
      case 'max':
        result[headerName] = columnData.length > 0 ? Math.max(...columnData.map(Number)) : null
        break
      default:
        result[headerName] = null
    }
  })

  return {
    data: [result],
    headers
  }
}

function executeSort(
  sourceResult: PipelineResult,
  sortBy: { column: string; direction: 'asc' | 'desc' }[]
): PipelineResult {
  if (sortBy.length === 0) {
    return sourceResult
  }

  const sortedData = [...sourceResult.data].sort((a, b) => {
    for (const sort of sortBy) {
      const aVal = a[sort.column]
      const bVal = b[sort.column]
      
      let comparison = 0
      if (aVal < bVal) comparison = -1
      else if (aVal > bVal) comparison = 1
      
      if (comparison !== 0) {
        return sort.direction === 'desc' ? -comparison : comparison
      }
    }
    return 0
  })

  return {
    data: sortedData,
    headers: sourceResult.headers
  }
}
