/**
 * Grid Layout Fixes
 * 
 * Critical fixes for react-grid-layout to ensure smooth drag and drop
 * where cards accurately follow the cursor.
 */

/* Critical fix for drag behavior - ensure immediate cursor following */
.react-draggable-dragging {
  transition: none !important;
  transition-property: none !important;
  cursor: grabbing !important;
  z-index: 999 !important;
}

/* For proper cursor behavior */
.react-grid-item {
  transition: none !important;
  transition-property: none !important;
}

/* Fix React Grid Layout container */
.react-grid-layout {
  transition: none !important;
}

/* Fix placeholder - essential for showing where the item will be placed */
.react-grid-placeholder {
  transition: none !important;
  transition-property: none !important;
}

/* Better resize handle behavior */
.react-resizable-handle {
  cursor: se-resize !important;
}

/* Remove animation during drag */
.react-draggable-dragging * {
  animation: none !important;
  transition: none !important;
} 