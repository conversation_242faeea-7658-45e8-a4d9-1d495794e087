import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { GoogleGenerativeAI } from '@google/generative-ai';
import prisma from '@/lib/db';

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);

interface RequestBody {
  message: string;
  selectedDatasets: string[];
  conversationHistory?: Array<{ role: string; content: string }>;
  model?: string;
  images?: Array<{ name: string; data: string; mimeType: string }>;
}

export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const { userId } = auth();
    if (!userId) {
      return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
    }

    // Get request body
    const body: RequestBody = await req.json();
    const {
      message,
      selectedDatasets = [],
      conversationHistory = [],
      model = 'gemini-1.5-flash',
      images = []
    } = body;

    // Validate input
    if (!message || typeof message !== 'string' || message.trim() === '') {
      return NextResponse.json({
        success: false,
        error: 'Message is required'
      }, { status: 400 });
    }

    // Allow messages without datasets for general AI assistance

    // Get user from database
    const user = await prisma.user.findUnique({
      where: { clerkId: userId },
      select: { id: true }
    });

    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'User not found'
      }, { status: 404 });
    }

    // Fetch the selected datasets with their actual data (if any)
    let datasets: any[] = [];
    if (selectedDatasets.length > 0) {
      datasets = await prisma.dataSet.findMany({
        where: {
          id: { in: selectedDatasets },
          userId: user.id
        },
        select: {
          id: true,
          name: true,
          description: true,
          data: true,
          headers: true,
          fileType: true
        }
      });

      // Continue even if no datasets found - allow general AI assistance
    }

    // Prepare dataset information for AI with more context
    const datasetInfo = datasets.map(dataset => {
      const data = Array.isArray(dataset.data) ? dataset.data : [];
      const sampleData = data.slice(0, 10); // Show first 10 rows as sample for better context

      return {
        name: dataset.name,
        description: dataset.description || 'No description',
        headers: dataset.headers || [],
        totalRows: data.length,
        sampleData: sampleData,
        fileType: dataset.fileType,
        fullData: data // Include full data for accurate analysis
      };
    });

    // Create the AI prompt with chart and table generation capabilities
    let systemPrompt = `You are an advanced AI assistant with data analysis and visualization capabilities. You can help with general questions, create charts, generate tables, and analyze datasets.

${datasets.length > 0 ? `
AVAILABLE DATASETS:
${datasetInfo.map(ds => `
Dataset: "${ds.name}"
Description: ${ds.description}
Columns: ${ds.headers.join(', ')}
Total Rows: ${ds.totalRows}
Sample Data (first 10 rows):
${ds.sampleData.map((row: any, i: number) => `Row ${i + 1}: ${JSON.stringify(row)}`).join('\n')}

FULL DATASET FOR ANALYSIS:
${JSON.stringify(ds.fullData)}
`).join('\n---\n')}
` : 'No datasets currently selected. You can still help with general questions, create sample charts, or generate example tables.'}

${images.length > 0 ? `
UPLOADED IMAGES: ${images.length} image(s) provided for analysis
` : ''}

CHART AND TABLE GENERATION:
When the user asks for charts or tables, you can generate them using the following format:

For CHARTS, include in your response:
CHART_DATA_START
{
  "type": "bar|line|pie|scatter",
  "title": "Chart Title",
  "data": {
    "labels": ["Label1", "Label2", "Label3"],
    "datasets": [{
      "label": "Dataset Label",
      "data": [10, 20, 30],
      "backgroundColor": ["#3B82F6", "#EF4444", "#10B981"]
    }]
  }
}
CHART_DATA_END

For TABLES, include in your response:
TABLE_DATA_START
{
  "title": "Table Title",
  "headers": ["Column1", "Column2", "Column3"],
  "rows": [
    ["Row1Col1", "Row1Col2", "Row1Col3"],
    ["Row2Col1", "Row2Col2", "Row2Col3"]
  ]
}
TABLE_DATA_END

INSTRUCTIONS:
1. Answer questions directly and naturally
2. When datasets are available, analyze ALL data thoroughly
3. For chart requests, create appropriate visualizations with realistic data
4. For table requests, generate well-structured tables with relevant data
5. Provide insights and explanations for generated content
6. Use natural language and be conversational
7. If no datasets are selected, create sample/example data for demonstrations

CONVERSATION HISTORY:
${conversationHistory.map(msg => `${msg.role}: ${msg.content}`).join('\n')}

USER QUESTION: ${message}

Please provide a helpful response. If charts or tables are requested, include the appropriate data blocks in your response.`;

    // Retry logic with fallback models
    const retryWithFallback = async (primaryModel: string, maxRetries: number = 3): Promise<string> => {
      const fallbackModels = [
        primaryModel,
        'gemini-1.5-flash-002',
        'gemini-1.5-flash',
        'gemini-1.0-pro'
      ];

      for (let attempt = 0; attempt < maxRetries; attempt++) {
        for (const modelToTry of fallbackModels) {
          try {
            console.log(`Attempt ${attempt + 1}: Trying model ${modelToTry}`);

            const geminiModel = genAI.getGenerativeModel({
              model: modelToTry,
              generationConfig: {
                temperature: 0.1,
                topP: 0.8,
                topK: 40,
                maxOutputTokens: 2048,
              }
            });

            // Prepare content for Gemini (text + images)
            const parts: any[] = [{ text: systemPrompt }];

            // Add images if provided and model supports them
            const supportsImages = modelToTry.includes('1.5');
            if (images.length > 0 && supportsImages) {
              images.forEach(img => {
                parts.push({
                  inlineData: {
                    mimeType: img.mimeType,
                    data: img.data
                  }
                });
              });
            } else if (images.length > 0 && !supportsImages) {
              // Add image descriptions to text for non-vision models
              parts[0].text += `\n\nNote: ${images.length} image(s) were uploaded but this model doesn't support image analysis. Please describe what you'd like to know about the images.`;
            }

            const result = await geminiModel.generateContent(parts);
            const response = result.response.text();

            console.log(`Successfully generated response with model: ${modelToTry}`);
            return response;

          } catch (error: any) {
            console.log(`Model ${modelToTry} failed on attempt ${attempt + 1}:`, error.message);

            // If it's a 503 (overloaded) error, wait before retrying
            if (error.status === 503) {
              const waitTime = Math.min(1000 * Math.pow(2, attempt), 10000); // Exponential backoff, max 10s
              console.log(`Waiting ${waitTime}ms before retry...`);
              await new Promise(resolve => setTimeout(resolve, waitTime));
            }

            // Continue to next model or retry
            continue;
          }
        }
      }

      throw new Error('All models failed after multiple retries. Please try again later.');
    };

    console.log('Sending request to Gemini:', {
      model,
      datasets: datasets.map(d => ({ name: d.name, rows: Array.isArray(d.data) ? d.data.length : 0 })),
      images: images.length
    });

    const response = await retryWithFallback(model);

    // Extract chart data from response
    let chartData = null;
    const chartMatch = response.match(/CHART_DATA_START\s*([\s\S]*?)\s*CHART_DATA_END/);
    if (chartMatch) {
      try {
        chartData = JSON.parse(chartMatch[1].trim());
      } catch (error) {
        console.error('Error parsing chart data:', error);
      }
    }

    // Extract table data from response
    let tableData = null;
    const tableMatch = response.match(/TABLE_DATA_START\s*([\s\S]*?)\s*TABLE_DATA_END/);
    if (tableMatch) {
      try {
        tableData = JSON.parse(tableMatch[1].trim());
      } catch (error) {
        console.error('Error parsing table data:', error);
      }
    }

    // Clean response by removing data blocks
    let cleanResponse = response
      .replace(/CHART_DATA_START[\s\S]*?CHART_DATA_END/g, '')
      .replace(/TABLE_DATA_START[\s\S]*?TABLE_DATA_END/g, '')
      .trim();

    // Extract sources from the response (simple pattern matching)
    const sources: Array<{ dataset: string; reference: string; type: 'data' | 'calculation' | 'example' }> = [];

    // Look for dataset references in the response
    datasets.forEach(dataset => {
      const datasetName = dataset.name;
      if (response.includes(datasetName)) {
        // Check for different types of references
        if (response.includes(`analysis of ${datasetName}`) || response.includes(`Based on ${datasetName}`)) {
          sources.push({
            dataset: datasetName,
            reference: `Analysis performed on ${datasetName} dataset with ${Array.isArray(dataset.data) ? dataset.data.length : 0} rows`,
            type: 'data'
          });
        }
        if (response.includes(`Row`) && response.includes(datasetName)) {
          sources.push({
            dataset: datasetName,
            reference: `Specific examples found in ${datasetName} dataset`,
            type: 'example'
          });
        }
        if (response.includes(`Calculating`) || response.includes(`calculation`)) {
          sources.push({
            dataset: datasetName,
            reference: `Calculations performed using ${datasetName} data`,
            type: 'calculation'
          });
        }
      }
    });

    // Add image sources if images were analyzed
    if (images.length > 0) {
      images.forEach(img => {
        sources.push({
          dataset: img.name,
          reference: `Image analysis and OCR performed on uploaded image`,
          type: 'data'
        });
      });
    }

    // Log for debugging
    console.log('AI Response generated successfully');

    return NextResponse.json({
      success: true,
      content: cleanResponse,
      model: model,
      datasetsAnalyzed: datasets.map(d => ({
        name: d.name,
        totalRows: Array.isArray(d.data) ? d.data.length : 0
      })),
      sources: sources,
      imagesAnalyzed: images.length,
      chart: chartData,
      table: tableData
    });

  } catch (error: any) {
    console.error('Error in simple AI chat:', error);
    return NextResponse.json({ 
      success: false, 
      error: `Internal server error: ${error.message}` 
    }, { status: 500 });
  }
}
