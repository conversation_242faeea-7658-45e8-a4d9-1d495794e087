'use client'

import { useState, useRef, useEffect } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Maximize2, Minimize2, Play, Save, X } from "lucide-react"
import Editor from '@monaco-editor/react'
import { useTheme } from 'next-themes'
import { toast } from 'sonner'

interface FullscreenEditorProps {
  isOpen: boolean
  onClose: () => void
  content: string
  language: string
  onContentChange: (value: string) => void
  onRun?: () => void
  isRunning?: boolean
  cellId: string
}

export function FullscreenEditor({
  isOpen,
  onClose,
  content,
  language,
  onContentChange,
  onRun,
  isRunning = false,
  cellId
}: FullscreenEditorProps) {
  const { theme } = useTheme()
  const editorRef = useRef<any>(null)
  const [localContent, setLocalContent] = useState(content)
  const [hasChanges, setHasChanges] = useState(false)

  // Update local content when prop changes
  useEffect(() => {
    setLocalContent(content)
    setHasChanges(false)
  }, [content])

  // Handle editor mount
  const handleEditorDidMount = (editor: any) => {
    editorRef.current = editor
    
    // Focus the editor
    editor.focus()
    
    // Add keyboard shortcuts
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.Enter, () => {
      if (onRun && !isRunning) {
        handleRun()
      }
    })
    
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
      handleSave()
    })
    
    editor.addCommand(monaco.KeyCode.Escape, () => {
      if (hasChanges) {
        handleSave()
      }
      onClose()
    })
  }

  // Handle content change
  const handleContentChange = (value: string | undefined) => {
    if (value !== undefined) {
      setLocalContent(value)
      setHasChanges(value !== content)
    }
  }

  // Handle save
  const handleSave = () => {
    if (onContentChange) {
      onContentChange(localContent)
      setHasChanges(false)
      toast.success('Code saved', { duration: 1000 })
    } else {
      toast.error('Cannot save: no content change handler')
    }
  }

  // Handle run
  const handleRun = () => {
    if (hasChanges) {
      handleSave()
    }
    if (onRun) {
      onRun()
    }
  }

  // Handle close with unsaved changes
  const handleClose = () => {
    if (hasChanges) {
      if (confirm('You have unsaved changes. Save before closing?')) {
        handleSave()
      }
    }
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-[95vw] max-h-[95vh] w-full h-full p-0 gap-0">
        <DialogHeader className="px-4 py-2 border-b flex flex-row items-center justify-between space-y-0">
          <DialogTitle className="text-sm font-medium flex items-center gap-2">
            <Maximize2 className="h-4 w-4" />
            Fullscreen Editor - Cell {cellId}
            {hasChanges && <span className="text-orange-500">●</span>}
          </DialogTitle>
          
          <div className="flex items-center gap-2">
            {/* Language indicator */}
            <div className="text-xs bg-muted px-2 py-1 rounded">
              {language.toUpperCase()}
            </div>
            
            {/* Save button */}
            <Button
              variant="outline"
              size="sm"
              onClick={handleSave}
              disabled={!hasChanges}
              className="h-7 text-xs"
            >
              <Save className="h-3 w-3 mr-1" />
              Save
            </Button>
            
            {/* Run button */}
            {onRun && (
              <Button
                variant="default"
                size="sm"
                onClick={handleRun}
                disabled={isRunning}
                className="h-7 text-xs"
              >
                <Play className="h-3 w-3 mr-1" />
                {isRunning ? 'Running...' : 'Run'}
              </Button>
            )}
            
            {/* Close button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              className="h-7 w-7 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>
        
        <div className="flex-1 relative">
          <Editor
            height="100%"
            language={language}
            value={localContent}
            theme={theme === 'dark' ? 'vs-dark' : 'light'}
            options={{
              minimap: { enabled: true },
              lineNumbers: 'on',
              folding: true,
              scrollBeyondLastLine: false,
              wordWrap: 'on',
              contextmenu: true,
              fontSize: 14,
              lineHeight: 20,
              padding: { top: 16, bottom: 16 },
              automaticLayout: true,
              bracketPairColorization: { enabled: true },
              colorDecorators: true,
              cursorBlinking: 'smooth',
              cursorSmoothCaretAnimation: 'on',
              find: {
                addExtraSpaceOnTop: false,
                autoFindInSelection: 'never',
                seedSearchStringFromSelection: 'always'
              },
              fontLigatures: true,
              formatOnPaste: true,
              formatOnType: true,
              hover: { enabled: true },
              lightbulb: { enabled: true },
              links: true,
              mouseWheelZoom: true,
              multiCursorModifier: 'ctrlCmd',
              occurrencesHighlight: 'singleFile',
              parameterHints: { enabled: true },
              quickSuggestions: {
                other: true,
                comments: false,
                strings: false
              },
              renderLineHighlight: 'all',
              renderWhitespace: 'selection',
              selectOnLineNumbers: true,
              selectionHighlight: true,
              smoothScrolling: true,
              suggestOnTriggerCharacters: true,
              tabCompletion: 'on',
              useTabStops: true,
              wordBasedSuggestions: 'matchingDocuments',
              wordHighlight: 'semantic',
              wrappingIndent: 'indent'
            }}
            onMount={handleEditorDidMount}
            onChange={handleContentChange}
          />
        </div>
        
        {/* Status bar */}
        <div className="px-4 py-2 border-t bg-muted/30 flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center gap-4">
            <span>Language: {language}</span>
            <span>Lines: {localContent.split('\n').length}</span>
            <span>Characters: {localContent.length}</span>
          </div>
          
          <div className="flex items-center gap-4">
            <span>Ctrl+Enter: Run</span>
            <span>Ctrl+S: Save</span>
            <span>Esc: Close</span>
            {hasChanges && <span className="text-orange-500">Unsaved changes</span>}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

// Extend monaco types
declare global {
  const monaco: any
}
