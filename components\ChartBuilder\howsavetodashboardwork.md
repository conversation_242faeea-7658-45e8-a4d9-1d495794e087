# Save to Dashboard Flow (Enhanced with Zustand)

## Chart Creation in Cells
- Users create charts by running SQL, Python, or JavaScript code in cells within the ChartBuilder component.
- When a cell executes successfully, the results are displayed in the QueryResult component.
- The QueryResult component provides visualization options (table, chart, plots, etc.).

## Chart Configuration
- In the QueryResult component, users can configure charts by selecting chart type, axes, and other visual properties.
- The chart configuration is stored in a memory cache (chartConfigCache) keyed by cell ID.

## Saving Charts to Dashboard
- The QueryResult component has a "Save to Dashboard" button that appears when in chart view mode.
- When clicked, it calls the handleSaveChart function which:
  - Collects the current chart configuration and data
  - Creates a complete chart object with all necessary properties
  - Calls the parent component's onSaveChart callback

## Zustand Dashboard Store
- The dashboardStore.ts file implements a Zustand store for managing dashboard items.
- It provides functions to add, update, remove, and get different types of items (charts, tables, plots).
- The store uses the persist middleware to save items to localStorage.
- When a chart is saved, it's added to the store's charts array with proper deduplication.
- The store is the single source of truth for all dashboard items.

## Dashboard Component
- The Dashboard component reads items directly from the Zustand store.
- It no longer needs to periodically check for new items, preventing duplication issues.
- New charts are added to the dashboard's orderedItems state only once.
- The Dashboard component renders these items using the DashboardLayout component.

## Dashboard Layout
- The DashboardLayout component renders each item (chart, heading, text) in a grid layout.
- It supports drag-and-drop repositioning and resizing of items.
- Each chart is displayed using the ChartVisualizer component.
- Layout changes are saved back to the Zustand store.

## Navigation Between Tabs
- The ChartBuilder component has tabs for "Notebook" and "Dashboard".
- When a user saves a chart from the Notebook tab, it becomes available in the Dashboard tab.
- The Dashboard tab shows all saved charts and allows further customization.

## User Interface Flow
1. User runs a query in a cell
2. User clicks on the "Chart" button to view the data as a chart
3. User configures the chart (axes, type, etc.)
4. User clicks "Save to Dashboard" button
5. The chart is saved to the Zustand store
6. User switches to the Dashboard tab to see and arrange the saved chart

## Benefits of Zustand Implementation
- **No Duplication**: Charts are only added once to the dashboard
- **Consistent State**: All components read from the same source of truth
- **Persistence**: Dashboard state is automatically saved and restored
- **Better Performance**: Fewer state updates and re-renders
- **Simplified Code**: Clearer data flow and fewer side effects

This enhanced architecture provides a seamless workflow where users can create and save multiple visualizations from their data queries, then arrange them into a comprehensive dashboard without duplication issues.