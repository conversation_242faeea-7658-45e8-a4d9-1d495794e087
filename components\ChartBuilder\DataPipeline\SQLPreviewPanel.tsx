'use client'

import React, { useState } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Code2, Co<PERSON>, ChevronDown, ChevronUp } from "lucide-react"
import { toast } from 'sonner'

interface SQLPreviewPanelProps {
  sql: string
}

export function SQLPreviewPanel({ sql }: SQLPreviewPanelProps) {
  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(sql)
      toast.success('SQL copied to clipboard')
    } catch (error) {
      toast.error('Failed to copy SQL')
    }
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b bg-muted/30">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">SQL Query</span>
          <Badge variant="outline" className="text-xs">
            Live Preview
          </Badge>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={copyToClipboard}
          disabled={!sql || sql.includes('Error')}
          className="h-7 px-2"
        >
          <Copy className="h-3 w-3 mr-1" />
          Copy
        </Button>
      </div>

      {/* SQL Content */}
      <div className="flex-1 p-3">
        {!sql || sql.includes('-- No nodes') ? (
          <div className="h-full flex items-center justify-center text-center">
            <div className="space-y-2">
              <Code2 className="h-8 w-8 mx-auto text-muted-foreground" />
              <p className="text-sm text-muted-foreground">
                Connect nodes to generate SQL
              </p>
            </div>
          </div>
        ) : (
          <pre className="text-xs bg-background border rounded-md p-3 h-full overflow-auto font-mono leading-relaxed">
            <code className="text-foreground">
              {sql}
            </code>
          </pre>
        )}
      </div>
    </div>
  )
}
