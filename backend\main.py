"""
Clean and simplified FastAPI backend for HRatlas
Uses Jupyter kernel for Python execution with Streamlit support
"""

from fastapi import FastAP<PERSON>, HTTPEx<PERSON>, Header, Request
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import pandas as pd
import numpy as np
import json
import os
import aiohttp
import logging
from typing import Optional, Dict, Any, List
from dotenv import load_dotenv
import jwt
import traceback
import math
from fastapi.responses import HTMLResponse
import tempfile
import subprocess
import threading
import socket
import random
from pathlib import Path

# Import our Jupyter kernel
from jupyter_kernel import get_kernel, reset_kernel

# Load environment variables
load_dotenv()
NEXTJS_API_URL = os.getenv('NEXTJS_API_URL', 'http://localhost:3000')
CLERK_SECRET_KEY = os.getenv('CLERK_SECRET_KEY')

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Custom JSON encoder for special float values
class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, float):
            if math.isnan(obj):
                return "NaN"
            elif math.isinf(obj):
                return "Infinity" if obj > 0 else "-Infinity"
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            if np.isnan(obj):
                return "NaN"
            elif np.isinf(obj):
                return "Infinity" if obj > 0 else "-Infinity"
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        return super().default(obj)

# FastAPI app
app = FastAPI(title="HRatlas Backend", version="2.0.0")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Request models
class QueryRequest(BaseModel):
    code: str
    datasets: Optional[List[Dict[str, Any]]] = []
    language: Optional[str] = "python"
    datasetId: Optional[str] = None
    datasetIds: Optional[List[str]] = []
    variableContext: Optional[Dict[str, Any]] = {}

class SQLQueryRequest(BaseModel):
    query: str
    datasets: Optional[List[Dict[str, Any]]] = []

class JSQueryRequest(BaseModel):
    code: str
    datasets: Optional[List[Dict[str, Any]]] = []

# Streamlit app management
streamlit_apps = {}

def find_free_port():
    """Find a free port for Streamlit app"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        s.bind(('', 0))
        s.listen(1)
        port = s.getsockname()[1]
    return port

def create_streamlit_app(code: str, app_id: str) -> Dict[str, Any]:
    """Create and run a Streamlit app from Python code"""
    try:
        # Create temporary directory for the app
        temp_dir = tempfile.mkdtemp(prefix=f"streamlit_app_{app_id}_")
        app_file = os.path.join(temp_dir, "streamlit_app.py")
        
        # Write the code to a file
        with open(app_file, 'w', encoding='utf-8') as f:
            f.write(code)
        
        # Find a free port
        port = find_free_port()
        
        # Start Streamlit app
        cmd = [
            "streamlit", "run", app_file,
            "--server.port", str(port),
            "--server.address", "0.0.0.0",
            "--server.headless", "true",
            "--server.enableCORS", "false",
            "--server.enableXsrfProtection", "false",
            "--browser.gatherUsageStats", "false"
        ]
        
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            cwd=temp_dir
        )
        
        # Store app info
        app_info = {
            'app_id': app_id,
            'port': port,
            'process': process,
            'temp_dir': temp_dir,
            'app_file': app_file,
            'url': f"http://localhost:{port}",
            'embed_url': f"http://localhost:{port}?embed=true&embed_options=hide_loading_screen",
            'status': 'starting'
        }
        
        streamlit_apps[app_id] = app_info
        
        # Wait a moment for the app to start
        import time
        time.sleep(2)
        app_info['status'] = 'running'
        
        return {
            'type': 'streamlit_app',
            'app_id': app_id,
            'url': app_info['url'],
            'embed_url': app_info['embed_url'],
            'open_url': app_info['url'],
            'title': f'Streamlit App {app_id}',
            'status': 'running'
        }
        
    except Exception as e:
        logger.error(f"Error creating Streamlit app: {str(e)}")
        return {
            'type': 'error',
            'message': f'Failed to create Streamlit app: {str(e)}'
        }

# Helper functions
async def get_dataset_from_nextjs(dataset_id: str, auth_token: str) -> Optional[pd.DataFrame]:
    """Fetch dataset from Next.js API"""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(
                f"{NEXTJS_API_URL}/api/datasets/{dataset_id}",
                headers={"Authorization": auth_token}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    return pd.DataFrame(data.get('data', []))
                else:
                    logger.warning(f"Failed to fetch dataset {dataset_id}: {response.status}")
                    return None
    except Exception as e:
        logger.error(f"Error fetching dataset {dataset_id}: {str(e)}")
        return None

def verify_jwt_token(auth_token: str) -> str:
    """Verify JWT token and return user ID"""
    # For development, allow requests without auth if CLERK_SECRET_KEY is not set
    if not CLERK_SECRET_KEY:
        logger.warning("CLERK_SECRET_KEY not set, skipping authentication")
        return "dev_user"

    if not auth_token:
        raise HTTPException(status_code=401, detail="Authorization header is required")

    try:
        token = auth_token.replace("Bearer ", "")
        decoded_token = jwt.decode(token, CLERK_SECRET_KEY, algorithms=["RS256"])
        user_id = decoded_token.get("sub")
        if not user_id:
            raise HTTPException(status_code=401, detail="Invalid token: missing user ID")
        return user_id
    except jwt.InvalidTokenError as e:
        logger.warning(f"Invalid JWT token: {e}")
        raise HTTPException(status_code=401, detail="Invalid token")

# API Endpoints

@app.get("/")
async def root():
    """Health check endpoint"""
    return {"message": "HRatlas Backend API", "version": "2.0.0", "status": "running"}

@app.post("/api/execute-jupyter")
async def execute_jupyter(request: Request, query_request: QueryRequest):
    """
    Jupyter-like code execution endpoint using the JupyterKernel
    """
    try:
        
        code = query_request.code
        datasets = query_request.datasets or []

        # Get the Jupyter kernel instance
        kernel = get_kernel()
        
        # Add Streamlit support to kernel namespace
        def run_streamlit_app():
            """Create and run a Streamlit app from current code"""
            app_id = f"cell_{hash(code) % 10000}"
            return create_streamlit_app(code, app_id)
        
        # Add Streamlit functions to kernel namespace
        kernel.namespace.update({
            'run_streamlit_app': run_streamlit_app,
            'create_streamlit_app': lambda code_str, app_name="app": create_streamlit_app(code_str, app_name),
            'streamlit': __import__('streamlit'),
            'st': __import__('streamlit'),
        })
        
        # Prepare datasets for the kernel
        dataset_list = []
        for dataset in datasets:
            if dataset and 'data' in dataset:
                dataset_list.append({
                    'name': dataset.get('name', f'Dataset {len(dataset_list) + 1}'),
                    'data': dataset['data']
                })

        # Execute code using the Jupyter kernel
        result = kernel.execute_code(code, dataset_list)
        
        # Check if code creates a Streamlit app
        streamlit_result = None
        if 'streamlit' in code.lower() or 'st.' in code:
            try:
                app_id = f"auto_{hash(code) % 10000}"
                streamlit_result = create_streamlit_app(code, app_id)
            except Exception as e:
                logger.warning(f"Failed to auto-create Streamlit app: {e}")
        
        # Transform result to match expected format
        response_data = {
            'data': result.get('data', []),
            'output': result.get('stdout', ''),
            'plots': result.get('plots', []),
            'variables': result.get('variables', {}),
            'variableTypes': {name: type(val).__name__ for name, val in result.get('variables', {}).items()},
            'outputType': 'jupyter',
            'execution_count': result.get('execution_count', 0),
            'html_outputs': result.get('html_outputs', []),
            'media_count': result.get('media_count', 0),
            'plot_count': result.get('plot_count', 0),
            'display_count': result.get('display_count', 0)
        }

        # Check for Plotly figures in plots and add to result
        plots = result.get('plots', [])
        for plot in plots:
            if isinstance(plot, str) and plot.startswith('{') and 'plotly' in plot.lower():
                try:
                    import json
                    plot_data = json.loads(plot)
                    if 'data' in plot_data and 'layout' in plot_data:
                        # This is a Plotly figure - add it to result
                        response_data['result'] = {
                            'application/vnd.plotly.v1+json': plot_data
                        }
                        break
                except Exception as e:
                    logger.warning(f"Failed to parse Plotly data: {e}")
        
        # Add Streamlit app result if created
        if streamlit_result and streamlit_result.get('type') == 'streamlit_app':
            response_data['result'] = streamlit_result
        
        # Handle errors
        if result.get('status') == 'error' and result.get('error'):
            error_info = result['error']
            response_data['error'] = f"{error_info['ename']}: {error_info['evalue']}"
            response_data['errorDetails'] = {
                'message': error_info['evalue'],
                'code': error_info['ename'],
                'stack': '\n'.join(error_info['traceback'])
            }
        
        # Handle stderr
        if result.get('stderr'):
            if 'output' in response_data:
                response_data['output'] += f"\n{result['stderr']}"
            else:
                response_data['output'] = result['stderr']
        
        # Handle result value
        if result.get('result') is not None:
            if isinstance(result['result'], pd.DataFrame):
                response_data['data'] = result['result'].to_dict('records')
            else:
                response_data['result'] = result['result']
        
        return response_data

    except Exception as e:
        logger.error(f"Error in execute_jupyter: {str(e)}")
        logger.error(traceback.format_exc())
        return {
            'error': str(e),
            'errorDetails': {
                'message': str(e),
                'code': 'ExecutionError',
                'stack': traceback.format_exc()
            },
            'data': [],
            'output': '',
            'plots': []
        }

@app.post("/api/execute")
async def execute_legacy(request: Request, query_request: QueryRequest):
    """
    Legacy endpoint that redirects to the new Jupyter kernel
    Maintains compatibility with existing frontend code
    """
    # Just call the new Jupyter endpoint
    return await execute_jupyter(request, query_request)

@app.post("/api/reset-kernel")
async def reset_jupyter_kernel(request: Request):
    """Reset the Jupyter kernel namespace"""
    try:

        # Reset the kernel
        reset_kernel()

        return {"status": "success", "message": "Kernel reset successfully"}

    except Exception as e:
        logger.error(f"Error resetting kernel: {str(e)}")
        return {"status": "error", "message": str(e)}

@app.post("/api/cleanup-streamlit")
async def cleanup_streamlit_app(request: Request):
    """Stop and cleanup a Streamlit app"""
    try:
        # Verify authentication
        auth_token = request.headers.get("authorization")
        user_id = verify_jwt_token(auth_token)

        data = await request.json()
        app_id = data.get('app_id')

        if app_id in streamlit_apps:
            app_info = streamlit_apps[app_id]

            # Stop the process
            if app_info['process']:
                app_info['process'].terminate()
                app_info['process'].wait()

            # Clean up temp directory
            import shutil
            if os.path.exists(app_info['temp_dir']):
                shutil.rmtree(app_info['temp_dir'])

            # Remove from tracking
            del streamlit_apps[app_id]

            return {"status": "success", "message": f"Streamlit app {app_id} stopped"}
        else:
            return {"status": "error", "message": "App not found"}

    except Exception as e:
        logger.error(f"Error stopping Streamlit app: {str(e)}")
        return {"status": "error", "message": str(e)}

@app.post("/api/query")
async def execute_sql_query(request: Request, query_request: SQLQueryRequest):
    """Execute SQL queries using alasql"""
    try:

        # For now, return a simple response
        # TODO: Implement SQL execution with alasql
        return {
            'data': [],
            'message': 'SQL execution not implemented in simplified version'
        }

    except Exception as e:
        logger.error(f"Error in SQL execution: {str(e)}")
        return {
            'error': str(e),
            'data': []
        }

@app.post("/api/execute-js")
async def execute_javascript(request: Request, query_request: JSQueryRequest):
    """Execute JavaScript code"""
    try:

        # For now, return a simple response
        # TODO: Implement JavaScript execution
        return {
            'data': [],
            'message': 'JavaScript execution not implemented in simplified version'
        }

    except Exception as e:
        logger.error(f"Error in JavaScript execution: {str(e)}")
        return {
            'error': str(e),
            'data': []
        }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
