'use client'

import React from 'react';
import { GitBranch, Eye, Clock, ChevronRight } from "lucide-react";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from "@/components/ui/button";
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card, CardHeader, CardContent } from "@/components/ui/card";
import { format } from 'date-fns';
import { Skeleton } from "@/components/ui/skeleton";

interface VersionHistoryProps {
  showVersionHistory: boolean;
  setShowVersionHistory: (show: boolean) => void;
  versionHistory: any[];
  currentDatasetId: string | null;
  loadVersionData: (datasetId: string, versionNumber: number) => void;
  isLoadingVersion?: boolean;
}

export const VersionHistory: React.FC<VersionHistoryProps> = ({
  showVersionHistory,
  setShowVersionHistory,
  versionHistory,
  currentDatasetId,
  loadVersionData,
  isLoadingVersion = false
}) => {
  return (
    <Dialog open={showVersionHistory} onOpenChange={setShowVersionHistory}>
      <DialogContent className="sm:max-w-[800px] max-h-[80vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <GitBranch className="h-5 w-5" />
            Version History
          </DialogTitle>
          <DialogDescription>
            Track changes made to this dataset over time
          </DialogDescription>
        </DialogHeader>
        {isLoadingVersion ? (
          <div className="flex flex-col gap-4 mt-4">
            <Skeleton className="h-6 w-1/3 mb-2" />
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex flex-col gap-2">
                <Skeleton className="h-8 w-1/4 rounded-full" />
                <Skeleton className="h-20 w-full rounded-xl" />
              </div>
            ))}
          </div>
        ) : (
        <div className="flex-1 overflow-hidden mt-4">
          <ScrollArea className="h-[60vh] pr-4">
            {versionHistory.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-40 text-muted-foreground">
                <GitBranch className="h-10 w-10 mb-2 opacity-50" />
                <p>No version history available</p>
              </div>
            ) : (
              <div className="space-y-6">
                {versionHistory.map((version) => (
                  <div
                    key={version.id}
                    className="relative pl-10 before:absolute before:left-4 before:top-6 before:h-full before:w-[2px] before:bg-muted last:before:h-0"
                  >
                    <div className="absolute left-0 top-1 h-8 w-8 rounded-full border-2 border-primary bg-background flex items-center justify-center text-sm font-medium text-primary">
                      {version.versionNumber}
                    </div>
                    <Card className="overflow-hidden transition-all hover:shadow-md">
                      <CardHeader className="pb-2 pt-4 px-5">
                        <div className="flex items-center justify-between">
                          <div className="flex flex-col">
                            <div className="flex items-center gap-2">
                              <Badge variant="outline" className="font-semibold">
                                v{version.versionNumber}
                              </Badge>
                              <span className="text-sm font-medium">
                                {version.user?.name || 'Unknown user'}
                              </span>
                            </div>
                            <div className="flex items-center gap-2 text-sm text-muted-foreground mt-1">
                              <Clock className="h-3.5 w-3.5" />
                              {format(new Date(version.createdAt), 'MMM d, yyyy HH:mm')}
                            </div>
                          </div>
                          <Badge variant="secondary">
                            {version.changes.length} {version.changes.length === 1 ? 'change' : 'changes'}
                          </Badge>
                        </div>
                      </CardHeader>
                      <CardContent className="px-5 pt-0">
                        <div className="mt-2 space-y-3">
                          {version.changes.slice(0, 3).map((change: any, i: number) => (
                            <div
                              key={i}
                              className="flex items-start gap-2 text-sm p-2 rounded-md bg-muted/40"
                            >
                              {change.type === 'edit' ? (
                                <div className="flex flex-col w-full">
                                  <div className="flex items-center justify-between">
                                    <span className="font-medium">
                                      Edited {change.column} (Row {(change.row || 0) + 1})
                                    </span>
                                  </div>
                                  <div className="flex items-center gap-2 mt-1.5">
                                    <span className="line-through text-red-500/80 max-w-[200px] truncate">
                                      {String(change.oldValue || 'empty')}
                                    </span>
                                    <ChevronRight className="h-3.5 w-3.5 text-muted-foreground" />
                                    <span className="text-green-500/80 max-w-[200px] truncate font-medium">
                                      {String(change.newValue || 'empty')}
                                    </span>
                                  </div>
                                </div>
                              ) : change.type === 'deleteRow' ? (
                                <div>
                                  <span className="text-red-500/80">Deleted row {(change.row || 0) + 1}</span>
                                </div>
                              ) : (
                                <div>
                                  <span className="text-red-500/80">Deleted column "{change.column}"</span>
                                </div>
                              )}
                            </div>
                          ))}
                          {version.changes.length > 3 && (
                            <div className="text-xs text-center text-muted-foreground pt-1">
                              +{version.changes.length - 3} more changes
                            </div>
                          )}
                        </div>
                      </CardContent>
                      <div className="px-5 py-3 bg-muted/30 flex justify-end">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => loadVersionData(currentDatasetId!, version.versionNumber)}
                        >
                          <Eye className="h-3.5 w-3.5 mr-1.5" />
                          View version
                        </Button>
                      </div>
                    </Card>
                  </div>
                ))}
              </div>
            )}
          </ScrollArea>
        </div>
        )}
      </DialogContent>
    </Dialog>
  );
};
