'use client'
import * as React from 'react'
import { 
  DndContext, 
  DragOverlay, 
  MouseSensor,
  TouchSensor,
  KeyboardSensor, 
  useSensor, 
  useSensors, 
  DragStartEvent, 
  DragOverEvent, 
  DragEndEvent,
  PointerSensor,
  rectIntersection
} from '@dnd-kit/core'
import { 
  SortableContext, 
  arrayMove, 
  sortableKeyboardCoordinates, 
  verticalListSortingStrategy 
} from '@dnd-kit/sortable'
import { format } from 'date-fns'
import { Plus, LayoutGrid, ListTodo, Search, Filter, SlidersHorizontal, CalendarClock, Layers, ChevronLeft, ChevronRight, PieChart, BarChart, Clock, Zap, GitBranch } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { type Column as ColumnType, Task, Priority, User } from "./types"
import { Column } from './column'
import { TaskCard } from './TaskCard'
import { toast } from "sonner"
import { useState } from 'react'
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardFooter, CardHeader } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { TaskList } from './TaskList'
import { SprintsView } from './SprintsView'

// Mock data
const users: User[] = [
    { id: 'user1', name: 'Alice', avatar: '/placeholder.svg?height=32&width=32' },
    { id: 'user2', name: 'Bob', avatar: '/placeholder.svg?height=32&width=32' },
    { id: 'user3', name: 'Charlie', avatar: '/placeholder.svg?height=32&width=32' },
]

const initialColumns: ColumnType[] = [
    {
        id: 'todo',
        title: 'To Do',
        tasks: [
            {
                id: 'task1',
                title: 'Implement login functionality',
                description: 'Create a secure login system with OAuth integration',
                priority: 'high',
                assignee: users[0],
                dueDate: new Date(2024, 5, 15),
                commentsCount: 3,
                createdAt: new Date(2024, 4, 10),
            },
            {
                id: 'task2',
                title: 'Design dashboard layout',
                description: 'Create responsive dashboard design',
                priority: 'medium',
                assignee: users[1],
                dueDate: new Date(2024, 5, 20),
                commentsCount: 2,
                createdAt: new Date(2024, 4, 12),
            },
        ],
    },
    {
        id: 'inprogress',
        title: 'In Progress',
        tasks: [
            {
                id: 'task3',
                title: 'API Integration',
                description: 'Integrate backend APIs with frontend',
                priority: 'high',
                assignee: users[2],
                dueDate: new Date(2024, 5, 18),
                commentsCount: 5,
                createdAt: new Date(2024, 4, 8),
            },
        ],
    },
    {
        id: 'done',
        title: 'Done',
        tasks: [
            {
                id: 'task4',
                title: 'Setup Project Structure',
                description: 'Initialize project and setup basic configuration',
                priority: 'medium',
                assignee: users[0],
                dueDate: new Date(2024, 5, 10),
                commentsCount: 1,
                createdAt: new Date(2024, 4, 5),
            },
        ],
    },
]

interface CreateTaskDialogProps {
    open: boolean
    onOpenChange: (open: boolean) => void
    onCreateTask: (task: Omit<Task, 'id' | 'createdAt'>) => void
    columnId: string
}

const CreateTaskDialog: React.FC<CreateTaskDialogProps> = ({ open, onOpenChange, onCreateTask, columnId }) => {
    const [title, setTitle] = React.useState('')
    const [description, setDescription] = React.useState('')
    const [priority, setPriority] = React.useState<Priority>('medium')
    const [assignee, setAssignee] = React.useState(users[0].id)
    const [dueDate, setDueDate] = React.useState(format(new Date(), 'yyyy-MM-dd'))

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault()
        onCreateTask({
            title,
            description,
            priority,
            assignee: users.find(user => user.id === assignee)!,
            dueDate: new Date(dueDate),
            commentsCount: 0,
        })
        onOpenChange(false)
        // Reset form
        setTitle('')
        setDescription('')
        setPriority('medium')
        setAssignee(users[0].id)
        setDueDate(format(new Date(), 'yyyy-MM-dd'))
    }

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent>
                <form onSubmit={handleSubmit}>
                    <DialogHeader>
                        <DialogTitle>Create New Task</DialogTitle>
                        <DialogDescription>Add a new task to your board</DialogDescription>
                    </DialogHeader>
                    <div className="grid gap-4 py-4">
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="title" className="text-right">Title</Label>
                            <Input
                                id="title"
                                value={title}
                                onChange={(e) => setTitle(e.target.value)}
                                className="col-span-3"
                                required
                            />
                        </div>
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="description" className="text-right">Description</Label>
                            <Textarea
                                id="description"
                                value={description}
                                onChange={(e) => setDescription(e.target.value)}
                                className="col-span-3"
                                required
                            />
                        </div>
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="priority" className="text-right">Priority</Label>
                            <Select value={priority} onValueChange={(value: Priority) => setPriority(value)}>
                                <SelectTrigger className="col-span-3">
                                    <SelectValue placeholder="Select priority" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="low">Low</SelectItem>
                                    <SelectItem value="medium">Medium</SelectItem>
                                    <SelectItem value="high">High</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="assignee" className="text-right">Assignee</Label>
                            <Select value={assignee} onValueChange={setAssignee}>
                                <SelectTrigger className="col-span-3">
                                    <SelectValue placeholder="Select assignee" />
                                </SelectTrigger>
                                <SelectContent>
                                    {users.map((user) => (
                                        <SelectItem key={user.id} value={user.id}>
                                            {user.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="dueDate" className="text-right">Due Date</Label>
                            <Input
                                id="dueDate"
                                type="date"
                                value={dueDate}
                                onChange={(e) => setDueDate(e.target.value)}
                                className="col-span-3"
                                required
                            />
                        </div>
                    </div>
                    <DialogFooter>
                        <Button type="submit">Create Task</Button>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
    )
}

export default function KanbanBoard() {
    const [columns, setColumns] = React.useState<ColumnType[]>(initialColumns)
    const [activeId, setActiveId] = React.useState<string | null>(null)
    const [activeTask, setActiveTask] = React.useState<Task | null>(null)
    const [viewMode, setViewMode] = useState<"kanban" | "list" | "sprints">("kanban")
    const [searchQuery, setSearchQuery] = useState("")
    const [filterPriority, setFilterPriority] = useState<Priority | "all">("all")
    
    // Move the sprint state inside the component
    const [isSprintDialogOpen, setIsSprintDialogOpen] = useState(false)
    const [activeSprint, setActiveSprint] = useState({
        name: "Sprint 1",
        startDate: new Date(),
        endDate: new Date(Date.now() + 12096e5), // Add 14 days
        status: "active" as "planning" | "active" | "completed"
    })

    // Add this state and dialog in the component:
    const [createGlobalTaskOpen, setCreateGlobalTaskOpen] = useState(false)
    const [selectedColumnForTask, setSelectedColumnForTask] = useState<string>(columns[0]?.id || '')

    // Add a Sprints structure for management
    const [sprints, setSprints] = useState<Array<{
        id: string;
        name: string;
        startDate: Date;
        endDate: Date;
        status: "planning" | "active" | "completed";
    }>>([
        {
            id: "sprint-1",
            name: activeSprint.name,
            startDate: activeSprint.startDate,
            endDate: activeSprint.endDate,
            status: activeSprint.status
        }
    ])

    // Update the sensors for smoother drag and drop
    const sensors = useSensors(
        useSensor(PointerSensor, {
            // Use PointerSensor instead of MouseSensor for better cross-device compatibility
            activationConstraint: {
                distance: 8, // Increase distance threshold to differentiate between click and drag
                delay: 0,
            },
        }),
        useSensor(TouchSensor, {
            // Keep touch support
            activationConstraint: {
                delay: 150, // Small delay for touch to distinguish between scroll and drag
                tolerance: 5, // Small tolerance for jitter
            },
        }),
        useSensor(KeyboardSensor, {
            coordinateGetter: sortableKeyboardCoordinates,
        })
    )

    // Helper function to find the container a task belongs to
    const findContainer = (taskId: string) => {
        for (const column of columns) {
            const task = column.tasks.find(t => t.id === taskId);
            if (task) return column.id;
        }
        return null;
    };

    // Find task by ID across all columns
    const findTaskById = (taskId: string): { task: Task, columnId: string } | null => {
        for (const column of columns) {
            const task = column.tasks.find(t => t.id === taskId);
            if (task) return { task, columnId: column.id };
        }
        return null;
    };

    const handleDragStart = (event: DragStartEvent) => {
        const { active } = event;
        const taskId = active.id.toString();
        
        const taskInfo = findTaskById(taskId);
        if (taskInfo) {
            setActiveId(taskId);
            setActiveTask(taskInfo.task);
            document.body.classList.add('dragging');
        }
    }

    const handleDragOver = (event: DragOverEvent) => {
        const { active, over } = event;
        if (!over || !active) return;

        const activeId = active.id.toString();
        const overId = over.id.toString();

        // Get the source column of the active task
        const sourceColumnId = findContainer(activeId);
        if (!sourceColumnId) return;

        // Find if the over element is a column 
        const isOverColumn = columns.some(col => col.id === overId);
        
        // If over a column and it's not the same as source
        if (isOverColumn && overId !== sourceColumnId) {
            setColumns(prevColumns => {
                // Find the active task
                const sourceColumn = prevColumns.find(col => col.id === sourceColumnId);
                if (!sourceColumn) return prevColumns;
                
                // Get the task being dragged
                const task = sourceColumn.tasks.find(t => t.id === activeId);
                if (!task) return prevColumns;
                
                // Create new columns with task moved
                return prevColumns.map(col => {
                    // Remove from source column
                    if (col.id === sourceColumnId) {
                        return {
                            ...col,
                            tasks: col.tasks.filter(t => t.id !== activeId)
                        };
                    }
                    // Add to target column
                    if (col.id === overId) {
                        return {
                            ...col,
                            tasks: [...col.tasks, task]
                        };
                    }
                    return col;
                });
            });
            
            // Notify the user
            toast.success(`Task moved to ${columns.find(col => col.id === overId)?.title || 'column'}`);
        }
    };

    const handleDragEnd = (event: DragEndEvent) => {
        const { active, over } = event;
        
        // Reset state
        setActiveId(null);
        setActiveTask(null);
        document.body.classList.remove('dragging');
        
        if (!over) return;
        
        const activeId = active.id.toString();
        const overId = over.id.toString();
        
        // Find the columns
        const sourceColumnId = findContainer(activeId);
        
        // If no source column found, exit
        if (!sourceColumnId) return;
        
        // Check if over is a column
        const isOverColumn = columns.some(col => col.id === overId);
        
        if (isOverColumn) {
            // Handle dropping on a column
            // (This is already handled in dragOver, so we don't need to do anything here)
        } else {
            // Handle sorting tasks within a column
            const overTaskInfo = findTaskById(overId);
            
            if (overTaskInfo && overTaskInfo.columnId === sourceColumnId) {
                const sourceColumn = columns.find(col => col.id === sourceColumnId);
                if (!sourceColumn) return;
                
                const oldIndex = sourceColumn.tasks.findIndex(t => t.id === activeId);
                const newIndex = sourceColumn.tasks.findIndex(t => t.id === overId);
                
                if (oldIndex !== -1 && newIndex !== -1) {
                    setColumns(prev => {
                        return prev.map(col => {
                            if (col.id === sourceColumnId) {
                                const newTasks = [...col.tasks];
                                const [removed] = newTasks.splice(oldIndex, 1);
                                newTasks.splice(newIndex, 0, removed);
                                
                                return {
                                    ...col,
                                    tasks: newTasks
                                };
                            }
                            return col;
                        });
                    });
                }
            }
        }
    };

    const handleEditTask = (taskId: string, updates: Partial<Task>) => {
        setColumns(prevColumns => {
            return prevColumns.map(col => ({
                ...col,
                tasks: col.tasks.map(task => 
                    task.id === taskId ? { ...task, ...updates } : task
                )
            }));
        });
        toast.success("Task updated successfully");
    };

    const handleDeleteTask = (taskId: string) => {
        setColumns(prevColumns => {
            return prevColumns.map(col => ({
                ...col,
                tasks: col.tasks.filter(task => task.id !== taskId)
            }));
        });
        toast.success("Task deleted successfully");
    };

    const handleAddTask = (columnId: string, newTask: Task) => {
        setColumns(prev => prev.map(col => {
            if (col.id === columnId) {
                return {
                    ...col,
                    tasks: [...col.tasks, newTask]
                }
            }
            return col
        }));
        toast.success("Task created successfully");
    }

    // Add this function to create a new column
    const addNewColumn = () => {
        const newColumnId = `column-${Date.now()}`
        
        // Use Dialog instead of prompt for better UX
        const newColumnTitle = prompt("Enter column title:")
        if (!newColumnTitle) return
        
        const newColumn: ColumnType = {
            id: newColumnId,
            title: newColumnTitle,
            tasks: []
        }
        
        setColumns(prev => [...prev, newColumn])
        toast.success(`Column "${newColumnTitle}" created`)
    }

    // Add handler for deleting columns
    const handleDeleteColumn = (columnId: string) => {
        // Find all tasks in the column
        const column = columns.find(col => col.id === columnId);
        if (!column) return;
        
        // Remove the column
        setColumns(prev => prev.filter(col => col.id !== columnId));
        toast.success(`Column "${column.title}" deleted`);
    };

    // Create Sprint Dialog component
    const SprintDialog = () => {
        const [sprintName, setSprintName] = useState("");
        const [startDate, setStartDate] = useState(format(new Date(), 'yyyy-MM-dd'));
        const [endDate, setEndDate] = useState(format(new Date(Date.now() + 12096e5), 'yyyy-MM-dd'));
        
        const handleCreateSprint = () => {
            if (!sprintName) return;
            
            setActiveSprint({
                name: sprintName,
                startDate: new Date(startDate),
                endDate: new Date(endDate),
                status: "planning"
            });
            
            // Mark all "backlog" tasks that should go to the new sprint
            setColumns(prevColumns => {
                return prevColumns.map(col => ({
                    ...col,
                    tasks: col.tasks.map(task => {
                        if (task.sprint === "backlog") {
                            return { ...task, sprint: "current" };
                        }
                        return task;
                    })
                }));
            });
            
            toast.success(`Sprint "${sprintName}" created successfully`);
            setIsSprintDialogOpen(false);
        };
        
        return (
            <Dialog open={isSprintDialogOpen} onOpenChange={setIsSprintDialogOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Create New Sprint</DialogTitle>
                        <DialogDescription>Plan your next development cycle</DialogDescription>
                    </DialogHeader>
                    <div className="grid gap-4 py-4">
                        <div className="grid gap-2">
                            <Label htmlFor="sprintName">Sprint Name</Label>
                            <Input 
                                id="sprintName" 
                                value={sprintName} 
                                onChange={(e) => setSprintName(e.target.value)}
                                placeholder="e.g. Sprint 2"
                            />
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                            <div className="grid gap-2">
                                <Label htmlFor="startDate">Start Date</Label>
                                <Input
                                    id="startDate"
                                    type="date"
                                    value={startDate}
                                    onChange={(e) => setStartDate(e.target.value)}
                                />
                            </div>
                            <div className="grid gap-2">
                                <Label htmlFor="endDate">End Date</Label>
                                <Input
                                    id="endDate"
                                    type="date"
                                    value={endDate}
                                    onChange={(e) => setEndDate(e.target.value)}
                                />
                            </div>
                        </div>
                    </div>
                    <DialogFooter>
                        <Button variant="outline" onClick={() => setIsSprintDialogOpen(false)}>
                            Cancel
                        </Button>
                        <Button onClick={handleCreateSprint}>Create Sprint</Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        );
    };

    // Filtered tasks for list view
    const allTasks = columns.flatMap(column => 
        column.tasks.map(task => ({
            ...task,
            columnId: column.id,
            columnTitle: column.title
        }))
    )
    
    const filteredTasks = allTasks.filter(task => {
        const matchesSearch = searchQuery === "" || 
            task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
            task.description.toLowerCase().includes(searchQuery.toLowerCase())
            
        const matchesPriority = filterPriority === "all" || task.priority === filterPriority
        
        return matchesSearch && matchesPriority
    })

    // Add handlers for the sprints
    const handleCreateSprint = (sprintData: Omit<typeof sprints[0], 'id'>) => {
        const newSprint = {
            ...sprintData,
            id: `sprint-${Date.now()}`
        }
        
        setSprints(prev => [...prev, newSprint])
        
        // If the sprint is active, set it as the active sprint
        if (sprintData.status === "active") {
            setActiveSprint({
                name: sprintData.name,
                startDate: sprintData.startDate,
                endDate: sprintData.endDate,
                status: sprintData.status
            })
        }
    }

    const handleUpdateSprint = (id: string, updates: Partial<typeof sprints[0]>) => {
        setSprints(prev => 
            prev.map(sprint => 
                sprint.id === id ? { ...sprint, ...updates } : sprint
            )
        )
        
        // If updating the active sprint
        const updatedSprint = sprints.find(s => s.id === id)
        if (updatedSprint && updatedSprint.status === "active") {
            setActiveSprint({
                name: updates.name || updatedSprint.name,
                startDate: updates.startDate || updatedSprint.startDate,
                endDate: updates.endDate || updatedSprint.endDate,
                status: updates.status || updatedSprint.status
            })
        }
    }

    const handleDeleteSprint = (id: string) => {
        setSprints(prev => prev.filter(sprint => sprint.id !== id))
    }

    return (
        <div className="kanban-container">
            <style jsx global>{`
                .kanban-container {
                    --column-bg: hsl(var(--card));
                    --column-border: hsl(var(--border));
                    --task-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                    touch-action: none; /* Prevents default touch actions during drag */
                }
                
                body.dragging {
                    cursor: grabbing !important;
                }
                
                .task-card {
                    background-color: hsl(var(--card));
                    border: 1px solid hsl(var(--border));
                    border-radius: 0.75rem;
                    padding: 0.75rem;
                    margin-bottom: 0.75rem;
                    cursor: grab;
                    user-select: none;
                    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
                    transition: all 0.2s ease-in-out;
                    position: relative;
                    display: flex;
                    flex-direction: column;
                    min-height: 100px;
                }
                
                .task-card:hover {
                    transform: translateY(-3px);
                    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
                    border-color: hsl(var(--primary) / 0.3);
                    transition: all 0.2s ease-in-out;
                }
                
                .task-card.dragging {
                    opacity: 0.7;
                    transform: scale(1.02);
                    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
                    pointer-events: none;
                }
                
                .drop-indicator {
                    height: 2px;
                    margin: 4px 0;
                    background-color: hsl(var(--primary));
                    transition: height 0.2s;
                }
                
                .column-drag-over {
                    border: 2px dashed hsl(var(--primary));
                }
                
                .column-drag-over .column-content {
                    background-color: hsl(var(--accent) / 0.1);
                }
                
                .priority {
                    padding: 2px 8px;
                    border-radius: 4px;
                    font-size: 0.75rem;
                }
                
                .priority.high {
                    background-color: hsla(0, 100%, 50%, 0.15);
                    color: hsl(0, 100%, 45%);
                }
                
                .priority.medium {
                    background-color: hsla(40, 100%, 50%, 0.15);
                    color: hsl(40, 100%, 35%);
                }
                
                .priority.low {
                    background-color: hsla(200, 100%, 50%, 0.15);
                    color: hsl(200, 100%, 35%);
                }

                /* Ensure drag handles can be grabbed properly */
                [role="button"] {
                    touch-action: none;
                }

                /* Additional styles to ensure drag works on mobile */
                @media (max-width: 768px) {
                    .task-card {
                        margin-bottom: 0.5rem;
                        padding: 0.5rem;
                    }
                    
                    .task-card.dragging {
                        transform: scale(1.05);
                        opacity: 0.6;
                    }
                }

                /* Add these new styles */
                .task-list-item {
                    border-bottom: 1px solid hsl(var(--border));
                    transition: background-color 0.15s;
                }
                
                .task-list-item:hover {
                    background-color: hsl(var(--accent) / 0.1);
                }
                
                .priority-dot {
                    width: 10px;
                    height: 10px;
                    border-radius: 50%;
                    flex-shrink: 0;
                }
                
                .priority-dot.high {
                    background-color: hsl(0, 100%, 50%);
                }
                
                .priority-dot.medium {
                    background-color: hsl(40, 100%, 50%);
                }
                
                .priority-dot.low {
                    background-color: hsl(200, 100%, 50%);
                }
                
                .task-list-status {
                    font-size: 0.75rem;
                    padding: 0.25rem 0.5rem;
                    border-radius: 1rem;
                    background-color: hsl(var(--muted));
                    color: hsl(var(--muted-foreground));
                    white-space: nowrap;
                }
                
                .due-date {
                    font-size: 0.75rem;
                    color: hsl(var(--muted-foreground));
                }
                
                .due-date.overdue {
                    color: hsl(0, 100%, 45%);
                }

                /* Hide scrollbars but keep functionality */
                .scrollbar-hide {
                    -ms-overflow-style: none;  /* IE and Edge */
                    scrollbar-width: none;  /* Firefox */
                }
                
                .scrollbar-hide::-webkit-scrollbar {
                    display: none;  /* Chrome, Safari and Opera */
                }
                
                .kanban-scroll-container {
                    position: relative;
                }
                
                .kanban-scroll-button {
                    opacity: 0.8;
                    transition: opacity 0.15s, transform 0.15s;
                }
                
                .kanban-scroll-button:hover {
                    opacity: 1;
                    transform: translateY(-50%) scale(1.05);
                }
                
                /* Improved task card styles */
                .task-card {
                    background-color: hsl(var(--card));
                    border: 1px solid hsl(var(--border));
                    border-radius: 0.75rem;
                    padding: 0.75rem;
                    margin-bottom: 0.75rem;
                    cursor: grab;
                    user-select: none;
                    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
                    transition: all 0.2s ease-in-out;
                    position: relative;
                    display: flex;
                    flex-direction: column;
                    min-height: 100px;
                }
                
                /* Sprint/backlog tags */
                .task-tag {
                    display: inline-flex;
                    align-items: center;
                    font-size: 0.7rem;
                    padding: 0.15rem 0.4rem;
                    border-radius: 1rem;
                    margin-right: 0.5rem;
                    background-color: hsl(var(--muted));
                    color: hsl(var(--muted-foreground));
                }
                
                .task-tag svg {
                    margin-right: 0.15rem;
                }
                
                .task-tag.sprint {
                    background-color: hsl(var(--primary) / 0.15);
                    color: hsl(var(--primary));
                }
                
                .task-tag.backlog {
                    background-color: hsl(var(--secondary) / 0.15);
                    color: hsl(var(--secondary));
                }
                
                .task-tag.bug {
                    background-color: hsl(0, 100%, 50%, 0.15);
                    color: hsl(0, 100%, 45%);
                }
                
                .task-tag.feature {
                    background-color: hsl(120, 100%, 30%, 0.15);
                    color: hsl(120, 100%, 25%);
                }
                
                /* Project stats section */
                .project-stats {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
                    gap: 0.75rem;
                    margin-bottom: 1rem;
                }
                
                .stat-card {
                    background-color: hsl(var(--card));
                    border: 1px solid hsl(var(--border));
                    border-radius: 0.5rem;
                    padding: 0.75rem;
                    display: flex;
                    flex-direction: column;
                }
                
                .stat-card .value {
                    font-size: 1.5rem;
                    font-weight: bold;
                    margin-top: 0.25rem;
                }
                
                .stat-card .label {
                    font-size: 0.75rem;
                    color: hsl(var(--muted-foreground));
                }

                /* Base styles for the kanban board */
                .kanban-board-container {
                    height: calc(100vh - 320px);
                    min-height: 500px;
                    width: 100%;
                    position: relative;
                }
                
                .kanban-columns-wrapper {
                    height: 100%;
                    overflow-x: auto;
                    overflow-y: hidden;
                    scroll-behavior: smooth;
                    /* Hide scrollbars */
                    scrollbar-width: none; /* Firefox */
                    -ms-overflow-style: none; /* IE and Edge */
                    padding-bottom: 1rem;
                }
                
                /* Make each column's content area scrollable independently */
                .column-content {
                    scrollbar-width: thin;
                    -ms-overflow-style: none; /* IE and Edge */
                    overflow-y: auto;
                    max-height: 100%;
                }
                
                .column-content::-webkit-scrollbar {
                    width: 6px;
                }
                
                .column-content::-webkit-scrollbar-thumb {
                    background-color: hsl(var(--border));
                    border-radius: 3px;
                }
                
                .column-content::-webkit-scrollbar-track {
                    background-color: transparent;
                }
                
                .kanban-columns-container {
                    height: 100%;
                    padding: 0.5rem 0.25rem;
                }
                
                /* Improved card styles */
                .task-card {
                    background-color: hsl(var(--card));
                    border: 1px solid hsl(var(--border));
                    border-radius: 0.75rem;
                    padding: 0.75rem;
                    margin-bottom: 0.75rem;
                    cursor: grab;
                    user-select: none;
                    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
                    transition: all 0.2s ease-in-out;
                    position: relative;
                    display: flex;
                    flex-direction: column;
                    min-height: 100px;
                }
                
                .task-card:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
                    border-color: hsl(var(--border));
                }
                
                .task-card.dragging {
                    opacity: 0.4;
                    transform: scale(1.03);
                    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
                }
                
                .dragging-overlay {
                    transform: rotate(1deg) !important;
                    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15) !important;
                    background-color: hsl(var(--card)) !important;
                    opacity: 0.95 !important;
                }
                
                /* Column styles */
                .column-drag-over {
                    border: 2px dashed hsl(var(--primary)) !important;
                }
                
                .pulse-animation {
                    animation: pulse 1.5s infinite;
                }
                
                @keyframes pulse {
                    0% {
                        box-shadow: 0 0 0 0 rgba(var(--primary-rgb), 0.4);
                    }
                    70% {
                        box-shadow: 0 0 0 6px rgba(var(--primary-rgb), 0);
                    }
                    100% {
                        box-shadow: 0 0 0 0 rgba(var(--primary-rgb), 0);
                    }
                }
                
                /* Clean tags */
                .task-tag {
                    display: inline-flex;
                    align-items: center;
                    font-size: 0.65rem;
                    padding: 0.15rem 0.5rem;
                    border-radius: 1rem;
                    margin-right: 0.5rem;
                    background-color: hsl(var(--muted));
                    color: hsl(var(--muted-foreground));
                }
                
                .task-tag.sprint {
                    background-color: hsl(var(--primary) / 0.15);
                    color: hsl(var(--primary));
                }
                
                .task-tag.backlog {
                    background-color: hsl(var(--secondary) / 0.15);
                    color: hsl(var(--secondary));
                }
                
                .task-tag.bug {
                    background-color: hsl(0, 100%, 50%, 0.15);
                    color: hsl(0, 100%, 45%);
                }
                
                .task-tag.feature {
                    background-color: hsl(120, 100%, 30%, 0.15);
                    color: hsl(120, 100%, 25%);
                }
                
                /* Better priority indicators */
                .priority {
                    padding: 2px 8px;
                    border-radius: 4px;
                    font-size: 0.7rem;
                    font-weight: 500;
                }
                
                .priority.high {
                    background-color: hsla(0, 90%, 65%, 0.15);
                    color: hsl(0, 90%, 45%);
                }
                
                .priority.medium {
                    background-color: hsla(40, 90%, 60%, 0.15);
                    color: hsl(40, 90%, 40%);
                }
                
                .priority.low {
                    background-color: hsla(200, 90%, 60%, 0.15);
                    color: hsl(200, 90%, 40%);
                }

                /* Add this to your CSS to isolate drag handles */
                .task-card {
                    /* Existing styles... */
                    position: relative;
                }

                /* Prevent drag conflicts with editing */
                .task-card > [data-no-dnd="true"] {
                    pointer-events: all !important;
                    position: relative;
                    z-index: 5;
                }

                /* Make sure the inner clickable area stays on top */
                .task-card .absolute {
                    z-index: 10;
                }

                /* Add these to improve task card interaction */
                .task-card [data-no-dnd] {
                    pointer-events: auto !important;
                    z-index: 10;
                    cursor: pointer;
                }

                .task-card:active {
                    cursor: grabbing !important;
                }

                /* Add more padding to edit/delete buttons to make them easier to click */
                .task-card .h-7 {
                    padding: 4px !important;
                    margin-left: 2px;
                }

                /* Make buttons in task card stand out more on hover */
                .task-card button:hover {
                    background-color: hsl(var(--muted));
                    transform: scale(1.1);
                }

                /* Make sure drag and drop works properly */
                .task-card {
                    cursor: grab !important;
                    touch-action: none !important;
                }

                .task-card:active {
                    cursor: grabbing !important;
                }

                /* Increase the drag handle area */
                .task-card {
                    /* Existing styles... */
                    padding: 1rem;
                    margin-bottom: 1rem;
                }

                /* Add animation for dragging state */
                .task-card.dragging {
                    opacity: 0.5;
                    transform: scale(1.03);
                    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
                    pointer-events: none;
                }

                /* Add rotation to drag overlay */
                .dragging-overlay {
                    transform: rotate(2deg) !important;
                    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15) !important;
                    background-color: hsl(var(--card)) !important;
                    opacity: 0.95 !important;
                }
            `}</style>

            <div className="mb-6 space-y-4">
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                    <h1 className="text-2xl font-bold tracking-tight">Project Management</h1>
                    
                    <div className="flex items-center gap-2">
                        <Tabs defaultValue={viewMode} onValueChange={(value) => setViewMode(value as "kanban" | "list" | "sprints")}>
                            <TabsList className="grid w-[250px] grid-cols-3">
                                <TabsTrigger value="kanban">
                                    <LayoutGrid className="h-4 w-4 mr-2" />
                                    Kanban
                                </TabsTrigger>
                                <TabsTrigger value="list">
                                    <ListTodo className="h-4 w-4 mr-2" />
                                    List
                                </TabsTrigger>
                                <TabsTrigger value="sprints">
                                    <GitBranch className="h-4 w-4 mr-2" />
                                    Sprints
                                </TabsTrigger>
                            </TabsList>
                        </Tabs>
                        
                        <div className="flex items-center gap-1.5">
                            <Button variant="outline" size="sm" onClick={addNewColumn}>
                                <Plus className="h-4 w-4 mr-1" />
                                Column
                            </Button>
                            <Button variant="default" size="sm" onClick={() => setCreateGlobalTaskOpen(true)}>
                                <Plus className="h-4 w-4 mr-1" />
                                Task
                            </Button>
                        </div>
                    </div>
                </div>
                
                <div className="flex flex-col sm:flex-row gap-2 items-start sm:items-center">
                    <div className="relative w-full sm:w-64">
                        <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                        <Input
                            placeholder="Search tasks..."
                            className="pl-8"
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                        />
                    </div>
                    
                    <Select 
                        value={filterPriority}
                        onValueChange={(value) => setFilterPriority(value as Priority | "all")}
                    >
                        <SelectTrigger className="w-full sm:w-auto">
                            <Filter className="h-4 w-4 mr-2" />
                            <SelectValue placeholder="Filter by Priority" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="all">All Priorities</SelectItem>
                            <SelectItem value="high">High Priority</SelectItem>
                            <SelectItem value="medium">Medium Priority</SelectItem>
                            <SelectItem value="low">Low Priority</SelectItem>
                        </SelectContent>
                    </Select>
                </div>
            </div>

            {/* Project Management Dashboard */}
            <div className="mb-6">
                <div className="project-stats">
                    <div className="stat-card">
                        <div className="flex justify-between items-center">
                            <div className="label">Open Tasks</div>
                            <PieChart className="h-4 w-4 text-muted-foreground" />
                        </div>
                        <div className="value">{allTasks.length}</div>
                    </div>
                    
                    <div className="stat-card">
                        <div className="flex justify-between items-center">
                            <div className="label">In Progress</div>
                            <BarChart className="h-4 w-4 text-muted-foreground" />
                        </div>
                        <div className="value">
                            {columns.find(col => col.id === 'inprogress')?.tasks.length || 0}
                        </div>
                    </div>
                    
                    <div className="stat-card">
                        <div className="flex justify-between items-center">
                            <div className="label">Completed</div>
                            <Zap className="h-4 w-4 text-muted-foreground" />
                        </div>
                        <div className="value">
                            {columns.find(col => col.id === 'done')?.tasks.length || 0}
                        </div>
                    </div>
                    
                    <div className="stat-card">
                        <div className="flex justify-between items-center">
                            <div className="label">Active Sprint</div>
                            <GitBranch className="h-4 w-4 text-muted-foreground" />
                        </div>
                        <div className="value flex items-center text-base">
                            <span className="text-primary">{activeSprint.name}</span>
                            <span className="text-xs text-muted-foreground ml-1">
                                ({Math.max(0, Math.ceil((activeSprint.endDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)))}d left)
                            </span>
                        </div>
                    </div>
                </div>
                
                <div className="flex justify-between gap-2 mb-2">
                    <div className="flex gap-1.5">
                        <Button 
                            variant="secondary" 
                            size="sm" 
                            onClick={() => {
                                setSearchQuery("");
                                toast.info("Showing current sprint tasks");
                            }}
                        >
                            <GitBranch className="h-4 w-4 mr-1" />
                            Current Sprint
                        </Button>
                        <Button 
                            variant="secondary" 
                            size="sm"
                            onClick={() => {
                                setSearchQuery("");
                                toast.info("Showing backlog tasks");
                            }}
                        >
                            <Clock className="h-4 w-4 mr-1" />
                            Backlog
                        </Button>
                    </div>
                    
                    <div className="flex gap-1.5">
                        <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => setViewMode("sprints")}
                        >
                            <GitBranch className="h-4 w-4 mr-1" />
                            Manage Sprints
                        </Button>
                        <Button 
                            variant="default" 
                            size="sm"
                            onClick={() => setIsSprintDialogOpen(true)}
                        >
                            <Plus className="h-4 w-4 mr-1" />
                            New Sprint
                        </Button>
                    </div>
                </div>
            </div>

            {viewMode === "sprints" ? (
                <SprintsView 
                    sprints={sprints}
                    onCreateSprint={handleCreateSprint}
                    onUpdateSprint={handleUpdateSprint}
                    onDeleteSprint={handleDeleteSprint}
                    onBack={() => setViewMode("kanban")}
                />
            ) : viewMode === "kanban" ? (
                <DndContext
                    sensors={sensors}
                    collisionDetection={rectIntersection}
                    onDragStart={handleDragStart}
                    onDragOver={handleDragOver}
                    onDragEnd={handleDragEnd}
                >
                    <div className="kanban-board-container">
                        <div className="kanban-columns-wrapper overflow-x-auto">
                            <div className="kanban-columns-container flex space-x-4 min-w-max">
                                {columns.map((column) => {
                                    // Filter tasks based on search and priority filter
                                    const filteredColumnTasks = column.tasks.filter(task => {
                                        const matchesSearch = searchQuery === "" || 
                                            task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                                            task.description.toLowerCase().includes(searchQuery.toLowerCase());
                                            
                                        const matchesPriority = filterPriority === "all" || task.priority === filterPriority;
                                        
                                        return matchesSearch && matchesPriority;
                                    });
                                    
                                    return (
                                        <div 
                                            key={column.id} 
                                            className="column-wrapper w-[350px]"
                                        >
                                            <Column
                                                column={{...column, tasks: filteredColumnTasks}}
                                                tasks={filteredColumnTasks}
                                                onAddTask={handleAddTask}
                                                onEditTask={handleEditTask}
                                                onDeleteTask={handleDeleteTask}
                                                onDeleteColumn={handleDeleteColumn}
                                            />
                                        </div>
                                    );
                                })}
                                
                                <div className="inline-flex items-start h-full p-3">
                                    <Button
                                        variant="outline"
                                        className="border-dashed border-2 h-12"
                                        onClick={addNewColumn}
                                    >
                                        <Plus className="h-5 w-5 mr-2" /> 
                                        Add Column
                                    </Button>
                                </div>
                            </div>
                        </div>
                        
                        <DragOverlay>
                            {activeTask && (
                                <div className="task-card dragging-overlay w-[330px] shadow-xl scale-105">
                                    <div className="font-medium">{activeTask.title}</div>
                                    <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                                        {activeTask.description}
                                    </p>
                                    <div className="mt-2 flex items-center justify-between">
                                        <span className={`priority ${activeTask.priority}`}>
                                            {activeTask.priority.charAt(0).toUpperCase() + activeTask.priority.slice(1)}
                                        </span>
                                    </div>
                                </div>
                            )}
                        </DragOverlay>
                    </div>
                </DndContext>
            ) : (
                <TaskList 
                    allTasks={allTasks}
                    filteredTasks={filteredTasks}
                    handleEditTask={handleEditTask}
                    handleDeleteTask={handleDeleteTask}
                    setViewMode={setViewMode}
                    filterPriority={filterPriority}
                    users={users}
                />
            )}

            {/* Render the Sprint Dialog */}
            <SprintDialog />

            {/* Global Create Task Dialog */}
            <Dialog open={createGlobalTaskOpen} onOpenChange={setCreateGlobalTaskOpen}>
                <DialogContent>
                    <form onSubmit={(e) => {
                        e.preventDefault();
                        if (!selectedColumnForTask) return;
                        
                        // Correctly access form elements
                        const formData = new FormData(e.currentTarget);
                        const title = formData.get('title') as string;
                        const description = formData.get('description') as string;
                        const priority = formData.get('priority') as Priority;
                        const assigneeId = formData.get('assignee') as string;
                        const dueDateStr = formData.get('dueDate') as string;
                        const sprintValue = formData.get('sprint') as string;
                        const typeValue = formData.get('type') as string;
                        
                        // Fix type issues with proper type assertion
                        const newTask: Task = {
                            id: `task-${Date.now()}`,
                            title,
                            description,
                            priority,
                            assignee: users.find(user => user.id === assigneeId)!,
                            dueDate: new Date(dueDateStr),
                            createdAt: new Date(),
                            commentsCount: 0,
                            // Type assertions to match the expected type
                            sprint: sprintValue !== 'none' ? 
                              (sprintValue as "current" | "backlog" | "next") : undefined,
                            type: typeValue !== 'task' ? 
                              (typeValue as "bug" | "feature") : undefined
                        };
                        
                        handleAddTask(selectedColumnForTask, newTask);
                        setCreateGlobalTaskOpen(false);
                    }}>
                        <DialogHeader>
                            <DialogTitle>Create New Task</DialogTitle>
                            <DialogDescription>Add a new task to your board</DialogDescription>
                        </DialogHeader>
                        <div className="grid gap-4 py-4">
                            <div className="grid gap-2">
                                <Label htmlFor="columnId">Column</Label>
                                <Select 
                                    value={selectedColumnForTask} 
                                    onValueChange={setSelectedColumnForTask}
                                    name="columnId"
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select column" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {columns.map(column => (
                                            <SelectItem key={column.id} value={column.id}>
                                                {column.title}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                            
                            <div className="grid gap-2">
                                <Label htmlFor="title">Title</Label>
                                <Input id="title" name="title" required />
                            </div>
                            
                            <div className="grid gap-2">
                                <Label htmlFor="description">Description</Label>
                                <Textarea id="description" name="description" required />
                            </div>
                            
                            <div className="grid grid-cols-2 gap-4">
                                <div className="grid gap-2">
                                    <Label htmlFor="priority">Priority</Label>
                                    <Select defaultValue="medium" name="priority">
                                        <SelectTrigger id="priority">
                                            <SelectValue placeholder="Select priority" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="low">Low</SelectItem>
                                            <SelectItem value="medium">Medium</SelectItem>
                                            <SelectItem value="high">High</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                                
                                <div className="grid gap-2">
                                    <Label htmlFor="type">Type</Label>
                                    <Select defaultValue="task" name="type">
                                        <SelectTrigger id="type">
                                            <SelectValue placeholder="Select type" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="task">Task</SelectItem>
                                            <SelectItem value="feature">Feature</SelectItem>
                                            <SelectItem value="bug">Bug</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                            </div>
                            
                            <div className="grid grid-cols-2 gap-4">
                                <div className="grid gap-2">
                                    <Label htmlFor="sprint">Sprint</Label>
                                    <Select defaultValue="none" name="sprint">
                                        <SelectTrigger id="sprint">
                                            <SelectValue placeholder="Select sprint" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="none">None</SelectItem>
                                            <SelectItem value="current">Current Sprint</SelectItem>
                                            <SelectItem value="backlog">Backlog</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                                
                                <div className="grid gap-2">
                                    <Label htmlFor="assignee">Assignee</Label>
                                    <Select defaultValue={users[0].id} name="assignee">
                                        <SelectTrigger id="assignee">
                                            <SelectValue placeholder="Select assignee" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {users.map(user => (
                                                <SelectItem key={user.id} value={user.id}>{user.name}</SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>
                            </div>
                            
                            <div className="grid gap-2">
                                <Label htmlFor="dueDate">Due Date</Label>
                                <Input
                                    id="dueDate"
                                    type="date"
                                    name="dueDate"
                                    defaultValue={format(new Date(Date.now() + 604800000), 'yyyy-MM-dd')}
                                    required
                                />
                            </div>
                        </div>
                        <DialogFooter>
                            <Button type="submit">Create Task</Button>
                        </DialogFooter>
                    </form>
                </DialogContent>
            </Dialog>
        </div>
    );
}