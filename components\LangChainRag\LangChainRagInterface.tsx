"use client"

import React, { useEffect } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { InfoIcon, Database, MessageSquare, Settings, FileSpreadsheet } from 'lucide-react';
import { useLangChainRag } from '@/hooks/useLangChainRag';
import DatasetSelector from './DatasetSelector';
import PDFSelector from './PDFSelector';
import ChatInterface from './ChatInterface';
import ModelSelector from './ModelSelector';
import { toast } from 'sonner';

const LangChainRagInterface: React.FC = () => {
  const {
    selectedDatasets,
    selectedPDFs,
    isLoading,
    isEmbedding,
    error,
    chatMessages,
    selectedModel,
    pineconeStatus,
    sourceDocuments,
    useDeepResearch,
    maxIterations,
    currentIterations,
    searchStrategy,
    retrievalK,
    setSelectedDatasets,
    setSelectedPDFs,
    setSelectedModel,
    setUseDeepResearch,
    setMaxIterations,
    setSearchStrategy,
    setRetrievalK,
    embedDataset,
    embedPDF,
    deleteEmbedding,
    clearChat,
    sendMessage,
    checkPineconeStatus,
    fetchDatasets,
    fetchPDFs
  } = useLangChainRag();

  // Check for errors and display toast
  useEffect(() => {
    if (error) {
      toast.error(error);
    }
  }, [error]);

  // Periodically check Pinecone status
  useEffect(() => {
    const interval = setInterval(() => {
      if (pineconeStatus.isInitialized) {
        checkPineconeStatus();
      }
    }, 60000); // Check every minute

    return () => clearInterval(interval);
  }, [pineconeStatus.isInitialized]);

  return (
    <div className="container mx-auto p-4">
      <Card className="w-full">
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>LangChain RAG Chat</CardTitle>
              <CardDescription>
                Chat with your datasets using LangChain and Pinecone vector database
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Badge
                variant={pineconeStatus.isInitialized ? "default" : "outline"}
                className={pineconeStatus.isInitialized ? "bg-green-600" : ""}
              >
                <Database className="h-3 w-3 mr-1" />
                {pineconeStatus.isInitialized
                  ? `Pinecone: ${pineconeStatus.recordCount} records`
                  : "Pinecone: Not initialized"}
              </Badge>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <Tabs defaultValue="chat" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="chat">
                <MessageSquare className="h-4 w-4 mr-2" />
                Chat
              </TabsTrigger>
              <TabsTrigger value="settings">
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </TabsTrigger>
              <TabsTrigger value="info">
                <InfoIcon className="h-4 w-4 mr-2" />
                Info
              </TabsTrigger>
            </TabsList>

            <TabsContent value="chat" className="mt-4">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="md:col-span-1">
                  <div className="space-y-3">
                    {/* Compact Dataset Selector */}
                    <div className="border rounded-lg p-2">
                      <DatasetSelector
                        selectedDatasets={selectedDatasets}
                        onSelectDatasets={setSelectedDatasets}
                        onEmbedDataset={embedDataset}
                        onDeleteEmbedding={(datasetId) => deleteEmbedding('dataset', datasetId)}
                        isEmbedding={isEmbedding}
                      />
                    </div>

                    {/* Compact PDF Selector */}
                    <div className="border rounded-lg p-2">
                      <PDFSelector
                        selectedPDFs={selectedPDFs}
                        onSelectPDFs={setSelectedPDFs}
                        onEmbedPDF={embedPDF}
                        onDeleteEmbedding={(pdfId) => deleteEmbedding('pdf', pdfId)}
                        isEmbedding={isEmbedding}
                      />
                    </div>

                    {/* Compact Model Selection */}
                    <div className="border rounded-lg p-2">
                      <div className="flex items-center gap-2 mb-2">
                        <Settings className="h-4 w-4 text-muted-foreground" />
                        <h3 className="text-sm font-medium">Model</h3>
                      </div>
                      <ModelSelector
                        value={selectedModel}
                        onChange={setSelectedModel}
                        disabled={isLoading}
                      />
                    </div>

                    {/* Compact Deep Research Controls */}
                    <div className="border rounded-lg p-2">
                      <div className="flex items-center gap-2 mb-2">
                        <Database className="h-4 w-4 text-muted-foreground" />
                        <h3 className="text-sm font-medium">Deep Research</h3>
                      </div>
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <input
                            type="checkbox"
                            id="deep-research-toggle"
                            checked={useDeepResearch}
                            onChange={(e) => setUseDeepResearch(e.target.checked)}
                            className="rounded border-gray-300 text-primary focus:ring-primary"
                          />
                          <label htmlFor="deep-research-toggle" className="text-xs">
                            Enable
                          </label>
                        </div>
                        {useDeepResearch && (
                          <div>
                            <label className="text-xs block mb-1">
                              Max Iterations: {maxIterations}
                            </label>
                            <input
                              type="range"
                              min="1"
                              max="5"
                              value={maxIterations}
                              onChange={(e) => setMaxIterations(parseInt(e.target.value))}
                              className="w-full"
                            />
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="md:col-span-3 h-[700px]">
                  <div className="flex flex-col h-full">
                    {/* Minimal Status Indicator */}
                    {(selectedDatasets.length > 0 || selectedPDFs.length > 0) && (
                      <div className="flex gap-2 mb-2 text-xs text-muted-foreground">
                        {selectedDatasets.length > 0 && (
                          <Badge variant="outline">{selectedDatasets.length} Datasets</Badge>
                        )}
                        {selectedPDFs.length > 0 && (
                          <Badge variant="outline">{selectedPDFs.length} PDFs</Badge>
                        )}
                        <Badge variant={pineconeStatus.isInitialized ? "default" : "outline"}>
                          {pineconeStatus.recordCount} Records
                        </Badge>
                      </div>
                    )}

                    <ChatInterface
                      messages={chatMessages}
                      onSendMessage={(message) => sendMessage(message)}
                      onClearChat={clearChat}
                      isLoading={isLoading}
                      disabled={selectedDatasets.length === 0 && selectedPDFs.length === 0}
                      sourceDocuments={sourceDocuments}
                      useDeepResearch={useDeepResearch}
                      deepResearchIterations={currentIterations}
                    />
                  </div>
                </div>
              </div>
            </TabsContent>



            <TabsContent value="settings" className="mt-4 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-4">
                  <Card>
                    <CardHeader className="py-3">
                      <CardTitle className="text-lg">RAG Settings</CardTitle>
                      <CardDescription>Configure retrieval parameters</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="grid gap-4">
                        <div>
                          <label htmlFor="retrievalK" className="text-sm font-medium">
                            Retrieval K (1-30)
                            <span className="text-xs text-muted-foreground ml-2">
                              Number of documents to retrieve
                            </span>
                          </label>
                          <input 
                            id="retrievalK" 
                            type="range" 
                            min="1" 
                            max="30" 
                            step="1" 
                            value={retrievalK} 
                            onChange={e => setRetrievalK(Number(e.target.value))}
                            className="w-full" 
                          />
                          <div className="flex justify-between text-xs text-muted-foreground">
                            <span>1 (More precise)</span>
                            <span className="font-medium">{retrievalK}</span>
                            <span>30 (More recall)</span>
                          </div>
                        </div>

                        <div>
                          <label className="text-sm font-medium mb-1 block">
                            Search Strategy
                          </label>
                          <select 
                            value={searchStrategy} 
                            // @ts-ignore
                            onChange={e => setSearchStrategy(e.target.value)}
                            className="w-full px-3 py-2 border rounded-md text-sm"
                          >
                            <option value="hybrid">Hybrid (Vector + Keyword)</option>
                            <option value="vector">Vector Only</option>
                            <option value="keyword">Keyword Only</option>
                          </select>
                          <p className="text-xs text-muted-foreground mt-1">
                            Hybrid search combines semantic and keyword matching for better results
                          </p>
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <label className="text-sm font-medium">Deep Research Mode</label>
                            <p className="text-xs text-muted-foreground">
                              Recursively analyze complex queries
                            </p>
                          </div>
                          <input 
                            type="checkbox" 
                            checked={useDeepResearch}
                            onChange={e => setUseDeepResearch(e.target.checked)}
                            className="h-4 w-4"
                          />
                        </div>

                        {useDeepResearch && (
                          <div>
                            <label htmlFor="maxIterations" className="text-sm font-medium">
                              Max Iterations (1-5)
                            </label>
                            <input 
                              id="maxIterations" 
                              type="range" 
                              min="1" 
                              max="5" 
                              step="1" 
                              value={maxIterations} 
                              onChange={e => setMaxIterations(Number(e.target.value))}
                              className="w-full" 
                            />
                            <div className="flex justify-between text-xs text-muted-foreground">
                              <span>1</span>
                              <span className="font-medium">{maxIterations}</span>
                              <span>5</span>
                            </div>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <div className="space-y-4">
                  <Alert>
                    <InfoIcon className="h-4 w-4" />
                    <AlertTitle>Vector Database Settings</AlertTitle>
                    <AlertDescription>
                      <div className="mt-2 space-y-2">
                        <p>
                          <span className="font-medium">Status:</span>{' '}
                          {pineconeStatus.isInitialized ? 'Connected' : 'Not initialized'}
                        </p>
                        {pineconeStatus.isInitialized && (
                          <>
                            <p>
                              <span className="font-medium">Records:</span> {pineconeStatus.recordCount}
                            </p>
                            <p>
                              <span className="font-medium">Namespace:</span> {pineconeStatus.namespace}
                            </p>
                          </>
                        )}
                      </div>
                    </AlertDescription>
                  </Alert>

                  <Alert>
                    <InfoIcon className="h-4 w-4" />
                    <AlertTitle>Embedding Settings</AlertTitle>
                    <AlertDescription>
                      <p className="mt-2">
                        Datasets are embedded using Cohere's <code>embed-english-v3.0</code> model for optimal semantic search.
                      </p>
                    </AlertDescription>
                  </Alert>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="info" className="mt-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>How It Works</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ol className="list-decimal pl-5 space-y-2 text-muted-foreground">
                      <li>
                        <strong>Select a dataset or PDF</strong> from your available files
                      </li>
                      <li>
                        <strong>Embed the content</strong> into Pinecone vector database
                      </li>
                      <li>
                        <strong>Ask questions</strong> about your data in natural language
                      </li>
                      <li>
                        <strong>Get insights</strong> powered by LangChain and LLMs with source references
                      </li>
                    </ol>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Benefits</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="list-disc pl-5 space-y-2 text-muted-foreground">
                      <li>Query your data using natural language</li>
                      <li>Support for both CSV datasets and PDF documents</li>
                      <li>Get AI-powered insights with source references</li>
                      <li>Semantic search capabilities</li>
                      <li>Support for multiple LLM providers</li>
                      <li>Persistent vector storage for fast retrieval</li>
                    </ul>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>

        <CardFooter className="flex justify-between text-sm text-muted-foreground">
          <div>Powered by LangChain.js, Pinecone, and AI models</div>
          <div>
            {selectedModel && (
              <span>
                Using: {selectedModel}
              </span>
            )}
          </div>
        </CardFooter>
      </Card>
    </div>
  );
};

export default LangChainRagInterface;
